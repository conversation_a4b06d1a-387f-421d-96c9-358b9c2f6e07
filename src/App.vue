<template>
  <div id="app">
    <keep-alive :include="cachedViews">
      <router-view :key="key" v-if="$route.meta.keepAlive && isLogin"></router-view>
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive && isLogin"></router-view>
    <!-- 底部导航 -->
    <!-- <div class="bottom" v-if="isShow">
      <van-grid square :border="false">
        <van-grid-item
          v-for="list in bottomItem"
          :icon="list.icon"
          :text="list.menuName"
          :url="list.menuHref"
          :key="list.menuId"
        />
      </van-grid>
    </div> -->
  </div>
</template>

<script>
export default {
  name: "App",
  data() {
    return {
      isShow: true,
      itemsArr: [],
      bottomItem: [],
      isLogin: false,
    };
  },
  computed: {
    cachedViews() {
      return this.$store.state.cachedViews.cachedViews;
    },
    key() {
      return this.$route.path;
    },
  },
  watch: {
    $route: {
      immediate: true,
      handler(route) {
        const {
          name,
          meta: { keepAlive },
        } = route;
        if (name && keepAlive) {
          this.$store.commit("cachedViews/ADD_CACHED_VIEW", route);
        }
      },
    },
  },
  created: function () {
    const timer = setInterval(() => {
      console.log("timer");
      const loginInfo = localStorage.getItem("loginInfo");
      if (loginInfo) {
        clearInterval(timer);
        this.isLogin = true;
      }
    }, 5);
  },
  mounted() {
    console.log("111", this.$store);
  },
  methods: {},
};
</script>

<style lang="scss"></style>
