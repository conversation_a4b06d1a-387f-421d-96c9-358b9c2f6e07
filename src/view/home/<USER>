<template>
  <div></div>
</template>

<script>
export default {
  data() {
    return {
      getData: false,
      otimer: null,
    };
  },
  created() {
    this.otimer = setInterval(() => {
      let appLoginInfo = JSON.parse(localStorage.getItem("loginInfo"));
      if (appLoginInfo.id) {
        clearInterval(this.otimer);
        console.log("app登录信息index", appLoginInfo);
        let params = {
          userCode: appLoginInfo.id,
        };
        /* 
    医院后勤管理(manager)、物业工人(teamLeader)、班组组长( teamWork)
    */
        this.axios.postContralHostBase("getRole", params, (res) => {
          console.log("role", res.data);
          localStorage.setItem("role", JSON.stringify(res.data.roleType));
          if (res.data.roleType == "manager") {
            this.$router.push({
              path: "/manageHome",
            });
          } else {
            this.$router.push({
              path: "/workerHome",
            });
          }
        });
        let menuParams = {
          loginInfo: JSON.stringify(appLoginInfo),
          moduleCode: "icmsPlan",
          sysCode: "app",
        };
        console.log("menuParams", menuParams);
        this.axios.postContralHostBase("getMenuList", menuParams, (res) => {
          if(res.code=='200') {
            localStorage.setItem('appMenuList',JSON.stringify(res.data.menuList))
          }
        });
      }
    }, 300);
    console.log("嘿嘿");
  },
  methods: {},
};
</script>