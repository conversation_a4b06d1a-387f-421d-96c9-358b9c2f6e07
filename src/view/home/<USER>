<template>
  <div class="container">
    <Header title="巡检服务" @backFun="goBack"></Header>
    <van-notify v-model="show" background="black" type="success"
      >定位初始化中,请稍后，请检查是否开启蓝牙</van-notify
    >
    <div class="scan-box">
      <img src="../../assets/images/bigScan.png" />
      <van-button
        round
        type="info"
        color="#29BEBC"
        style="width: 163px; fontsize: 18px"
        @click="APPScan"
        >扫码</van-button
      >
      <!-- <van-button
        round
        type="info"
        color="#29BEBC"
        style="width: 163px; fontsize: 18px; margin-top: 12px"
        @click="goContent"
        >扫码(调试用)</van-button
      > -->
    </div>
    <div class="service">
      <span>巡检服务</span>
      <div class="item-box">
        <div class="item" v-show="showTasks">
          <van-badge :content="taskNum">
            <img
              src="../../assets/images/<EMAIL>"
              @click="goTask"
              style="margin-bottom: 3px"
            />
          </van-badge>
          <span>巡检任务</span>
        </div>
        <div class="item" v-show="showFeedback">
          <img src="../../assets/images/<EMAIL>" @click="openPopup" />
          <span>问题反馈</span>
        </div>
        <div class="item" v-show="showFeedbackList">
          <img
            src="../../assets/images/<EMAIL>"
            @click="goFeedbackList"
          />
          <span>反馈记录</span>
        </div>
        <div class="item" v-show="showSignIn">
          <img src="../../assets/images/<EMAIL>" @click="APPScan('sign')" />
          <span>扫码签到</span>
        </div>
        <div class="item" v-show="showInspectionRecord">
          <img
            src="../../assets/images/<EMAIL>"
            @click="goInspectionRecord"
          />
          <span>巡检记录</span>
        </div>
        <div class="item" v-show="showAnalysis">
          <img
            src="../../assets/images/<EMAIL>"
            @click="goAnalysis"
          />
          <span>统计分析</span>
        </div>
      </div>
    </div>
    <drag-ball class="dragBall" @click="clickBall">
      <div slot="value">报修</div>
    </drag-ball>
    <van-popup v-model="bottomShow" round position="bottom">
      <van-cell to="feedback?type=1">
        <template>
          <span class="popup-item">整改</span>
        </template>
      </van-cell>
      <van-cell to="feedback?type=2">
        <template>
          <span class="popup-item">自修</span>
        </template>
      </van-cell>
      <van-cell to="feedback?type=3">
        <template>
          <span class="popup-item">报修</span>
        </template>
      </van-cell>
      <van-cell to="" class="cell-cancel" @click="bottomShow = false">
        <template>
          <span class="popup-cancel">取消</span>
        </template>
      </van-cell>
    </van-popup>
  </div>
</template>

<script>
export default {
  data() {
    return {
      bottomShow: false, // 问题反馈的弹出框显示与隐藏
      scanInfo: "", //二维码信息
      taskNum: 0,
      loginInfo: "",
      ibeacon: "",
      otimer: "",
      show: false,
      ibeaconArr: [],
      menuList: [],
      showTasks: false,
      showFeedback: false,
      showFeedbackList: false,
      showSignIn: false,
      showInspectionRecord: false,
      showAnalysis: false,
    };
  },
  created() {
    this.otimer = setInterval(() => {
      this.menuList = JSON.parse(localStorage.getItem("appMenuList"));
      if (this.menuList.length > 0) {
        this.menuList = JSON.parse(localStorage.getItem("appMenuList"));
        this.getMenu();
        this.$forceUpdate();
        clearInterval(this.otimer);
      }
    }, 300);
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.getTaskNum();
  },
  mounted() {
    // console.log('workHome');
    this.getLocation();
  },
  methods: {
    getMenu() {
      this.menuList.forEach((item) => {
        switch (item.menuName) {
          case "巡检任务":
            this.showTasks = true;
            break;
          case "问题反馈":
            this.showFeedback = true;
            break;
          case "反馈记录":
            this.showFeedbackList = true;
            break;
          case "扫码签到":
            this.showSignIn = true;
            break;
          case "巡检记录":
            this.showInspectionRecord = true;
            break;
          case "统计分析":
            this.showAnalysis = true;
            break;
        }
      });
    },
    //点击报修
    clickBall() {
      this.$router.push({
        path: "/feedback",
        query: {
          type: "3",
          from: "workerHome",
        },
      });
    },
    //点击问题反馈执行
    openPopup() {
      this.bottomShow = true;
    },
    //点击反馈记录跳转
    goFeedbackList() {
      this.$router.push({
        path: "/feedbackList",
      });
    },
    //点击扫码签到跳转
    goSignIn(val) {
      this.$router.push({
        path: "/signIn",
        query: {
          scanInfo: val,
        },
      });
    },
    //点击巡检记录跳转
    goInspectionRecord() {
      this.$router.push({
        path: "/inspectionRecord",
      });
    },
    goTask() {
      this.$router.push({
        path: "tasks",
      });
    },
    goAnalysis() {
      this.$router.push({
        path: "analysis",
      });
    },
    goContent() {
      this.scanInfo = "ihsp,ZXYSZGDW,ZKYXYY,ZKYXYY0200302003";
      this.getData();
    },
    //APP扫码
    APPScan(type) {
      const YBS = this.utils;
      if (!YBS.hasPermission("storage")) {
        let pageParam = {
          title: "存储权限使用说明",
          cont: "用于存储图片、视频等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["storage"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
              if (!YBS.hasPermission("camera")) {
                let pageParam = {
                  title: "摄像机权限使用说明",
                  cont: "用于扫描巡检码、空间码、拍照上传等场景"
                };
                YBS.openCustomDialog(pageParam, function () {
                  YBS.reqPermission(["camera"], function (ret) {
                    if (ret && ret.list.length > 0 && ret.list[0].granted) {
                    }
              });
                })
                return;
              }
            }
          });
        })
        return;
      }
      if (!YBS.hasPermission("camera")) {
        let pageParam = {
          title: "摄像机权限使用说明",
          cont: "用于扫描巡检码、空间码、拍照上传等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["camera"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
            }
          });
        })
        return;
      }
      try {
        this.utils.scanCode().then(
          (item) => {
            if (type == "sign") {
              if (item && item.length) {
                let scanInfo = item.join(",");
                console.log(scanInfo);
                this.goSignIn(scanInfo);
              }
            } else {
              if (item && item.length) {
                // this.pageLoading = true;
                // this.getEquipmentByIdFn(item[3]);
                this.scanInfo = item.join(",");
                console.log("scanInfo", this.scanInfo);
                this.getData();
              } else {
                this.$toast.fail("未查找到相关设备");
              }
            }
          },
          () => {
            this.$toast.fail("无效的二维码,请检查二维码");
          }
        );
      } catch (e) {
        this.$toast.fail(e);
      }
    },
    getData() {
      if (this.scanInfo == "") return;
      let params = {
        typeValue: this.scanInfo,
      };
      this.axios.postContralHostBase("getPerformTask", params, async (res) => {
        if (res.code != 200) return;
        // res.data = res.data.splice(0, 1);
        if (res.data.length == 1) {
          let dialogConfirm = "";
          if (res.data[0].taskPointRelease.executeOrder != "start") {
            dialogConfirm = await this.$dialog
              .confirm({
                title: "提示",
                message: "该任务需要按顺序执行，请返回上一任务点执行工作。如果继续执行请点击继续执行。",
                confirmButtonColor: "#00c5c0",
                confirmButtonText:'继续执行',
                cancelButtonText:'忽略'
              })
              .then(() => {
                // on confirm
                return "confirm";
              })
              .catch(() => {
                // on cancel
                return "cancel";
              });
          }
          if (dialogConfirm == "cancel") return;
          if (!res.data[0].taskPointRelease.maintainProjectRelease) {
            this.noBookSubmit(res.data[0]);
          } else {
            this.$router.push({
              path: "inspectionContent",
              query: {
                taskData: res.data[0],
                source: "bigScanOneData",
                // isLocation: this.isLocation,
              },
            });
          }
        } else if (res.data.length > 1) {
          console.log("返回的任务个数", res.data.length);
          this.$router.push({
            path: "tasks",
            query: {
              taskData: res.data,
              from: "bigScan",
              typeValue: this.scanInfo,
            },
          });
        } else {
          this.$toast("该二维码无关联任务");
        }
      });
    },
    getTaskNum() {
      let params = {};
      this.axios.postContralHostBase("getTaskNum", params, (res) => {
        if (res.code == "200") {
          this.taskNum = res.data.totalTaskNumber;
        }
      });
    },
    noBookSubmit(taskData) {
      let params = {
        taskPointReleaseId: taskData.taskPointRelease.id,
        state: "2",
        taskId: taskData.id,
        staffId: this.loginInfo.id,
        staffName: this.loginInfo.name,
        spyScan: "2",
        isBookEmpty: true,
      };
      this.axios.postContralHostBase("inspectionSubmit", params, (res) => {
        if (res.code == "200") {
          let that = this;
          this.$toast.success({
            message: "巡检完成!",
            duration: 1000,
            onClose() {},
          });
        }
      });
    },
    getLocation() {
      let that = this;
      let whetherLocation = sessionStorage.getItem("whetherLocation") || "";
      let giveUpLocation = sessionStorage.getItem("giveUpLocation") || "";
      let ibeaconArr = JSON.parse(sessionStorage.getItem("ibeaconArr")) || "";
      this.ibeacon = api.require("brightBeacon");
      if (
        whetherLocation == "true" ||
        giveUpLocation == "true" ||
        ibeaconArr.length > 0
      ) {
      } else {
        setTimeout(() => {
          that.show = true;
        }, 500);
        setTimeout(() => {
          that.show = false;
        }, 3000);
      }
      this.ibeacon.startRanging(
        {
          uuids: [
            "FDA50693-A4E2-4FB1-AFCF-C6EB07647825",
            "E2C56DB5-DFFB-48D2-B060-D0F5A71096E0",
          ],
        },
        function (ret, err) {
          console.log(ret);
          that.ibeaconArr = ret.list;
          console.log("testarr", that.ibeaconArr);
        }
      );
      setTimeout(() => {
        this.ibeacon.stopRanging();
        if (that.ibeaconArr.length == 0) {
          that.noIbeaconArr();
        } else {
          sessionStorage.setItem("ibeaconArr", JSON.stringify(that.ibeaconArr));
          sessionStorage.setItem("whetherLocation", "true");
        }
      }, 3000);
    },
    noIbeaconArr() {
      let whetherLocation = sessionStorage.getItem("whetherLocation") || "";
      let giveUpLocation = sessionStorage.getItem("giveUpLocation") || "";
      if (whetherLocation == "true" || giveUpLocation == "true") return;
      this.$dialog
        .confirm({
          title: "提示",
          message: "定位失败,请确认是否到达指定区域,手机是否开启蓝牙定位功能",
          cancelButtonText: "放弃定位",
          confirmButtonText: "重新定位",
          confirmButtonColor: "#00c5c1",
        })
        .then(() => {
          // on confirm
          this.getLocation();
          // sessionStorage.setItem("whetherLocation", "true");
        })
        .catch(() => {
          // on cancel
          sessionStorage.setItem("giveUpLocation", "true");
          this.$toast("已放弃定位");
        });
    },
    goBack() {
      api.closeWin();
    },
  },
};
</script>

<style scoped>
.scan-box > img {
  width: 84px;
  height: 86px;
  margin-bottom: 34px;
}
.scan-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 84px;
  margin-bottom: 30px;
}
.service > span {
  display: inline-block;
  padding-left: 16px;
  color: #353535;
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 39px;
}
.item-box {
  display: flex;
  flex-wrap: wrap;
}
.item-box .item {
  width: 33%;
  display: flex;
  flex-direction: column;
  font-size: 14px;
  align-items: center;
  margin-bottom: 35px;
}
.item-box .item img {
  width: 32px;
  margin-bottom: 6px;
}
.item-box .item span {
  color: #888888;
}
.popup-item {
  font-size: 18px;
  text-align: center;
  color: #353535;
}
.popup-cancel {
  font-size: 17px;
  text-align: center;
  color: #888888;
}
.van-cell__value {
  text-align: center;
}
.van-popup {
  padding-top: 18px;
  /* box-sizing: border-box; */
}
.van-cell {
  padding: 12px 16px;
}
.van-cell::after {
  border: none;
}
.cell-cancel {
  /* margin-top: 18px; */
  border-top: 10px solid #f5f6fb;
}
.dragBall {
  position: absolute;
  z-index: 10003;
  right: 0;
  top: 70%;
  width: 62px!important;
  height: 62px!important;
  background: none!important;
  background-image: url('../../assets/images/<EMAIL>')!important;
  background-size: 100% 100%!important;
  background-repeat: no-repeat!important;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: none!important;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0!important;
  user-select: none;
}
</style>