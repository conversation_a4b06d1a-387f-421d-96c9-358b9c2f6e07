<template>
  <div class=''>
    <Header title="" @backFun="goBack"> </Header>
    <van-overlay :show="loding">
      <div class="wrapper">
        <div class="block">
          <van-loading size="70px">{{ loadingText }}</van-loading>
        </div>
      </div>
    </van-overlay>
    <button id="btn" style="display:none" @click="APPScan">APPScan</button>
  </div>
</template>
<script>
import moment from 'moment'
import axios from 'axios'
  export default {
    components: {},
    data() {
      return {
        loding: true,
        loginInfo: null,
        isLocation: '',
        timer: '',
        loadingText: '开启摄像头...',
        photoAlbum: '1' // 1：是 2：否
      }
    },
    created() {
      this.timer = setInterval(() => {
        if (localStorage.getItem("loginInfo")) {
          this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
          this.getPermission()
          clearInterval(this.timer)
          this.sysClickBack()
        }
      }, 100);
    },
    methods: {
      sysClickBack() {
        api.addEventListener({
          name:'keyback666'
        },(ret,err) => {
          this.goBack()
        })
      },
      // 查询是否可以选择相册内的图片
      inquireIsPhotograph() {
        const url = __PATH.BASEURL +'/hospital/configuration/hospitalConfiguration/getIsPhotograph'
        const params = {
          unitCode: this.loginInfo.unitCode,
          hospitalCode: this.loginInfo.hospitalCode,
          name:"isPhotograph"
        }
        return axios.post(url,params)
      },
      async APPScan() {
        const isPhotoAlbum = await this.inquireIsPhotograph()
        // 校验是否配置可从相册选择
        if (isPhotoAlbum.data.code == '200') {
          this.photoAlbum = isPhotoAlbum.data.data.value
        }
        var FNScanner = api.require('FNScanner');
        this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
        return new Promise((resolve, reject) => {
          FNScanner.openScanner({
            autorotation: false,
            isAlbum: this.photoAlbum == '2' //是否隐藏相册按钮 1：是 2：否
          }, (ret, err) => {
            if (ret.eventType == 'success') {
              this.loding = false
              var sacnInfoArr = ret.content.split(",");
              const params = {
                typeValue: ret.content
              }
              // 风险点
              if (ret.content.indexOf('riskDisclosure?riskId') != -1) {
                const str = ret.content
                const riskDetail = str.slice(str.lastIndexOf('?') + 1, str.length).split('&')
                const scanCode = [
                  'risk',
                  riskDetail[1].substring(riskDetail[1].indexOf('=') + 1, riskDetail[1].length),
                  riskDetail[2].substring(riskDetail[2].indexOf('=') + 1, riskDetail[2].length),
                  riskDetail[0].substring(riskDetail[0].indexOf('=') + 1, riskDetail[0].length)
                ]
                sacnInfoArr = scanCode
                params.typeValue = scanCode.join(',')
              }
              sessionStorage.setItem('scanCodeData', JSON.stringify(params))
              this.loadingText = '加载中...'
              this.loding = true
              this.axios.postContralHostBase('getPerformTask', params, (res,error) => {
                if (res.code == '200') {
                  const scanData = res.data
                  this.loding = false
                  sessionStorage.setItem('source', sacnInfoArr[3])
                  if (scanData.length > 1) {
                    this.$router.push({
                      path: 'tasks',
                      query: {
                        from: 'scanCode',
                        source: sacnInfoArr[3]
                      }
                    })
                  } else if (scanData.length == 1) {
                    this.loding = true
                    this.axios.postContralHostBase("getTaskPointDetail", {
                      taskId: scanData[0].id
                    }, pointRes => {
                      if (pointRes.code == '200') {
                        this.loding = false
                        pointRes.data.forEach(pointItem => {
                          if (sacnInfoArr[0] == 'ihsp' || sacnInfoArr[0] == 'icms' || sacnInfoArr[0].toLowerCase() == 'risk') { // 空间点/自定义点二维码
                            if (pointItem.taskPointId == sacnInfoArr[3]) {
                              if (pointItem.carryOutFlag == '0') { // 执行
                                // 需要定位
                                if (scanData[0].locationFlag == '0') {
                                  this.getLocation(scanData[0].taskPointRelease.id, scanData[0].taskPointRelease.taskId,scanData[0].taskPointRelease.maintainProjectRelease,scanData[0].taskPointRelease.locationPointReleaseList)
                                } else {
                                  let whetherLocation = 1
                                  sessionStorage.setItem("whetherLocation", 1);
                                  // 有任务书
                                  if (scanData[0].taskPointRelease.maintainProjectRelease) {
                                    let type = null
                                    if (pointItem.maintainProjectRelease.equipmentTypeName == '日常巡检') {
                                      type = 0
                                    } else if (pointItem.maintainProjectRelease.equipmentTypeName == '专业巡检') {
                                      type = 1
                                    }
                                    this.$router.push({
                                      path: "checkDetail",
                                      query: {
                                        id: scanData[0].taskPointRelease.id,
                                        active: '',
                                        type,
                                        from: 'scanCodeSub'
                                      },
                                    });
                                  } else {
                                    this.noBookSubmit(scanData[0].taskPointRelease.id, scanData[0].taskPointRelease.taskId, whetherLocation)
                                  }
                                }
                              } else { // 详情
                                let type = null
                                if (pointItem.maintainProjectRelease && pointItem.maintainProjectRelease.equipmentTypeName == '日常巡检') {
                                  type = 0
                                } else if (pointItem.maintainProjectRelease && pointItem.maintainProjectRelease.equipmentTypeName == '专业巡检') {
                                  type = 1
                                } else {
                                  type = 2
                                }
                                this.$router.push({
                                  path: "recordDetail",
                                  query: {
                                    id: pointItem.id,
                                    type,
                                    from: 'scanCodeOneTask'
                                  }
                                })
                              }
                            }
                          } else if (sacnInfoArr[0].toUpperCase() == 'IAAS') { // 设备点二维码
                            if (pointItem.engineerCode == sacnInfoArr[3]) {
                              if (pointItem.carryOutFlag == '0') { // 执行
                                // 需要定位
                                if (scanData[0].locationFlag == '0') {
                                  this.getLocation(scanData[0].taskPointRelease.id, scanData[0].taskPointRelease.taskId,scanData[0].taskPointRelease.maintainProjectRelease,scanData[0].taskPointRelease.locationPointReleaseList)
                                } else {
                                  let whetherLocation = 1
                                  sessionStorage.setItem("whetherLocation", 1);
                                  // 有任务书
                                  if (scanData[0].taskPointRelease.maintainProjectRelease) {
                                    let type = null
                                    if (pointItem.maintainProjectRelease.equipmentTypeName == '日常巡检') {
                                      type = 0
                                    } else if (pointItem.maintainProjectRelease.equipmentTypeName == '专业巡检') {
                                      type = 1
                                    }
                                    this.$router.push({
                                      path: "checkDetail",
                                      query: {
                                        id: scanData[0].taskPointRelease.id,
                                        active: '',
                                        type,
                                        from: 'scanCodeSub'
                                      },
                                    });
                                  } else {
                                    this.noBookSubmit(scanData[0].taskPointRelease.id, scanData[0].taskPointRelease.taskId, whetherLocation)
                                  }
                                }
                              } else { // 详情
                                let type = null
                                if (pointItem.maintainProjectRelease && pointItem.maintainProjectRelease.equipmentTypeName == '日常巡检') {
                                  type = 0
                                } else if (pointItem.maintainProjectRelease && pointItem.maintainProjectRelease.equipmentTypeName == '专业巡检') {
                                  type = 1
                                } else {
                                  type = 2
                                }
                                this.$router.push({
                                  path: "recordDetail",
                                  query: {
                                    id: pointItem.id,
                                    type,
                                    from: 'scanCode'
                                  }
                                })
                              }
                            }
                          } else {
                            this.$toast.fail('二维码格式不正确，请联系管理员')
                          }
                        })
                      } else {
                        this.$toast.fail(pointRes.message || '服务器繁忙请稍后再试')
                      }
                    })
                  } else if (scanData.length  == 0) {
                    this.$toast.fail('无权限')
                    setTimeout(() => {
                      this.goBack()
                    },3000)
                  }
                } else {
                  this.loding = false
                  this.$toast.fail(res.message || '无效二维码')
                  setTimeout(() => {
                    this.goBack()
                  }, 2000)
                }
              })
            } else if (ret.eventType == 'cameraError') {
              this.loding = false
              this.$toast.fail({
                message: '请开启访问摄像头权限',
                duration: 2500
              })
              setTimeout(() => {
                FNScanner.closeView()
                this.goBack()
              }, 2500)
            }
            else if (ret.eventType == 'fail') {
              this.loding = false
              this.$toast.fail({
                message: '扫码失败,请重新扫码',
                duration: 2500
              })
              setTimeout(() => {
                FNScanner.closeView()
                this.goBack()
              }, 2500)
            } else if (ret.eventType == 'cancel') {
              FNScanner.closeView()
              this.goBack()
            }
          });

        })
      },
      //定位
      getLocation(id, taskId, maintainProjectRelease, location) {
        let that = this;
        let whetherLocation = sessionStorage.getItem("whetherLocation") || "";
        let giveUpLocation = sessionStorage.getItem("giveUpLocation") || "";
        let ibeaconArr = JSON.parse(sessionStorage.getItem("ibeaconArr")) || "";
        this.ibeacon = api.require('brightBeacon');
        if (
          whetherLocation == "true" ||
          giveUpLocation == "true" ||
          ibeaconArr.length > 0
        ) {} else {
          // setTimeout(() => {
            // that.show = true;
          // }, 500);
          setTimeout(() => {
            that.show = false;
          }, 2000);
        }
        this.loadingText = '正在扫描定位...'
        this.loding = true
        this.ibeacon.startRanging(
          {
            uuid: []
          }, (res, err) => {
            this.loding = false
            this.ibeaconArr = res.list.map(i => "_" + i.minor)
            if (this.ibeaconArr.length == 0) {
              this.ibeacon.stopRanging();
              this.$toast('无法获取定位信息，请开启蓝牙或重新扫码')
              sessionStorage.setItem("whetherLocation", 3);
            } else {
              sessionStorage.setItem("ibeaconArr", JSON.stringify(this.ibeaconArr));
              this.ibeaconArr.forEach(i => {
                whetherLocation = location.filter(item => item.deviceUuid + "_" + item.deviceMinor != i)
              })
              //whetherLocation 1：未开启定位 2:定位成功 3：定位失败
              if (whetherLocation.length > 0) {
                sessionStorage.setItem("whetherLocation", 2);
              }
            }
            this.show = false
            this.ibeacon.stopRanging();
            if (maintainProjectRelease) { // 有任务书
              let type = null
              if (maintainProjectRelease.equipmentTypeName == '日常巡检') {
                type = 0
              } else if (maintainProjectRelease.equipmentTypeName == '专业巡检') {
                type = 1
              }
              this.$router.push({
                path: "checkDetail",
                query: {
                  id: id,
                  active: '',
                  type,
                  from: 'scanCodeSub'
                },
              });
            } else { // 无任务书
              this.noBookSubmit(id, taskId, whetherLocation)
            }
          }
        );
      },
      // 无任务书提交
      noBookSubmit(id, taskId, isLocation) {
        let params = {
          taskPointReleaseId: id,
          state: "2",
          taskId: taskId,
          staffId: this.loginInfo.id,
          staffName: this.loginInfo.name,
          spyScan: isLocation,
          isBookEmpty: true,
        };
        this.axios.postContralHostBase("inspectionSubmit", params, (res) => {
          if (res.code == "200") {
            let that = this;
            this.$toast.success({
              message: "执行成功!",
              duration: 1000,
              onClose() {
                that.goBack();
              },
            });
          }
        });
      },
      goBack() {
        api.closeFrame({});
      },
      getPermission() {
        const YBS = this.utils;
        if (!YBS.hasPermission("storage")) {
          let strogePageParam = {
            title: "读取照片使用说明",
            cont: "用于扫码、存储图片、巡检报修等场景"
          };
        YBS.openCustomDialog(strogePageParam, function () {
          YBS.reqPermission(["storage"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
              if (!YBS.hasPermission("camera")) {
                let pageParam = {
                  title: "摄像机权限使用说明",
                  cont: "用于扫描巡检码、空间码、拍照上传等场景"
                };
                YBS.openCustomDialog(pageParam, function () {
                YBS.reqPermission(["camera"], function (ret) {
                  if (ret && ret.list.length > 0 && ret.list[0].granted) {
                    const btn = document.getElementById('btn')
                    btn.click()
                  } else {
                    this.$toast('请打开相机权限否则将无法拍照')
                  }
                });
                })
                return;
              }
            }
          });
        })
          return;
        } else {
          if (!YBS.hasPermission("camera")) {
            let pageParam = {
              title: "摄像机权限使用说明",
              cont: "用于扫描巡检码、空间码、拍照上传等场景"
            };
            YBS.openCustomDialog(pageParam, function () {
            YBS.reqPermission(["camera"], function (ret) {
              if (ret && ret.list.length > 0 && ret.list[0].granted) {
              } else {
                  this.$toast('请打开相机权限否则将无法拍照')
                }
              });
            })
            return;
          } else {
            const btn = document.getElementById('btn')
            btn.click()
          }
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  .block {
    padding: 10px 0 0 0;
    width: 120px;
    height: 110px;
    border-radius: 10px;
    background-color: #ddd;
    text-align: center;
  }
}
</style>