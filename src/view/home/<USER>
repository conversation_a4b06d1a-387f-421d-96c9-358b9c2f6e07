<template>
  <div class="container">
    <div class="top">
      <div class="navbar">
        <van-icon
          name="arrow-left"
          size="20"
          style="width: 50px"
          @click="goBack"
        />
        <span class="title">巡检服务</span>
        <div class="scan" @click="APPScan">
          <img src="../../assets/images/scan.png" />
          <span>扫码</span>
        </div>
      </div>
      <!-- <span class="title">服务助手</span> -->
    </div>
    <div class="manage-center">
      <span class="title">管理中心</span>
      <div class="item-box">
        <div class="item" v-show="showTasks">
          <van-badge :content="taskNum">
            <img
              src="../../assets/images/inspectionTasks_icon.png"
              @click="goTasks"
              style="margin-bottom: 3px"
            />
          </van-badge>
          <span>巡检任务</span>
        </div>
        <div class="item" v-show="showFeedback">
          <img
            src="../../assets/images/problemFeedback_icon.png"
            @click="openPopup"
          />
          <span>问题反馈</span>
        </div>
        <div class="item" v-show="showFeedbackList">
          <img
            src="../../assets/images/feedbackRecord_icon.png"
            @click="goFeedbackList"
          />
          <span>反馈记录</span>
        </div>
      </div>
    </div>
    <div class="data-center">
      <span class="title">数据中心</span>
      <div class="item-box">
        <div class="item" v-show="showAnalysisPro">
          <img
            src="../../assets/images/statisticAnalysis_icon.png"
            @click="goAnalysis"
          />
          <span>任务概览</span>
        </div>
        <div class="item" v-show="showSignRecord">
          <img
            src="../../assets/images/signRecord_icon.png"
            @click="goSignRecord"
          />
          <span>签到记录</span>
        </div>
      </div>
    </div>
    <van-popup v-model="bottomShow" round position="bottom">
      <van-cell to="feedback?type=1">
        <template>
          <span class="popup-item">整改</span>
        </template>
      </van-cell>
      <van-cell to="feedback?type=2">
        <template>
          <span class="popup-item">自修</span>
        </template>
      </van-cell>
      <van-cell to="feedback?type=3">
        <template>
          <span class="popup-item">报修</span>
        </template>
      </van-cell>
      <van-cell to="" class="cell-cancel" @click="bottomShow = false">
        <template>
          <span class="popup-cancel">取消</span>
        </template>
      </van-cell>
    </van-popup>
  </div>
</template>

<script>
export default {
  data() {
    return {
      scanInfo: "",
      bottomShow: false, // 问题反馈的弹出框显示与隐藏
      loginInfo: "",
      menuList: [],
      otimer: "",
      showTasks: false,
      showFeedback: false,
      showFeedbackList: false,
      showAnalysisPro: false,
      showSignRecord: false,
      taskNum: 0,
      show: false,
      ibeacon: "",
      ibeaconArr: [],
    };
  },
  created() {
    this.otimer = setInterval(() => {
      this.menuList = JSON.parse(localStorage.getItem("appMenuList"));
      if (this.menuList.length > 0) {
        this.menuList = JSON.parse(localStorage.getItem("appMenuList"));
        this.getMenu();
        this.$forceUpdate();
        clearInterval(this.otimer);
      }
    }, 300);
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.getTaskNum();
  },
  mounted() {
    // console.log('manageHome');
    this.getLocation();
  },
  methods: {
    getTaskNum() {
      let params = {};
      this.axios.postContralHostBase("getTaskNum", params, (res) => {
        if (res.code == "200") {
          this.taskNum = res.data.totalTaskNumber;
        }
      });
    },
    getMenu() {
      this.menuList.forEach((item) => {
        switch (item.menuName) {
          case "巡检任务":
            this.showTasks = true;
            break;
          case "问题反馈":
            this.showFeedback = true;
            break;
          case "反馈记录":
            this.showFeedbackList = true;
            break;
          case "任务概览":
            this.showAnalysisPro = true;
            break;
          case "签到记录":
            this.showSignRecord = true;
            break;
        }
      });
    },
    goTasks() {
      this.$router.push({
        path: "tasks",
      });
    },
    goFeedbackList() {
      this.$router.push({
        path: "feedbackList",
      });
    },
    //点击问题反馈执行
    openPopup() {
      this.bottomShow = true;
    },
    goAnalysis() {
      this.$router.push({
        path: "analysisPro",
      });
    },
    goSignRecord() {
      this.$router.push({
        path: "signRecord",
      });
    },
    //APP扫码
    APPScan(type) {
      const YBS = this.utils;
      if (!YBS.hasPermission("storage")) {
        let pageParam = {
          title: "存储权限使用说明",
          cont: "用于存储图片、视频等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["storage"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
              if (!YBS.hasPermission("camera")) {
                let pageParam = {
                  title: "摄像机权限使用说明",
                  cont: "用于扫描巡检码、空间码、拍照上传等场景"
                };
                YBS.openCustomDialog(pageParam, function () {
                  YBS.reqPermission(["camera"], function (ret) {
                    if (ret && ret.list.length > 0 && ret.list[0].granted) {
                    }
              });
                })
                return;
              }
            }
          });
        })
        return;
      }
      if (!YBS.hasPermission("camera")) {
        let pageParam = {
          title: "摄像机权限使用说明",
          cont: "用于扫描巡检码、空间码、拍照上传等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["camera"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
            }
          });
        })
        return;
      }
      try {
        this.utils.scanCode().then(
          (item) => {
            if (type == "sign") {
              if (item && item.length) {
                let scanInfo = item.join(",");
                console.log(scanInfo);
                this.goSignIn(scanInfo);
              }
            } else {
              if (item && item.length) {
                // this.pageLoading = true;
                // this.getEquipmentByIdFn(item[3]);
                this.scanInfo = item.join(",");
                console.log("scanInfo", this.scanInfo);
                this.getData();
              } else {
                this.$toast.fail("未查找到相关设备");
              }
            }
          },
          () => {
            this.$toast.fail("无效的二维码,请检查二维码");
          }
        );
      } catch (e) {
        this.$toast.fail(e);
      }
    },
    getData() {
      if (this.scanInfo == "") return;
      let params = {
        typeValue: this.scanInfo,
      };
      this.axios.postContralHostBase("getPerformTask", params, (res) => {
        if (res.code != 200) return;
        // res.data = res.data.splice(0, 1);
        if (res.data.length == 1) {
          if (!res.data[0].taskPointRelease.maintainProjectRelease) {
            this.noBookSubmit(res.data[0]);
          } else {
            this.$router.push({
              path: "inspectionContent",
              query: {
                taskData: res.data[0],
                source: "bigScanOneData",
              },
            });
          }
        } else if (res.data.length > 1) {
          this.$router.push({
            path: "tasks",
            query: {
              taskData: res.data,
              from: "bigScan",
              typeValue: this.scanInfo,
            },
          });
        } else {
          this.$toast("该二维码无关联任务");
        }
      });
    },
    noBookSubmit(taskData) {
      let params = {
        taskPointReleaseId: taskData.taskPointRelease.id,
        state: "2",
        taskId: taskData.id,
        staffId: this.loginInfo.id,
        staffName: this.loginInfo.name,
        spyScan: "2",
        isBookEmpty: true,
      };
      this.axios.postContralHostBase("inspectionSubmit", params, (res) => {
        if (res.code == "200") {
          let that = this;
          this.$toast.success({
            message: "巡检完成!",
            duration: 1000,
            onClose() {},
          });
        }
      });
    },
    getLocation() {
      let that = this;
      let whetherLocation = sessionStorage.getItem("whetherLocation") || "";
      let giveUpLocation = sessionStorage.getItem("giveUpLocation") || "";
      let ibeaconArr = JSON.parse(sessionStorage.getItem("ibeaconArr")) || "";
      this.ibeacon = api.require("brightBeacon");
      if (
        whetherLocation == "true" ||
        giveUpLocation == "true" ||
        ibeaconArr.length > 0
      ) {
      } else {
        setTimeout(() => {
          that.show = true;
        }, 500);
        setTimeout(() => {
          that.show = false;
        }, 3000);
      }
      this.ibeacon.startRanging(
        {
          uuids: [
            "FDA50693-A4E2-4FB1-AFCF-C6EB07647825",
            "E2C56DB5-DFFB-48D2-B060-D0F5A71096E0",
          ],
        },
        function (ret, err) {
          console.log(ret);
          that.ibeaconArr = ret.list;
          console.log("testarr", that.ibeaconArr);
        }
      );
      setTimeout(() => {
        this.ibeacon.stopRanging();
        if (that.ibeaconArr.length == 0) {
          that.noIbeaconArr();
        } else {
          sessionStorage.setItem("ibeaconArr", JSON.stringify(that.ibeaconArr));
          sessionStorage.setItem("whetherLocation", "true");
        }
      }, 3000);
    },
    noIbeaconArr() {
      let whetherLocation = sessionStorage.getItem("whetherLocation") || "";
      let giveUpLocation = sessionStorage.getItem("giveUpLocation") || "";
      if (whetherLocation == "true" || giveUpLocation == "true") return;
      this.$dialog
        .confirm({
          title: "提示",
          message: "定位失败,请确认是否到达指定区域,手机是否开启蓝牙定位功能",
          cancelButtonText: "放弃定位",
          confirmButtonText: "重新定位",
          confirmButtonColor: "#00c5c1",
        })
        .then(() => {
          // on confirm
          this.getLocation();
          // sessionStorage.setItem("whetherLocation", "true");
        })
        .catch(() => {
          // on cancel
          sessionStorage.setItem("giveUpLocation", "true");
          this.$toast("已放弃定位");
        });
    },
    goBack() {
      api.closeWin();
    },
  },
};
</script>

<style scoped>
.container {
  width: 100vw;
  background-color: #f5f6fb;
}
.top {
  width: 100%;
  height: 22vh;
  background-image: url("../../assets/images/indexBg.png");
  background-size: 100% 100%;
  padding-top: 20px;
  box-sizing: border-box;
  position: relative;
}
.top > .title {
  color: #fff;
  font-size: 20px;
  position: absolute;
  top: 58%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  padding: 0 16px;
  color: #fff;
}
.navbar img {
  width: 30px;
  height: 30px;
}
.navbar .title {
  font-size: 18px;
}
.navbar .scan {
  display: flex;
  align-items: center;
}
.manage-center,
.data-center {
  box-sizing: border-box;
  min-height: 260px;
  background-color: #fff;
  border-radius: 10px;
  margin: 0 16px;
  padding: 16px;
  padding-left: 0;
  transform: translateY(-30px);
}
.data-center {
  transform: translateY(-20px);
}
.manage-center .title,
.data-center .title {
  display: inline-block;
  margin-bottom: 39px;
  font-size: 16px;
  color: #353535;
  font-weight: 700;
  padding-left: 16px;
}
.item-box {
  display: flex;
  flex-wrap: wrap;
}
.item-box .item {
  width: 33%;
  display: flex;
  flex-direction: column;
  font-size: 14px;
  align-items: center;
  margin-bottom: 35px;
}
.item-box .item img {
  width: 50px;
  margin-bottom: 6px;
}
.item-box .item span {
  color: #888888;
}
.popup-item {
  font-size: 18px;
  text-align: center;
  color: #353535;
}
.popup-cancel {
  font-size: 17px;
  text-align: center;
  color: #888888;
}
.van-cell__value {
  text-align: center;
}
.van-popup {
  padding-top: 18px;
  /* box-sizing: border-box; */
}
.van-cell {
  padding: 12px 16px;
}
.van-cell::after {
  border: none;
}
.cell-cancel {
  /* margin-top: 18px; */
  border-top: 10px solid #f5f6fb;
}
</style>