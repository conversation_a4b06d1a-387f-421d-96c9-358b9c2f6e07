<template>
  <div class="vant-table">
    <div v-if="type == '1'">
      <table width="100%" cellspacing="0" cellpadding="0" style="width:100%" class="table">
        <tr>
          <th class="th" v-for="(item, index) in option.column" :key="index">{{ item.label }}</th>
        </tr>
        <tr v-for="(item, index) in tableData" :key="index" class="list-tr">
          <td v-for="(context, i) in option.column" :key="i">{{ item[context.tableDataprop] }}</td>
        </tr>
      </table>
    </div>
    <div v-if="type == '2'">
      <table cellspacing="0" cellpadding="0" style="width:100%" class="table">
        <tr>
          <th class="th" v-for="(item, index) in option.column" :key="index">{{ item.label }}</th>
        </tr>
        <tr v-for="(item, index) in tableData" :key="index" class="list-tr">
          <td v-for="(context, i) in option.column" :key="i">{{ item[context.tableDataprop] }}</td>
        </tr>
      </table>
    </div>
    <div v-if="type == '3'">
      <table cellspacing="0" cellpadding="0" style="width:100%" class="table">
        <tr v-for="(item, index) in tableData" :key="index" class="list-tr">
          <td :colspan="item.value ? 0 : 2" :class="item.value ? '' : 'typt-title'">{{ item.title }}</td>
          <td v-if="item.value">{{ item.value }}</td>
        </tr>
      </table>
    </div>
  </div>
</template>
<script>
export default {
  name: "TableVant",
  props: {
    tableData: {
      type: Array,
      default: []
    },
    option: {
      type: Object,
      default: {}
    },
    type: {
      type: Number,
      default: 0
    }
  },
  created() {}
};
</script>

<style scoped>
.table {
  table-layout: fixed;
  border-collapse: collapse;
  border-radius: 0.185185rem;
  border-right: 1px solid #797979;
  border-bottom: 1px solid #797979;
  font-size: 14px;
  background-color: #fff;
}
.th {
  font-weight: normal;
  height: 1.5rem;
  line-height: 1.5rem;
  background-color: #e6eaf0;
  text-align: center;
  border-left: 1px solid #797979;
  border-top: 1px solid #797979;
}
.th:last-child {
  border-right: 1px solid #797979;
}
.th:first-child {
  width: 12%;
}
.th:nth-child(3),
.th:nth-child(4),
.th:nth-child(5),
.th:nth-child(6) {
  width: 12%;
}
.list-tr {
  height: 1.074074rem;
  line-height: 1.074074rem;
}
td {
  text-align: center;
  height: 30px;
  border-left: 1px solid #797979;
  border-top: 1px solid #797979;
}
.typt-title {
  background-color: #ccc;
}
</style>
