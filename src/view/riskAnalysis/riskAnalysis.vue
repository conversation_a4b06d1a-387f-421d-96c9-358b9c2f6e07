<template>
  <div class="container">
    <Header title="风险分析" @backFun="goBack"> </Header>
    <div class="title">风险等级分布</div>
    <div style="padding: 0 8px;height: calc(50% - 55px);">
      <!-- <Table :type="1" :option="option" :tableData="tableData"></Table> -->
      <el-table
        :data="tableData"
        style="width: 100%"
        height="100%">
        <el-table-column
          v-for="(item, index) in column"
          :prop="item.tableDataprop"
          :label="item.label"
          :width="item.width === null ? 'auto' : item.width"
          :key="index"
          align="center">
          <template slot-scope="scope">
            <el-link
              v-if="item.tableDataprop != 'index' && item.tableDataprop != 'hospitalName' && scope.row.index < tableData.length"
              :underline="false"
              type="primary"
              @click="goRiskList(item.tableDataprop, scope.row)">
              {{ scope.row[item.tableDataprop] }}
            </el-link>
            <span v-else>{{ scope.row[item.tableDataprop] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="title" style="margin-top:24px">风险等级分析</div>
    <div id="myChart" style="width: 100vw; height: 30vh"></div>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
import taskList from "./list.vue";
import Table from "./table.vue";
export default {
  components: {
    topNav,
    taskList,
    Table
  },
  data() {
    return {
      show: false,
      echartData: [],
      echartData2: [],
      listData: [],
      dateType: "day",
      tableData: [],
      //th
      column: [
        {
          label: "序号",
          tableDataprop: "index",
          width: 50
        },
        {
          label: "医院名称",
          tableDataprop: "hospitalName"
        },
        {
          label: "重大",
          tableDataprop: "majorRisk",
          width: 50
        },
        {
          label: "较大",
          tableDataprop: "moreRisk",
          width: 50
        },
        {
          label: "一般",
          tableDataprop: "commonDangerCount",
          width: 50
        },
        {
          label: "低",
          tableDataprop: "lowDangerCount",
          width: 50
        }
      ],
      startTime: "",
      endTime: "",
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
      total: 0,
      timer: ""
    };
  },
  created() {
    this.timer = setInterval(() => {
      if (localStorage.getItem("loginInfo")) {
        this.getTableData();
        clearInterval(this.timer);
      }
    }, 100);
  },
  methods: {
    sysClickBack() {
      api.addEventListener(
        {
          name: "keyback666"
        },
        (ret, err) => {
          this.goBack();
        }
      );
    },
    initChart() {
      let myChart = this.$echarts.init(document.getElementById("myChart"));
      myChart.setOption({
        backgroundColor: "#fff",
        color: ["#41cb86", "#f24342", "#3a62d8", "#f99542"],
        legend: {
          orient: "horizontal",
          bottom: "0",
          data: [this.echartData[0].name, this.echartData[1].name, "", this.echartData[2].name, this.echartData[3].name],
          formatter: name => {
            let target;
            for (let i = 0; i < this.echartData.length; i++) {
              if (this.echartData[i].name == name) {
                target = this.echartData[i].value;
              }
            }
            if (!target) {
              return name + "(" + target + ")" + "  " + 0 + "%";
            } else {
              return name + "(" + target + ")" + "  " + ((target / this.total) * 100).toFixed(2) + "%";
            }
          }
        },
        series: [
          {
            type: "pie",
            // radius: ["40%", "60%"],
            radius: "60%",
            center: ["50%", "45%"],
            data: this.echartData,
            hoverAnimation: false,
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 1,
              labelLine: {
                length: 20,
                length2: 16,
                show: true,
                lineStyle: {
                  color: "#ccc"
                }
              }
            },
            label: {
              show: false,
              position: "outside",
              formatter: param => {
                return `${param.data.name}`;
              },
              backgroundColor: "auto", //圆点颜色，auto：映射的系列色
              // height,width,lineHeight必须为0
              height: 0,
              width: 0,
              lineHeight: 18,
              // radius和padding为圆点大小，圆点半径为几radius和padding各项数值就为几如：圆点半径为1
              // borderRadius: 4,
              // padding: [4, -4, 4, -4],
              borderRadius: 4,
              padding: [4, -4, 4, -4],
              rich: {
                a: {
                  fontSize: 14,
                  padding: [18, 8, 0, 10]
                },
                b: {
                  padding: [18, 8, 0, 10],
                  color: "#595959"
                }
              }
            }
          }
        ]
      });
    },
    initChart2() {
      let myChart = this.$echarts.init(document.getElementById("myChart2"));
      myChart.setOption({
        backgroundColor: "#fff",
        color: ["rgba(246, 189, 22, 0.85)", "rgba(90, 216, 166, 0.85)", "rgba(91, 143, 249, 0.85)", "rgba(93, 112, 146, 0.85)", "#e3584e"],
        series: [
          {
            type: "pie",
            // radius: ["40%", "60%"],
            radius: "60%",
            center: ["50%", "50%"],
            data: this.echartData2,
            hoverAnimation: false,
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 1,
              labelLine: {
                length: 20,
                length2: 16,
                show: true,
                lineStyle: {
                  color: "#ccc"
                }
              }
            },
            label: {
              position: "outside",
              formatter: "{b}",
              color: "#595959"
            }
          }
        ]
      });
    },
    getTableData(custom) {
      this.axios.postContralHostBase("getRiskAnalysis", {}, res => {
        if (res.code == "200") {
          this.tableData = res.data.resultData.map((i, index) => {
            i.index = index + 1;
            return i;
          });
          res.data.pieChart = res.data.pieChart.map(i => {
            let obj = {
              name: i.riskName,
              value: i.riskTotal
            };
            return obj;
          });
          this.echartData = res.data.pieChart;
          let total = 0;
          this.echartData.forEach(item => {
            total += item.value;
          });
          this.total = total;
          this.initChart();
        }
      });
    },
    getChart1Data(custom) {
      let params = {
        dateType: custom ? "" : this.dateType,
        startTime: custom ? this.startTime + " 00:00:00" : "",
        endTime: custom ? this.endTime + " 23:59:59" : ""
      };
      this.axios.postContralHostBase("getAnalysisChart1", params, res => {
        if (res.code == "200") {
          this.echartData = res.data;
          this.initChart();
        }
      });
    },
    getChart2Data(custom) {
      let params = {
        dateType: custom ? "" : this.dateType,
        startTime: custom ? this.startTime + " 00:00:00" : "",
        endTime: custom ? this.endTime + " 23:59:59" : ""
      };
      this.axios.postContralHostBase("getAnalysisChart2", params, res => {
        if (res.code == "200") {
          res.data.forEach(item => {
            if (item.name.length > 8) {
              item.name = item.name.substr(0, 8) + "...";
            }
          });
          this.echartData2 = res.data;
          this.initChart2();
        }
      });
    },
    changeBtn(val) {
      this.dateType = val;
      if (val != "custom") {
        this.getTableData(this.show);
        this.getChart1Data(this.show);
        this.getChart2Data(this.show);
      } else {
        this.show = true;
      }
    },
    getData() {
      this.getTableData(this.show);
      this.getChart1Data(this.show);
      this.getChart2Data(this.show);
      this.show = false;
    },
    onClose() {
      this.show = false;
      this.startTime = "";
      this.endTime = "";
    },
    goBack() {
      api.closeFrame({});
    },
    goRiskList(type, row) {
      console.log(type, row)
      if (
          (type == 'moreRisk' && row.moreRisk == 0) ||
          (type == 'majorRisk' && row.majorRisk == 0) ||
          (type == 'commonDangerCount' && row.commonDangerCount  == 0) ||
          (type == 'lowDangerCount' && row.lowDangerCount == 0)
        ) {
        this.$toast.fail('暂无更多！')
      } else {
        api.openWin({
          name: 'idps/riskParameter',
          url: 'widget://html/common_window.html',
          bgColor: 'rgba(250, 250, 250, 0)',
          hideHomeIndicator: true,
          bounces: false,
          scrollEnabled: false,
          useWKWebView: true,
          pageParam: {
            title: '风险清单',
            link: 'widget://html/idps/riskParameter.html',
            currentHospital: {hospitalCode: row.hospitalCode},
          }
        })
      }
    }
  },
  mounted() {
    setTimeout(() => {
      this.sysClickBack();
    }, 500);
  }
};
</script>

<style lang="scss" scoped>
.container {
  width: 100vw;
  height: 95vh;
  background-color: #fff;
  padding-bottom: 5vh;
}
.title {
  padding: 0 16px;
  font-size: 16px;
  height: 40px;
  line-height: 40px;
  /* border-bottom: 1px solid #ccc4c4; */
  color: #353535;
  font-weight: 600;
  text-align: center;
}
.btns {
  padding: 0 16px;
  margin: 10px 0 0 0;
  display: flex;
  width: 52%;
  justify-content: space-between;
}
.btns .btn {
  width: 50px;
  height: 26px;
  border: 1px solid #797979;
  color: #333333;
  font-size: 13px;
  text-align: center;
  line-height: 26px;
}
.btn:nth-child(2) {
  border-left: none;
  border-right: none;
}
.btn:nth-child(3) {
  border-right: none;
}
.active-btn {
  background-color: #29bebc;
  color: #fff !important;
}
.date-title {
  padding: 10px 0;
  text-align: center;
  font-size: 16px;
  font-weight: 600px;
}
.time-text {
  font-size: 13px;
  margin: 0 auto;
  text-align: center;
}
>>> .van-cell {
  width: auto;
}
.items {
  padding: 5px 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.date-picker {
  padding: 10px 8px;
  min-height: 24px;
  width: 60%;
}
.vant-table {
  height: 40vh;
  overflow-y: auto;
}
/deep/ .el-table .el-table__cell {
  padding: 8px 0;
}
/deep/ .el-table__header {
  th {
    background-color: #E6EFFC;
    color: #1D2129;
  }
}
</style>
