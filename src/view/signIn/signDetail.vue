<template>
  <div class="container">
    <Header title="签到" @backFun="$router.go(-1)"> </Header>
    <!-- <top-nav title="签到"></top-nav> -->
    <div class="location">
      <span>任务点名称</span>
      <span>{{taskPointInfo.taskPointName}}</span>
    </div>
    <div class="remark">
      <div class="title">签到说明</div>
      <van-field
        v-model="message"
        type="textarea"
        placeholder="请填写签到说明，内容不超过500个字符"
        maxlength="500"
      />
    </div>
    <div class="btns">
      <van-button type="primary" size="large" round color="#29BEBC" @click="signIn"
        >签到</van-button
      >
      <van-button
        type="primary"
        size="large"
        round
        color="#29BEBC"
        plain
        @click="goBack"
        >取消</van-button
      >
    </div>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
export default {
  components: {
    topNav,
  },
  data() {
    return {
      message: "",
      loginInfo:'',
      taskPointInfo:'',
      scanInfo:''
    };
  },
  created() {
    this.taskPointInfo = this.$route.query.taskPointInfo
    this.scanInfo = this.$route.query.scanInfo
    this.loginInfo = JSON.parse(localStorage.getItem('loginInfo'))
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    signIn() {
      let params = {
        remarks:this.message,
        createBy:this.loginInfo.id,
        createByName:this.loginInfo.name,
        company:this.loginInfo.company,
        companyCode:this.loginInfo.companyCode,
        taskPointName:this.taskPointInfo.taskPointName,
        taskPointCode:this.taskPointInfo.taskPointCode,
        typeValue:this.scanInfo,
        officeCode:this.loginInfo.officeCode,
        officeName:this.loginInfo.officeName
      }
      this.axios.postContralHostBase('signIn',params,res=>{
        if(res.code==200) {
          this.$toast('保存成功!')
          this.$router.go(-1)
        }
      })
    }
  },
};
</script>

<style scoped>
.container {
  width: 100vw;
  /* background-color: #f5f6fb; */
}
.top-nav {
  height: 64px;
  background-color: #29bebc;
  text-align: center;
  color: #fff;
  position: relative;
}
.top-nav span {
  font-size: 18px;
  line-height: 80px;
}
.arrow-icon {
  position: absolute;
  top: 50%;
  left: 16px;
  transform: translateY(-4px);
}
.location {
  display: flex;
  background-color: #fff;
  min-height: 48px;
  /* line-height: 48px; */
  padding-left: 16px;
  border-bottom: 1px solid #ebedf0;
  margin-top: 12px;
}
.location span:nth-child(1) {
  font-size: 15px;
  color: #353535;
  font-weight: 600;
  /* margin-right: 8px; */
  width: 100px;
}
.location span:nth-child(2) {
  font-size: 14px;
  color: #888888;
  flex: 1;
}
.remark {
  background-color: #fff;
  padding-top: 12px;
  margin-bottom: 8px;
}
.remark .title {
  font-size: 15px;
  color: #353535;
  font-weight: 600;
  padding-left: 16px;
}
.btns {
  padding: 0 16px;
  background-color: #fff;
}
.btns > button:nth-child(1) {
  margin-bottom: 12px;
}
>>> .remark .van-field__control {
  background-color: #f7f8fa;
  border-radius: 8px;
  padding: 8px;
  height: 50vh;
}
</style>