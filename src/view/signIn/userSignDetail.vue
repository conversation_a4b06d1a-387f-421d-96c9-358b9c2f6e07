<template>
  <div class="container">
    <Header title="人员签到详情" @backFun="$router.go(-1)"> </Header>
    <!-- <top-nav title="人员签到详情"></top-nav> -->
    <div class="record">
      <div class="title">签到记录</div>
      <div class="record-list" v-for="item in listData" :key="item.id">
        <div class="top">
          <span class="name">{{ item.create_by_name }}</span>
          <div class="date">
            <van-icon name="underway-o" />
            <span>{{ transDate(item.create_time) }}</span>
          </div>
        </div>
        <div class="location">
          <span>签到地点</span>
          <span>{{item.task_point_name}}</span>
        </div>
        <div class="remark">
          <span>签到说明</span>
          <span>{{ item.remarks }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
export default {
  components: {
    topNav,
  },
  data() {
    return {
      details: "",
      listData: [],
    };
  },
  created() {
    this.details = this.$route.query.details || "";
    this.getData();
  },
  methods: {
    getData() {
      let params = {
        distributionTeamId: this.details.distribution_team_id,
        createBy: this.details.create_by,
      };
      this.axios.postContralHostBase(
        "getScanSignedRecordDetail",
        params,
        (res) => {
          if (res.code == "200") {
            this.listData = res.data;
          }
        }
      );
    },
    transDate(val) {
      return this.utils.formatDateTime(val)
    }
  },
};
</script>

<style scoped>
.container {
  width: 100vw;
  background-color: #f5f6fb;
}
.record {
  width: 100%;
  background-color: #fff;
}
.record .title {
  color: #353535;
  font-size: 16px;
  font-weight: 600;
  height: 54px;
  line-height: 54px;
  padding-left: 16px;
  border-bottom: 1px solid #f5f6fb;
}
.record-list {
  border-bottom: 1px solid #f5f6fb;
  padding-top: 15px;
}
.record-list .top {
  display: flex;
  justify-content: space-between;
  padding: 0 16px;
  margin-bottom: 8px;
}
.record-list .top .name {
  font-size: 16px;
  font-weight: 600;
  color: #353535;
}
.record-list .top .date {
  font-size: 14px;
  color: #b2b2b2;
  display: flex;
  align-items: center;
}
.record-list .top .date span {
  margin-left: 3px;
}
.record-list .location {
  display: flex;
  padding-left: 16px;
  margin-bottom: 8px;
}
.record-list .location span:nth-child(1),
.record-list .remark span:nth-child(1) {
  color: #353535;
  font-size: 15px;
  min-width: 75px;
}
.record-list .location span:nth-child(2),
.record-list .remark span:nth-child(2) {
  color: #888888;
  font-size: 14px;
}
.record-list .remark {
  padding: 0 16px;
  margin-bottom: 15px;
  display: flex;
}
.record-list .remark span:nth-child(1) {
  min-width: 75px;
}
</style>