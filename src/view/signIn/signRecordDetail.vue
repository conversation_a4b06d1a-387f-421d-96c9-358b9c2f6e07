<template>
  <div class="container">
    <Header title="签到详情" @backFun="$router.go(-1)"> </Header>
    <!-- <top-nav title="签到详情"></top-nav> -->
    <sign-list :listData="listData"></sign-list>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
import signList from "../components/signList.vue";
export default {
  components: {
    topNav,
    signList,
  },
  data() {
    return {
      id: "",
      listData: [],
    };
  },
  created() {
    this.id = this.$route.query.id || "";
    this.getData();
  },
  methods: {
    getData() {
      let params = {
        distributionTeamId:this.id
      };
      this.axios.postContralHostBase("getScanSignedRecord", params, (res) => {
        if (res.code == "200") {
          this.listData = res.data;
        }
      });
    },
  },
};
</script>

<style scoped>
</style>