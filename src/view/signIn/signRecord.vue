<template>
  <div class="container">
    <Header title="签到记录" @backFun="$router.go(-1)"> </Header>
    <!-- <top-nav title="签到记录"></top-nav> -->
    <div class="nav">
      <span class="title">签到记录</span>
      <div class="btns">
        <div
          :class="['btn', activeIndex == 'day' ? 'active-btn' : '']"
          @click="changeBtn('day')"
        >
          今日
        </div>
        <div
          :class="['btn', activeIndex == 'month' ? 'active-btn' : '']"
          @click="changeBtn('month')"
        >
          本月
        </div>
        <div
          :class="['btn', activeIndex == 'year' ? 'active-btn' : '']"
          @click="changeBtn('year')"
        >
          本年
        </div>
      </div>
    </div>
    <sign-list
      :listData="listData"
      type="1"
      style="margin-bottom: 10px"
    ></sign-list>
    <sign-list :listData="taskPointData" type="2"></sign-list>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
import signList from "../components/list.vue";
export default {
  components: {
    topNav,
    signList,
  },
  data() {
    return {
      listData: [],
      activeIndex: "month",
      taskPointData: [],
    };
  },
  created() {
    this.getData();
    this.getTaskPointData();
  },
  methods: {
    getData() {
      let params = {
        dateType: this.activeIndex,
      };
      this.axios.postContralHostBase("getScanSignedRecord", params, (res) => {
        if (res.code == "200") {
          this.listData = res.data;
        }
      });
    },
    getTaskPointData() {
      let params = {
        dateType: this.activeIndex,
      };
      this.axios.postContralHostBase("getTaskPointRecord", params, (res) => {
        if (res.code == "200") {
          this.taskPointData = res.data;
        }
      });
    },
    changeBtn(index) {
      this.activeIndex = index;
      this.getData();
      this.getTaskPointData();
    },
  },
};
</script>

<style scoped>
.container {
  width: 100vw;
  background-color: #f5f6fb;
}
.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  background-color: #fff;
  padding: 0 16px;
}
.nav .title {
  font-size: 16px;
  color: #353535;
  font-weight: 600;
}
.btns {
  display: flex;
  width: 52%;
  justify-content: space-between;
}
.btns .btn {
  width: 50px;
  height: 26px;
  border: 1px solid #29bebc;
  color: #29bebc;
  font-size: 13px;
  text-align: center;
  line-height: 26px;
  border-radius: 2px;
}
.active-btn {
  background-color: #29bebc;
  color: #fff !important;
}
</style>