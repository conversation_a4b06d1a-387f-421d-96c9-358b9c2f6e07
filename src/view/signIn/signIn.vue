<template>
  <div class="container">
    <van-sticky>
      <Header title="签到" @backFun="$router.go(-1)"> </Header>
      <!-- <top-nav title="签到"></top-nav> -->
      <div class="f-box">
        <div class="sign-box">
          <div class="info">{{ taskPointInfo.taskPointName }}</div>
          <div class="btn" @touchstart="() => {}" @click="goDetail">
            <img src="../../assets/images/signIn.png" />
          </div>
        </div>
      </div>
    </van-sticky>
    <div class="record">
      <div class="title">签到记录</div>
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div class="record-list" v-for="item in dataList" :key="item.id">
          <div class="top">
            <span class="name">{{ item.createByName }}</span>
            <div class="date">
              <van-icon name="underway-o" />
              <span>{{ item.createTime }}</span>
            </div>
          </div>
          <div class="location">
            <span>任务点名称</span>
            <span>{{ item.taskPointName }}</span>
          </div>
          <div class="remark">
            <span>签到说明&emsp;&ensp;</span>
            <span>{{ item.remarks }}</span>
          </div>
        </div>
      </van-list>
    </div>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
export default {
  components: {
    topNav,
  },
  data() {
    return {
      loading: false,
      finished: false,
      pageNo: 1,
      pageSize: 10,
      dataList: [],
      scanInfo: "",
      taskPointInfo: "",
    };
  },
  created() {
    this.scanInfo = this.$route.query.scanInfo;
    this.getData();
    this.getTaskPoint();
  },
  methods: {
    //跳转到签到详情页
    goDetail() {
      this.$router.push({
        path: "signDetail",
        query: {
          taskPointInfo: this.taskPointInfo,
          scanInfo:this.scanInfo
        },
      });
    },
    getData() {
      let params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        typeValue:this.scanInfo
      };
      this.axios.postContralHostBase("getSignRecord", params, (res) => {
        if (res.code == 200) {
          this.finished = true;
          this.loading = false;
          this.dataList = res.data.list;
        }
      });
    },
    getTaskPoint() {
      let params = {
        typeValue: this.scanInfo,
      };
      this.axios.postContralHostBase("getSignTaskPoint", params, (res) => {
        if (res.code != 200) return;
        this.taskPointInfo = res.data;
      });
    },
    onLoad() {},
  },
};
</script>

<style scoped>
img {
  -webkit-touch-callout: none;
}
.container {
  width: 100vw;
  /* background-color: #f5f6fb; */
}
.f-box {
  width: 100%;
  height: 88px;
  background-color: #f5f6fb;
  display: flex;
  justify-content: center;
  align-items: center;
}
.sign-box {
  width: 95%;
  height: 66px;
  background-color: #fff;
  border-radius: 10px;
  display: flex;
}
.sign-box .info {
  width: 78%;
  height: 100%;
  font-size: 15px;
  font-weight: 400;
  color: #353535;
  display: flex;
  align-items: center;
  padding-left: 16px;
}
.sign-box .btn {
  width: 22%;
  height: 100%;
}
.sign-box .btn img {
  width: 100%;
  height: 100%;
}
.sign-box .btn:active {
  opacity: 0.8;
}
.record {
  width: 100%;
  background-color: #fff;
}
.record .title {
  color: #353535;
  font-size: 16px;
  font-weight: 600;
  height: 54px;
  line-height: 54px;
  padding-left: 16px;
  border-bottom: 1px solid #f5f6fb;
}
.record-list {
  border-bottom: 1px solid #f5f6fb;
  padding-top: 15px;
}
.record-list .top {
  display: flex;
  justify-content: space-between;
  padding: 0 16px;
  margin-bottom: 8px;
}
.record-list .top .name {
  font-size: 16px;
  font-weight: 600;
  color: #353535;
}
.record-list .top .date {
  font-size: 14px;
  color: #b2b2b2;
  display: flex;
  align-items: center;
}
.record-list .top .date span {
  margin-left: 3px;
}
.record-list .location {
  padding-left: 16px;
  margin-bottom: 8px;
  display: flex;
}
.record-list .location span:nth-child(1),
.record-list .remark span:nth-child(1) {
  color: #353535;
  font-size: 15px;
  min-width: 96px;
  margin-right: 12px;
}
.record-list .location span:nth-child(2),
.record-list .remark span:nth-child(2) {
  color: #888888;
  font-size: 14px;
  line-height: 21px;
}
.record-list .remark {
  padding: 0 16px;
  margin-bottom: 15px;
  display: flex;
}
.record-list .remark span:nth-child(1) {
  min-width: 96px;
}
</style>