<template>
  <div class='container'>
    <Header title="填报详情" @backFun="goBack"> </Header>
    <div v-loading="pageLoading" class="inner">
      <div class="titleBar">
        <div class="leftContent">
          <span class="line"></span>
          填报信息
        </div>
        <van-button v-if="isEdit" color="#29BEBC" @click="saveDetail">保存</van-button>
        <van-icon v-else name="edit" size="20" @click="editDetail"/>
      </div>
      <div class="detailInfo">
        <template v-if="isEdit">
          <van-field
            v-if="columns.length > 1"
            v-model="currentDept"
            required
            readonly
            label="填报科室"
            @click="deptShow = true">
            <template #right-icon>
              <van-icon name="arrow-down" />
            </template>
          </van-field>
          <van-field
            v-else
            v-model="currentDept"
            required
            readonly
            label="填报科室"
          />
        </template>
        <van-field
          v-else
          v-model="detailData.departmentName"
          readonly
          label="填报科室"
        />
        <van-field
          v-if="isEdit"
          required
          v-model="loginInfo.name"
          readonly
          label="填报人姓名"
        />
        <van-field
          v-else
          v-model="detailData.informantName"
          readonly
          label="填报人姓名"
        />
        <van-field
          v-if="isEdit"
          required
          v-model="phone"
          type="number"
          maxlength="11"
          label="联系电话"
        /><van-field
          v-else
          v-model="detailData.phone"
          type="tel"
          maxlength="11"
          label="联系电话"
          readonly
        />
        <van-field
          v-if="isEdit"
          v-model="work"
          readonly
          required
          label="安全管理工作"
          placeholder="选择安全管理工作类型/事项"
          @click="selectWork"
        />
        <van-field
          v-else
          :value="detailData.jobCategoryName + '/' + detailData.managementIssueName"
          readonly
          label="安全管理工作"
          placeholder="选择安全管理工作类型/事项"
        />
        <div v-if="isEdit" class="imgSub">
          <div class="img-label">图片支撑</div>
          <van-uploader
            class="permit-upload"
            v-model="imgList"
            multiple
            accept=".doc,.docx,.pdf,image/*"
            max-count="10"
            :after-read="afterImgRead"
          />
        </div>
        <div v-else class="itemBar">
          <div class="item-label">图片支撑</div>
          <div class="itemList">
            <van-image
              v-for="(item, index) in detailData.imgData"
              :key="index + item.name"
              width="100"
              height="100"
              :src="item.url"
              @click="showImg = true"
            />
            <div class="fileItem" v-for="(item, index) in detailData.fileData" :key="index + item.name" @click="downLoadFile(item)">
              <van-icon size="20" name="description" />
              <div>{{ item.name }}</div>
            </div>
          </div>
        </div>
        <van-field
          v-if="isEdit"
          v-model="descriptionContent"
          rows="2"
          autosize
          label="活动描述"
          type="textarea"
          maxlength="500"
          placeholder="请输入内容"
          show-word-limit
        />
        <div v-else class="itemBar">
          <div class="item-label">活动描述</div>
          <div class="itemList">
            {{ detailData.activityDescription || '暂无' }}
          </div>
        </div>
        <van-field
          v-if="isEdit"
          v-model="currentDateStr"
          required
          readonly
          label="填报时间"
          @click="datePanelShow = true"
        />
        <van-field
          v-else
          v-model="detailData.fillingTime"
          readonly
          label="填报时间"
        />
      </div>
    </div>
    <van-image-preview v-model="showImg" :images="detailData.imgData.map(i => i.url)"></van-image-preview>
    <!-- 选择填报工作类型 -->
    <van-popup v-model="workPanelShow" round :close-on-click-overlay="false" position="bottom" :lock-scroll="false" :style="{ height: '40%' }">
      <van-cascader
        class="workClass"
        v-model="currentWork"
        title="选择工作类型/事项"
        :options="optionList"
        :field-names="fieldNames"
        @close="workPanelShow = false"
        @finish="onFinish"
      />
    </van-popup>
    <!-- 选择填报时间 -->
    <van-popup v-model="datePanelShow" round :close-on-click-overlay="false" position="bottom" :style="{ height: '40%' }">
      <van-datetime-picker
        v-model="currentDate"
        type="datetime"
        :formatter="formatter"
        @confirm="dateComfirm"
        @cancel="datePanelShow = false"
      />
    </van-popup>
    <!-- 选择科室 -->
    <van-popup v-model="deptShow" round :close-on-click-overlay="false" position="bottom" :style="{ height: '40%' }">
      <van-picker
        title="标题"
        show-toolbar
        :columns="columns"
        @confirm="deptConfirm"
        @cancel="deptShow = false"
      />
    </van-popup>
  </div>
</template>
<script>
import moment from 'moment'
export default {
  components: {},
  data() {
    return {
      pageLoading: false,
      moment,
      loginInfo: {},
      isEdit: false,
      detailData: {
        imgData: []
      },
      imgList: [],
      showImg: false,
      allOption: [],
      optionList: [],
      fieldNames: {
        text: 'classifyName',
        value: 'id',
        children: 'children',
      },
      phone: '',
      work: '',
      workName: [],
      workId: [],
      currentWork: '',
      descriptionContent: '',
      workPanelShow: false,
      currentDept: '',
      currentDeptId: '',
      columns: [],
      deptShow: false,
      datePanelShow: false,
      currentDate: '',
      currentDateStr: ''
    }
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.columns = this.loginInfo.officeName.split(',')
    this.currentDept = this.columns[0]
    this.currentDeptId = this.loginInfo.officeId.split(',')[0]
    this.getDetailData()
    this.getClass()
    this.sysClickBack()
  },
  methods: {
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`;
      } else if (type === 'month') {
        return `${val}月`;
      } else if (type === 'day') {
        return `${val}日`;
      } else if (type === 'hour') {
        return `${val}时`;
      } else if (type === 'minute') {
        return `${val}分`;
      }
      return val;
    },
    goBack() {
      this.$router.go(-1)
    },
    getClass() {
      this.axios.postContralHostBase('getClassify', {}, res => {
        if (res.code == '200') {
          this.allOption = res.data
          this.optionList = this.utils.transData(
            res.data,
            "id",
            "parentId",
            "children"
          );
        } else {
          this.$message.error(res.message || '提交失败！')
        }
      })
    },
    getDetailData() {
      this.pageLoading = true
      this.axios.postContralHostBase('getSafetyManagementDetail', {
        id: this.$route.query.id
      }, res => {
        if (res.code == '200') {
          this.detailData = res.data
          console.log(JSON.parse(this.detailData.pictureUrl))
          this.detailData.imgData = []
          this.detailData.fileData = []
          JSON.parse(this.detailData.pictureUrl).forEach(i => {
            const item = {
              url: i.path,
              fileName: i.name,
              name: i.name,
              status: 'success'
            }
            const suffix = i.name.slice(i.name.lastIndexOf('.'), i.name.length).toLowerCase()
            const imgFormat = ['.jpg', '.jpeg', '.png', '.gif', '.gif', '.svg']
            if (imgFormat.includes(suffix)) {
              this.detailData.imgData.push(item)
            } else {
              this.detailData.fileData.push(item)
            }
          })
        } else {
          this.$message.error(res.message || '获取详情失败！')
        }
        this.pageLoading = false
      })
    },
    selectWork() {
      this.workPanelShow = true
    },
    onFinish({selectedOptions}) {
      if (selectedOptions.length == 2) {
        this.workPanelShow = false
        this.workId = []
        this.workName = []
        selectedOptions.forEach(option => this.workId.push(option.id))
        selectedOptions.forEach(option => this.workName.push(option.classifyName))
        this.work = selectedOptions.map(option => option.classifyName).join('/')
      }
    },
    dateComfirm(val) {
      this.currentDateStr = moment(val).format('YYYY-MM-DD HH:mm')
      this.datePanelShow = false
    },
    afterImgRead(file) {
      let params = {
        file: file.file
      };
      this.pageLoading = true
      this.axios.postContralHostBase("uploadImg", params, res => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          // 优先取全局设置的SSO域名，如果取不到则取上传后返回的临时SSO地址中的域名
          const downloadUrl = __PATH.DOWNLOAD_URL || new URL(data.temporaryUrl).origin + '/';
          this.imgList[this.imgList.length - 1].url = downloadUrl + data.fileKey;
          this.imgList[this.imgList.length - 1].fileName = data.fileName;
        }
        this.pageLoading = false
      });
    },
    editDetail() {
      if (moment(this.detailData.fillingTime).format('YYYY-MM') == moment().format('YYYY-MM')) {
        this.currentWork = this.detailData.managementIssueId
        this.phone = this.detailData.phone
        this.work = this.detailData.jobCategoryName + '/' + this.detailData.managementIssueName
        this.workId = [this.detailData.jobCategoryId, this.detailData.managementIssueId]
        this.workName = [this.detailData.jobCategoryName, this.detailData.managementIssueName]
        this.descriptionContent = this.detailData.activityDescription
        this.imgList = []
        JSON.parse(this.detailData.pictureUrl).forEach(i => {
          const item = {
            url: i.path,
            fileName: i.name,
            file : new File([], i.name, {}),
            name: i.name,
            status: 'success'
          }
          this.imgList.push(item)
        })
        this.currentDate = new Date(moment(this.detailData.fillingTime).format('YYYY-MM-DD HH:mm'))
        this.currentDateStr = moment(this.detailData.fillingTime).format('YYYY-MM-DD HH:mm')
        this.isEdit = true
      } else {
        this.$toast("已过期无法修改")
      }
    },
    saveDetail() {
      if (this.currentDept &&
        this.loginInfo.name &&
        this.phone &&
        this.work &&
        this.imgList.length > 0
        )
      {
        const imgArr = []
        this.imgList.forEach(i => imgArr.push({
          name: i.fileName,
          path: i.url
        }))
        const params = {
          id: this.$route.query.id,
          departmentId: this.currentDeptId,
          departmentName: this.currentDept,
          informantId: this.loginInfo.id,
          informantName: this.loginInfo.name,
          phone: this.phone,
          jobCategoryId: this.workId[0],
          jobCategoryName: this.workName[0],
          managementIssueId: this.workId[1],
          managementIssueName: this.workName[1],
          activityDescription: this.descriptionContent,
          pictureUrl: JSON.stringify(imgArr),
          fillingTime: this.currentDateStr,
          userId: this.loginInfo.id,
          userName: this.loginInfo.name,
          integral: String(this.allOption.find(i => i.id == this.workId[1]).integral)
        }
        this.pageLoading = true
        this.axios.postContralHostBase('saveSafetyManagement', params, res => {
          if (res.code == '200') {
            this.$message.success('保存成功！')
            this.getDetailData()
          } else {
            this.$message.error(res.message || '保存失败！')
          }
          this.pageLoading = false
        })
      } else {
        this.$message.error('请填写所有必填项')
      }
      this.isEdit = false
    },
    deptConfirm(val) {
      this.currentDept = val
      this.currentDeptId = this.loginInfo.officeId.split(',')[this.columns.indexOf(this.currentDept)]
      this.deptShow = false
    },
    // 点击按钮返回
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    downLoadFile(item) {
      api.download(
        {
          url: item.url,
          report: true,
          cache: true,
          allowResume: true
        },
        function(ret, err) {
          if (ret.state == 1) {
            if (api.systemType == "ios") {
              api.openWin({
                name: "my/pdfview",
                url: "widget://html/common_window.html",
                bgColor: "rgba(250, 250, 250, 0)",
                hideHomeIndicator: true,
                bounces: false,
                scrollEnabled: false,
                useWKWebView: true,
                pageParam: {
                  title: "文件预览",
                  savePath: ret.savePath,
                  webUrl: ret.savePath
                }
              });
            } else {
              var docReader = api.require("docReader");
              docReader.open(
                {
                  path: ret.savePath,
                  autorotation: false
                },
                function(ret, err) {
                  console.log(JSON.stringify(ret));
                  console.log(JSON.stringify(err));
                }
              );
            }
            console.log("下载成功");
          } else {
            console.log("下载失败");
          }
        }
      );
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  overflow: hidden;
  width: 100vw;
  height: 100vh;
  background-color: #f2f4f9;
  .inner {
    height: calc(100% - 60px);
    padding: 10px;
    .titleBar {
      position: relative;
      padding: 6px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .leftContent {
        font-size: 16px;
        color: #1D2129;
        font-weight: bold;
        display: flex;
        align-items: center;
        .line {
          margin-right: 8px;
          display: inline-block;
          width: 4px;
          height: 16px;
          background-color: #29BEBC;
        }
      }
    }
    .detailInfo {
      max-height: calc(100% - 80px);
      overflow: auto;
      font-size: 16px;
      padding: 8px 16px;
      margin: 10px 0;
      background-color: #fff;
      border-radius: 4px;
      .imgSub {
        font-size: 14px;
        position: relative;
        padding: 10px 16px;
        display: flex;
        .img-label {
          width: 6.2em;
          margin-right: 12px;
          color: #646566;
          text-align: left;
        }
        .permit-upload {
          flex: 1;
        }
      }
      .imgSub::before {
        position: absolute;
        left: 8px;
        top: 12px;
        color: #ee0a24;
        font-size: 14px;
        content: '*';
      }
      .itemBar {
        display: flex;
        padding: 10px 16px;
        .item-label {
          width: 6.2em;
          flex: 1;
          font-size: 14px;
          color: #646566;
        }
        .itemList {
          font-size: 14px;
          width: calc(100% - 6.2em);
          >div {
            float: left;
            width: calc(50% - 5px) !important;
            height: 100px;
          }
          >div:nth-child(2n) {
            margin-left: 5px;
          }
          .fileItem {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            div {
              margin-top: 10px;
              width: 100%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
}
/deep/ .van-cell::after {
  display: none;
}
/deep/ .van-button {
  height: 28px;
  position: absolute;
  right: 0;
}
/deep/ .van-tabs__line {
  background-color: #29BEBC;
}
/deep/ .van-cascader__option--selected {
  color: #29BEBC;
}
.workClass {
  height: 100%;
  /deep/ .van-tabs {
    height: calc(100% - 48px);
    .van-tabs__content {
      height: calc(100% - 48px);
      .van-tab__pane-wrapper {
        overflow: auto;
      }
    }
  }
  /deep/ .van-tab__pane {
    overflow: auto;
    .van-cascader__options {
      height: auto;
      z-index: 9999;
    }
  }
}
</style>
