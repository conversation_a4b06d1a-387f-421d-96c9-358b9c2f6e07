<template>
  <div class="container">
    <Header title="安全管理填报" @backFun="goBack"> </Header>
    <van-tabs v-model="activeName" v-loading="pageLoading" sticky color="#29BEBC" @change="tabChange" offset-top="60px">
      <van-tab title="填报信息" name="a">
        <div class="inner">
          <van-field
            v-if="columns.length > 1"
            v-model="currentDept"
            required
            readonly
            label="填报科室"
            @click="deptShow = true">
            <template #right-icon>
              <van-icon name="arrow-down" />
            </template>
          </van-field>
          <van-field
            v-else
            v-model="currentDept"
            required
            readonly
            label="填报科室"
          />
          <van-field
            v-model="loginInfo.name"
            required
            readonly
            label="填报人姓名"
          />
          <van-field
            v-model="phone"
            required
            type="number"
            maxlength="11"
            label="联系电话"
          />
          <van-field
            v-model="work"
            required
            readonly
            label="安全管理工作"
            placeholder="选择安全管理工作类型/事项"
            @click="selectWork"
          />
          <van-field
            v-model="subParams.content"
            rows="2"
            autosize
            label="活动描述"
            type="textarea"
            maxlength="500"
            placeholder="请输入内容"
            show-word-limit
          />
          <div class="imgSub">
            <div class="img-label">图片支撑</div>
            <van-uploader
              class="permit-upload"
              v-model="imgList"
              multiple
              accept=".doc,.docx,.pdf,image/*"
              max-count="10"
              :after-read="afterImgRead"
            />
          </div>
          <van-field
            v-model="subParams.date"
            required
            readonly
            label="填报时间"
            @click="datePanelShow = true"
          />
        </div>
        <div class="btnWrap">
          <van-button color="#29BEBC" class="subBtn" @click="submit">提交</van-button>
        </div>
      </van-tab>
      <van-tab title="填报记录" name="b">
        <div class="listTab">
          <div class="time-box">
            <div class="start-time" @click="clickStart">
              <span v-if="startDate">{{ moment(startDate).format('YYYY-MM-DD') }}</span>
              <span v-else style="color:#86909C">请选择开始时间</span>
            </div>
            <span>&nbsp;&nbsp;至&nbsp;&nbsp;</span>
            <div class="end-time" @click="clickEnd">
              <span v-if="endDate">{{ moment(endDate).format('YYYY-MM-DD') }}</span>
              <span v-else style="color:#86909C">请选择结束时间</span>
            </div>
          </div>
        </div>
        <div class="filterParams">
          <div class="typeFilter" :class="workPanelShow ? 'check' : ''" @click="filterType">
            {{ workName.length ? workName.join('/') : '工作类型/事项' }}
            </div>
          <div class="resetBtn" @click="resetParams">重置</div>
        </div>
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" offset="100" :immediate-check="false">
          <div class="card" v-for="item in listData" :key="item.id" @click="goDetails(item.id)">
            <div class="itemList">
              <div>填报科室：</div>
              <div>{{ item.departmentName }}</div>
            </div>
            <div class="itemList">
              <div>填报人姓名：</div>
              <div>{{ item.informantName }}</div>
            </div>
            <div class="itemList">
              <div>联系电话：</div>
              <div>{{ item.phone }}</div>
            </div>
            <div class="itemList" style="align-items: baseline;">
              <div>安全管理工作：</div>
              <div>{{ item.jobCategoryName + '/' + item.managementIssueName }}</div>
            </div>
            <div class="itemList">
              <div>填报时间：</div>
              <div>{{ item.fillingTime }}</div>
            </div>
          </div>
        </van-list>
      </van-tab>
    </van-tabs>
    <!-- 选择填报工作类型 -->
    <van-popup v-model="workPanelShow" round :close-on-click-overlay="false" position="bottom" :lock-scroll="false" :style="{ height: '40%' }">
      <van-cascader
        class="workClass"
        v-model="currentWork"
        title="选择工作类型/事项"
        :options="optionList"
        :field-names="fieldNames"
        @close="workPanelShow = false"
        @finish="onFinish"
      />
    </van-popup>
    <!-- 选择填报时间 -->
    <van-popup v-model="datePanelShow" round :close-on-click-overlay="false" position="bottom" :style="{ height: '40%' }">
      <van-datetime-picker
        v-model="currentDate"
        type="datetime"
        :formatter="formatter"
        @confirm="dateComfirm"
        @cancel="datePanelShow = false"
      />
    </van-popup>
    <!-- 选择开始时间 -->
    <van-popup v-model="startDateShow" round :close-on-click-overlay="false" position="bottom" :style="{ height: '40%' }">
      <van-datetime-picker
        v-model="startDate"
        type="date"
        :formatter="formatter"
        @confirm="startDateComfirm"
        @cancel="startDateCancel"
      />
    </van-popup>
    <!-- 选择结束时间 -->
    <van-popup v-model="endDateShow" round :close-on-click-overlay="false" position="bottom" :style="{ height: '40%' }">
      <van-datetime-picker
        v-model="endDate"
        type="date"
        :formatter="formatter"
        :min-date="minDate"
        @confirm="endDateComfirm"
        @cancel="endDateCancel"
      />
    </van-popup>
    <!-- 选择科室 -->
    <van-popup v-model="deptShow" round :close-on-click-overlay="false" position="bottom" :style="{ height: '40%' }">
      <van-picker
        title="标题"
        show-toolbar
        :columns="columns"
        @confirm="deptConfirm"
        @cancel="deptShow = false"
      />
    </van-popup>
  </div>
</template>
<script>
import moment from 'moment'
export default {
  components: {},
  beforeRouteEnter(to, from, next) {
    if (from.name == 'safetyDetail') {
      to.query.active = 'b'
    }
    next()
  },
  data() {
    return {
      moment,
      loginInfo: {},
      activeName: 'a',
      phone: '',
      work: '',
      workName: [],
      workId: [],
      subParams: {
        content: '',
        date: moment().format('YYYY-MM-DD HH:mm')
      },
      imgList: [],
      datePanelShow: false,
      currentDate: new Date(),
      workPanelShow: false,
      currentWork: '',
      allOption: [],
      optionList: [],
      fieldNames: {
        text: 'classifyName',
        value: 'id',
        children: 'children',
      },
      showStartTimePicker: false,
      startTime: '',
      showEndTimePicker: false,
      endTime: '',
      loading: false,
      finished: false,
      pageNo: 0,
      pageSize: 10,
      listData: [],
      pageLoading: false,
      afterStartDate: '',
      startDate: '',
      startDateShow: false,
      afterEndDate: '',
      endDate: '',
      endDateShow: false,
      currentDept: '',
      currentDeptId: '',
      columns: [],
      deptShow: false,
      minDate: new Date()
    }
  },
  created() {
    this.timer = setInterval(() => {
      if (localStorage.getItem("loginInfo")) {
        this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
        if (this.$route.query.active == 'b') {
          this.activeName = 'b'
          this.pageNo = 0
          this.pageSize = 10
          this.loading = true
          this.finished = false
          this.listData = []
          this.startDate = ''
          this.endDate = ''
          this.onLoad()
        } else {
          this.phone = this.loginInfo.phone
          this.columns = this.loginInfo.officeName.split(',')
          this.currentDept = this.columns[0]
          this.currentDeptId = this.loginInfo.officeId.split(',')[0]
        }
        this.getClass()
        this.sysClickBack()
        clearInterval(this.timer);
      }
    }, 100);
  },
  mounted() {
    setTimeout(() => {
      // 相机权限
      const YBS = this.utils;
      if (!YBS.hasPermission("storage")) {
        let pageParam = {
          title: "存储权限使用说明",
          cont: "用于存储图片、视频等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["storage"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
              if (!YBS.hasPermission("camera")) {
              YBS.reqPermission(["camera"], function (ret) {
                if (ret && ret.list.length > 0 && ret.list[0].granted) {
                }
              });
              return;
            } else {
            }
          }
          });
        })
        return;
      }
      if (!YBS.hasPermission("camera")) {
        let pageParam = {
          title: "摄像机权限使用说明",
          cont: "用于扫描巡检码、空间码、拍照上传等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["camera"], function (ret) {
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
          }
        });
        })
        return;
      }
    }, 1000);
  },
  methods: {
    goBack() {
      api.closeFrame({})
    },
    tabChange(val) {
      this.work = ''
      this.currentWork = ''
      this.workName = []
      this.workId = []
      if (val == 'b') {
        this.pageNo = 0
        this.pageSize = 10
        this.loading = true
        this.finished = false
        this.listData = []
        this.startDate = ''
        this.endDate = ''
        this.onLoad()
      } else {
        this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
        this.phone = this.loginInfo.phone
        this.columns = this.loginInfo.officeName.split(',')
        this.currentDept = this.columns[0]
        this.currentDeptId = this.loginInfo.officeId.split(',')[0]
        this.imgList = []
        this.subParams = {
          content: '',
          date: moment().format('YYYY-MM-DD HH:mm')
        }
      }
    },
    getClass() {
      this.axios.postContralHostBase('getClassify', {}, res => {
        if (res.code == '200') {
          this.allOption = res.data
          this.optionList = this.utils.transData(
            res.data,
            "id",
            "parentId",
            "children"
          );
        } else {
          this.$message.error(res.message || '提交失败！')
        }
      })
    },
    onFinish({selectedOptions}) {
      if (selectedOptions.length == 2) {
        this.workPanelShow = false
        this.workId = []
        this.workName = []
        selectedOptions.forEach(option => this.workId.push(option.id))
        selectedOptions.forEach(option => this.workName.push(option.classifyName))
        this.work = selectedOptions.map(option => option.classifyName).join('/')
        if (this.activeName == 'b') {
          this.pageNo = 0
          this.pageSize = 10
          this.listData = []
          this.onLoad()
        }
      }
    },
    selectWork() {
      this.workPanelShow = true
    },
    dateComfirm(val) {
      this.subParams.date = moment(val).format('YYYY-MM-DD HH:mm')
      this.datePanelShow = false
    },
    clickStart() {
      this.afterStartDate = this.startDate
      this.startDate = new Date()
      this.startDateShow = true
    },
    deptConfirm(val) {
      this.currentDept = val
      this.currentDeptId = this.loginInfo.officeId.split(',')[this.columns.indexOf(this.currentDept)]
      this.deptShow = false
    },
    startDateComfirm(val) {
      this.startDate = val
      this.startDateShow = false
    },
    startDateCancel() {
      this.startDate = this.afterStartDate
      this.startDateShow = false
    },
    clickEnd() {
      if (this.startDate) {
        this.minDate = this.startDate
        this.afterEndDate = this.endDate
        this.endDate = new Date()
        this.endDateShow = true
      } else {
        return this.$toast("请选择开始时间");
      }
    },
    endDateComfirm(val) {
      this.endDate = val
      this.endDateShow = false
      this.listData = []
      this.finished = false;
      this.loading = true;
      this.pageNo = 1
      this.getList()
    },
    endDateCancel() {
      this.endDate = this.afterEndDate
      this.endDateShow = false
    },
    filterType() {
      this.workPanelShow = true
    },
    afterImgRead(file) {
      let params = {
        file: file.file
      };
      this.pageLoading = true
      this.axios.postContralHostBase("uploadImg", params, res => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          // 优先取全局设置的SSO域名，如果取不到则取上传后返回的临时SSO地址中的域名
          const downloadUrl = __PATH.DOWNLOAD_URL || new URL(data.temporaryUrl).origin + '/';
          this.imgList[this.imgList.length - 1].url = downloadUrl + data.fileKey;
          this.imgList[this.imgList.length - 1].fileName = data.fileName;
        }
        this.pageLoading = false
      });
    },
    formatter(type, val) {
      if (type === 'year') {
        return `${val}年`;
      } else if (type === 'month') {
        return `${val}月`;
      } else if (type === 'day') {
        return `${val}日`;
      } else if (type === 'hour') {
        return `${val}时`;
      } else if (type === 'minute') {
        return `${val}分`;
      }
      return val;
    },
    submit() {
      if (this.currentDept &&
        this.loginInfo.name &&
        this.phone &&
        this.work &&
        this.imgList.length > 0 &&
        this.subParams.date)
      {
        const imgArr = []
        this.imgList.forEach(i => imgArr.push({
          name: i.fileName,
          path: i.url
        }))
        const params = {
          departmentId: this.currentDeptId,
          departmentName: this.currentDept,
          informantId: this.loginInfo.id,
          informantName: this.loginInfo.name,
          phone: this.phone,
          jobCategoryId: this.workId[0],
          jobCategoryName: this.workName[0],
          managementIssueId: this.workId[1],
          managementIssueName: this.workName[1],
          activityDescription: this.subParams.content,
          pictureUrl: JSON.stringify(imgArr),
          fillingTime: this.subParams.date,
          userId: this.loginInfo.id,
          userName: this.loginInfo.name,
          integral: String(this.allOption.find(i => i.id == this.workId[1]).integral)
        }
        console.log(params)
        this.pageLoading = true
        this.axios.postContralHostBase('saveSafetyManagement', params, res => {
          if (res.code == '200') {
            this.$router.push('/complete')
          } else {
            this.$message.error(res.message || '提交失败！')
            this.pageLoading = false
          }
        })
      } else {
        if (!this.currentDept) {
          return this.$toast("请选择填报科室");
        }
        if (!this.loginInfo.name) {
          return this.$toast("请选择填报人姓名");
        }
        if (!this.phone) {
          return this.$toast("请输入联系电话");
        }
        if (!this.work) {
          return this.$toast("请选择安全管理工作");
        }
        if (this.imgList.length == 0) {
          return this.$toast("请上传图片支撑");
        }
        if (!this.subParams.date) {
          return this.$toast("请选择填报时间");
        }
      }
    },
    onLoad() {
      this.finished = false;
      this.loading = true;
      this.pageNo++;
      this.getList();
    },
    getList() {
      const params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        jobCategoryId: this.workId[0] || '',
        managementIssueId: this.workId[1] || '',
        startTime: this.startDate ? moment(this.startDate).format('YYYY-MM-DD 00:00:00') : '',
        endTime: this.endDate ? moment(this.endDate).format('YYYY-MM-DD 23:59:59') : ''
      }
      this.axios.postContralHostBase('getSafetyManagementList', params, res => {
        if (res.code == '200') {
          this.listData = this.listData.concat(res.data.list)
          this.finished = this.listData.length >= res.data.total
          this.loading = false
        } else {
          this.$message.error(res.message || '获取列表失败！')
        }
      })
    },
    goDetails(id) {
      this.$router.push({
        path: '/safetyDetail',
        query: {
          id
        }
      })
    },
    // 点击按钮返回
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    resetParams() {
      this.work = ''
      this.workId = []
      this.workName = []
      this.currentWork = ''
      this.pageNo = 0
      this.pageSize = 10
      this.loading = true
      this.finished = false
      this.listData = []
      this.startDate = ''
      this.endDate = ''
      this.onLoad()
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  overflow: hidden;
  width: 100vw;
  height: 100vh;
  background-color: #f2f4f9;
  /deep/ .van-tabs {
    height: calc(100% - 60px);
    .van-tabs__content {
      height: calc(100% - 54px);
      .van-tab__pane {
        height: 100%;
        .van-cascader__option {
          > span {
            max-width: 70%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
    .van-cell::after {
      display: none;
    }
    .inner {
      max-height: calc(100% - 90px);
      overflow: auto;
      font-size: 16px;
      padding: 8px 16px;
      margin: 10px;
      background-color: #fff;
      border-radius: 4px;
      .imgSub {
        font-size: 14px;
        position: relative;
        padding: 10px 16px;
        display: flex;
        .img-label {
          width: 6.2em;
          margin-right: 12px;
          color: #646566;
          text-align: left;
        }
        .permit-upload {
          flex: 1;
        }
      }
      .imgSub::before {
        position: absolute;
        left: 8px;
        top: 12px;
        color: #ee0a24;
        font-size: 14px;
        content: '*';
      }
    }
    .btnWrap {
      position: absolute;
      width: 100%;
      height: 60px;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      .subBtn {
        background-color: #29BEBC;
        color: #fff;
        text-align: center;
        font-size: 15px;
        width: 50%;
        height: 40px;
        line-height: 40px;
        border-radius: 4px;
      }
    }
    .listTab {
      padding: 10px 16px;
      background-color: #fff;
      font-size: 14px;
      display: flex;
      align-items: center;
      .time-box {
        width: 100%;
        display: flex;
        justify-content: space-between;
        position: relative;
        align-items: center;
        > div {
          width: 50%;
          height: 35px;
          background-color: #f2f3f5;
          font-size: 14px;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #1d2129;
          border-radius: 4px;
        }
      }
    }
    .filterParams {
      background-color: #fff;
      padding: 0 16px 10px 16px;
      font-size: 14px;
      display: flex;
      .typeFilter {
        width: 70%;
        text-align: center;
        position: relative;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .resetBtn {
        width: 30%;
        text-align: center;
      }
      .typeFilter::after {
        position: absolute;
        top: 50%;
        margin-top: -5px;
        border: 3px solid;
        border-color: transparent transparent #dcdee0 #dcdee0;
        -webkit-transform: rotate(-45deg);
        transform: rotate(-45deg);
        opacity: 0.8;
        content: '';
      }
      .check {
        color: #29BEBC;
      }
      .check::after {
        margin-top: -1px;
        -webkit-transform: rotate(135deg);
        transform: rotate(135deg);
      }
    }
    .van-list {
      padding: 0 10px;
      padding-top: 12px;
      height: calc(100% - 84px);
      overflow: auto;
      .card {
        background-color: #fff;
        border-radius: 8px;
        font-size: 16px;
        padding: 12px;
        margin-bottom: 12px;
        .itemList {
          display: flex;
          align-items: center;
        }
        .itemList:first-child {
          align-items: normal;
        }
        div > div:nth-child(1) {
          color: #4e5969;
          width: 120px;
        }
        div > div:nth-child(2) {
          color: #1d2129;
          width: calc(100% - 120px);
          white-space: normal;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .card > div {
        margin-bottom: 10px;
      }
    }
  }
}
/deep/ .van-picker__cancel {
  font-size: 16px;
}
/deep/ .van-picker__confirm {
  font-size: 16px;
}
/deep/ .van-tabs__line {
  background-color: #29BEBC;
}
/deep/ .van-cascader__option--selected {
  color: #29BEBC;
}
/deep/ .van-cascader {
  .van-tab__text {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .van-cascader__option {
    >span {
      max-width: 70%;
    }
  }
}
.workClass {
  height: 100%;
  /deep/ .van-tab__pane {
    overflow: auto;
    .van-cascader__options {
      height: auto;
      z-index: 9999;
    }
  }
}
</style>
