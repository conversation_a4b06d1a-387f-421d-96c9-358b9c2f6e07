<template>
  <div class="container">
    <Header title="安全管理填报" leftText="完成" @backFun="goBack"> </Header>
    <div class="inner">
      <div style="text-align: center;">
        <img src="@/assets/images/success.png" alt="">
        <div style="margin: 10px 0;">提交成功</div>
        <div>在“填报记录”中可查看登记历史</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {}
  },
  created() {
    this.sysClickBack()
  },
  methods: {
    // 点击按钮返回
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  width: 100vw;
  height: 100vh;
  .inner {
    width: 100%;
    height: calc(100% - 60px);
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 30%;
    }
  }
}
</style>