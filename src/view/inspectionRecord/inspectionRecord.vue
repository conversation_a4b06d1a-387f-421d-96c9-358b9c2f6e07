<template>
  <div class="container">
    <van-overlay :show="overlayShow">
      <van-loading type="spinner" vertical>加载中...</van-loading>
    </van-overlay>
    <Header title="巡检记录" @backFun="goBack"> </Header>
    <!-- <top-nav title="巡检任务"></top-nav> -->
    <van-tabs
      v-model="active"
      sticky
      offset-top="60px"
      color="#29BEBC"
      line-width="50"
      title-active-color="#29BEBC"
      @change="onChange"
      swipe-threshold="4"
      ellipsis
    >
      <van-tab v-for="item in tasksList" :title="item.dictName" :key="item.id">
        <van-empty description="暂无数据" v-if="!item.details" />
        <van-pull-refresh
          v-model="refreshing"
          @refresh="onRefresh"
          success-text="刷新成功"
          v-else
        >
          <div class="list" v-for="i in item.details" :key="i.id" @click="goTaskDetail(i)">
            <span class="title">
              <span class="taskType">{{ cycleTypeFn(i.cycleType) }}</span>
              <span style="margin: 0 8px 0 5px">任务名称:</span>
              <div class="title-status">
                <span style="flex:1;text-overflow: ellipsis;overflow:hidden;">{{ i.taskName }}</span>
                <span 
                  :class="i.taskStatus == '2' ? 'fnish-text' : 'unfinished-text'"
                  style="flex: 0;font-weight: normal"
                >{{ i.taskStatus == '2' ? '已完成' : '未完成' }}</span>
              </div>
            </span>
            <div class="finish">
              <span>巡检点:</span>
              <span>{{ i.planCount }}</span>
            </div>
            <div class="time">
              <span>巡检时间:</span>
              <span
                >{{ i.taskStartTime }}至{{ i.taskEndTime }}</span
              >
            </div>
          </div>
          <div class="bottom-tips">
            <div v-if="finished">加载中<img style="vertical-align:middle;" src="../../assets/images/加载.png"></div>
            <div v-else>暂无更多</div>
          </div>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import moment from 'moment'
import topNav from "../components/topNav.vue";
export default {
  components: {
    topNav,
  },
  data() {
    return {
      active: 0,
      refreshing: false,
      cycleTypeList: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        },
      ], 
      tasksList: [],
      overlayShow: false,
      scanFlag: "",
      from: "",
      taskData: "",
      loginInfo: "",
      typeValue: "",
      giveUpLocation: "",
      ibeaconArr: [],
      locationData: "",
      isLocation: false,
      finished: true,
      params: {
        accomplishType: 3,
        pageNo: 1,
        pageSize: 10,
        planTypeId: ''
      },
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
    };
  },
  created() {
    this.getPlanType()
    apiready = () => {
      // if (!localStorage.getItem("loginInfo")) {
        var userInfo = api.getPrefs({
          sync: true,
          key: "userInfo"
        });
        userInfo = JSON.parse(userInfo);
        userInfo.hospitalCode = api.pageParam.hospitalCode
        userInfo.hospitalName = api.pageParam.hospitalName
        if (userInfo.id) {
          const virtualToken = encodeURIComponent(userInfo.hospitalName);
          localStorage.setItem('token', virtualToken);
          localStorage.setItem("loginInfo", JSON.stringify(userInfo));
        }
      // }
      this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
      this.active = Number(this.$route.query.active) || 0;
      this.from = this.$route.query.from || "";
      this.taskData = this.$route.query.taskData || "";
      this.getPlanType()
      this.sysClickBack()
    }
    if (JSON.parse(localStorage.getItem("loginInfo")).isHospital == 0) {
      var userInfo = api.getPrefs({
        sync: true,
        key: "userInfo"
      });
      userInfo = JSON.parse(userInfo);
      userInfo.hospitalCode = api.pageParam.hospitalCode
      userInfo.hospitalName = api.pageParam.hospitalName
      if (userInfo.id) {
        const virtualToken = encodeURIComponent(userInfo.hospitalName);
        localStorage.setItem('token', virtualToken);
        localStorage.setItem("loginInfo", JSON.stringify(userInfo));
      }
    }
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.active = Number(this.$route.query.active) || 0;
    this.from = this.$route.query.from || "";
    this.taskData = this.$route.query.taskData || "";
  },
  mounted() {
    this.giveUpLocation = sessionStorage.getItem("giveUpLocation") || "false";
    this.ibeaconArr = JSON.parse(sessionStorage.getItem("ibeaconArr")) || [];
    window.onscroll = this.isscroll
    this.sysClickBack()
  },
  methods: {
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    onRefresh() {
      this.params.pageNo = 1
      this.params.pageSize = 10
      this.finished = true
      this.getData()
    },
    onChange (val) {
      this.params.planTypeId = this.tasksList[val].id
      this.params.pageNo = 1
      this.params.pageSize = 10
      this.active = val
      this.finished = true
      this.getData()
    },
    async goTaskDetail(data) {
      sessionStorage.setItem('taskInfo',JSON.stringify(data))
      this.$router.push({
        path: 'taskDetail',
        query: {
          taskInfo: data,
          active: this.active,
          from: 'inspectionRecord'
        }
      })
    },
    getPlanType () {
      const params = {
        pageNo: 1,
        pageSize: 99999,
        dictType: "plan_type"
      }
      this.axios.postContralHostBase("getPlanType", params, res => {
        if (res.code == '200') {
          this.tasksList = res.data.list
          console.log(res.data.list)
          this.params.planTypeId = this.tasksList[this.active].id
          this.getData()
        }
      })
    },
    async getData(type) {
      this.axios.postContralHostBase("getInspectionTaskList",this.params, res => {
        this.$nextTick(() => {
          if (res.code == '200') {
            if (type == 'pull') {
              res.data.forEach(i => {
                this.tasksList[this.active].details.push(i)
              })
            } else {
              this.tasksList[this.active].details = res.data
            }
            if (res.data.length == 0 || this.tasksList[this.active].details.length == res.data[0].sumTask) {
              console.log('满足停止请求条件')
              this.finished = false
            }
            this.refreshing = false
          }
        })
        this.$forceUpdate();
      })
    },
    isscroll () {
      let scrollY = document.documentElement.scrollTop || document.body.scrollTop
      let vh = document.documentElement.clientHeight;
      const scrollHeight = document.documentElement.scrollHeight
      if (scrollY + vh >= scrollHeight && this.finished) {
        console.log('满足加载条件')
        ++this.params.pageNo
        this.thorttle(this.getData('pull'))
      }
    },
    // 节流
    thorttle (fn) {
      let timer = null
      return () => {
        if (!timer) {
          timer = setTimeout(() => {
            fn.call(this)
            timer = null
          }, 1000)
        }
      }
    },
    goBack() {
      api.closeFrame({});
    },
    cycleTypeFn(row) {
      const item = this.cycleTypeList.find(i => i.cycleType == row)
      return item.label
    }
  },
  filters: {
    transDate(val) {
      return val.split(" ")[0];
    },
  },
};
</script>

<style scoped>
.container {
  width: 100vw;
  /* background-color: #f5f6fb; */
  background-color: #fff;
}
>>> .van-tab__text {
  font-size: 15px;
}
>>> .van-tabs__wrap {
  border-bottom: solid 5px #e6eaf0;
}
.van-list {
  background-color: #fff;
}
>>> .van-tabs__content {
  /* margin-top: 10px; */
}
.list {
  border-bottom: 5px solid #e6eaf0;
  font-size: 16px;
  position: relative;
  background-color: #fff;
}
.list > .title {
  /* display: inline-block; */
  display: flex;
  align-items: center;
  color: #353535;
  font-weight: 600;
  width: 100%;
  margin: 0 0 12px 0;
  padding: 14px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border-bottom: 1px solid #e6eaf0 ;
}
.taskType {
  display: inline-block;
  width: 45px;
  height: 19px;
  /* background-color: #F2F2F2; */
  background-color: #e6eaf0;
  border-radius: 10px;
  font-size: 14px;
  -webkit-transform: scale(0.8);
  color: #688DFF;
  text-align: center;
  line-height: 19px;
  font-weight: normal;
}
.list > div {
  margin-bottom: 12px;
  padding: 0 0 0 25px;
  display: flex;
}
.list > div span:nth-child(1) {
  width: 65px;
  font-weight: 400;
  font-size: 15px;
}
.list > div span:nth-child(2) {
  font-weight: 400;
  font-size: 14px;
  color: #888888;
  padding-left: 5px;
}
.current-time span:nth-child(1) {
  line-height: 16px;
}
.current-time span:nth-child(2) {
  display: flex;
  align-items: center;
}
.cycle {
  position: absolute;
  right: 16px;
  top: 14px;
  font-size: 14px;
  color: #29bebc;
}
.finish {
  align-items: center;
}
.van-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9;
}
.van-overlay {
  z-index: 3;
}
.van-empty {
  width: 100vw;
  height: 88vh;
}
>>>.van-pull-refresh__track {
  min-height: 80vh;
}
.title-status {
  display: flex;
  align-items: center;
  width: 60%;
  justify-content: space-between;
}
.fnish-text {
  color: #29bebc;
}
.unfinished-text {
  color: #ff2836;
}
.bottom-tips {
  font-size: 12px;
  text-align: center;
  color: #969799;
}
</style>