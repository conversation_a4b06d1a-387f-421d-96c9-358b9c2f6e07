<template>
  <div class="container">
    <Header title="隐患分析" @backFun="goBack"> </Header>
    <div class="nav">
      <div class="btns">
        <div
          :class="['btn', dateType == '1' ? 'active-btn' : '']"
          @click="changeBtn('1')"
        >
          本年
        </div>
        <div
          :class="['btn', dateType == '2' ? 'active-btn' : '']"
          @click="changeBtn('2')"
        >
          本月
        </div>
        <div
          :class="['btn', dateType == '0' ? 'active-btn' : '']"
          @click="changeBtn('0')"
        >
          自定义
        </div>
      </div>
    </div>
    <van-popup v-model="show" title="请选择时间区间" closeable round>
      <div style="width: 70vw;height:30vh">
        <div class="time-text">
          <div class="title">
            <span>请选择时间区间</span>
          </div>
          <div class="items">
            <span>开始时间：</span>
            <van-field
              class="date-picker"
              placeholder="点击选择时间"
              type="date"
              v-model="startTime"
              :value="startTime"
              center
              :clearable="false"
              is-link
              use-button-slot
            />
          </div>
          <div class="items">
            <span>结束时间：</span>
            <van-field
              class="date-picker"
              placeholder="点击选择时间"
              type="date"
              v-model="endTime"
              :value="startTime"
              center
              :clearable="false"
              is-link
              use-button-slot
            />
          </div>
          <van-button style="height:30px" type="info" @click="getData">查询</van-button>
        </div>
      </div>
    </van-popup>
    
    <div id="myChart" style="width: 100vw; height: 40vh;border-bottom: 1px solid #CCC4C4"></div>
    <div style="padding: 10px 8px">
      <div style="padding-bottom: 10px;width:100%;text-align: center">隐患类型分析</div>
      <Table :type="2" :option="option1" :tableData="tableData1"></Table>
    </div>
    <div style="padding: 10px 8px">
      <div style="padding-bottom: 10px;width:100%;text-align: center">隐患分布</div>
      <Table :type="2" :option="option2" :tableData="tableData2"></Table>
    </div>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
import taskList from "../analysis/list.vue";
import Table from '../analysis/table.vue';
import moment from 'moment'
export default {
  components: {
    topNav,
    taskList,
    Table
  },
  data() {
    return {
      show: false,
      echartData: [],
      listData: [],
      dateType: "1",
      visible: false,
      tableData1: [],
      tableData2: [],
      //th
      option1: {
        column: [
          {
            label: '类型',
            tableDataprop: 'questionTypeName',
          },
          {
            label: '数量',
            tableDataprop: 'count'
          },
          {
            label: '百分比',
            tableDataprop: 'percentage'
          }
        ]
      },
      option2: {
        column: [
          {
            label: '单位',
            tableDataprop: 'hospitalName',
          },
          {
            label: '数量',
            tableDataprop: 'count'
          },
          {
            label: '百分比',
            tableDataprop: 'percentage'
          }
        ]
      },
      timeInterval: [],
      currentDate: new Date(),
      startTime: '',
      endTime: '',
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
    };
  },
  created() {
    this.getTableData()
    apiready = () => {
      this.sysClickBack()
      if (!localStorage.getItem("loginInfo")) {
        var userInfo = api.getPrefs({
          sync: true,
          key: "userInfo"
        });
        userInfo = JSON.parse(userInfo);
        userInfo.hospitalCode = api.pageParam.hospitalCode
        userInfo.hospitalName = api.pageParam.hospitalName
        if (userInfo.id) {
          const virtualToken = encodeURIComponent(userInfo.hospitalName);
          localStorage.setItem('token', virtualToken);
          localStorage.setItem("loginInfo", JSON.stringify(userInfo));
        }
      }
      this.getTableData();
    }
  },
  mounted() {
    setTimeout(() => {
      this.sysClickBack()
    }, 1000)
  },
  methods: {
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    initChart() {
      const optionData = []
      this.echartData.questionStateAnalyse.forEach(i => {
        const item = {}
        item.name = i.flowName
        item.value = parseInt(i.percentage)
        item.count = i.count
        optionData.push(item)
      })
      let myChart = this.$echarts.init(document.getElementById("myChart"));
      myChart.setOption({
        title: {
          text: '隐患状态分析',
          left:'30%',
          top: '5%'
        },
        backgroundColor: "#fff",
        color: ["#61A5E8", "#7ECF51","#EECB5F","#E3935D","#E16757","#E16757"],
        series: [
          {
            type: "pie",
            radius: "60%",
            center: ["30%", "50%"],
            data: optionData,
            hoverAnimation: false,
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 1,
              labelLine: {
                length: 20,
                length2: 16,
                show: true,
                lineStyle: {
                  color: "#ccc",
                },
              },
            },
            label: {
              show: false
            },
          },
        ],
        legend: {
          icon: 'circle',
          itemGap: 15,
          itemWidth: 10,
          itemHeight: 10,
          right: '6%',
          top: '25%',
          textStyle: {
            color: "#656565"
          },
          orient: 'vertical',
          formatter: name => {
            const count = this.echartData.questionStateAnalyse.find(i => i.flowName == name)
            return name + '(' + count.count + '件) ' + ' ' + '  ' + count.percentage
          }
        }
      });
      this.tableData1 = this.echartData.questionTypeAnalyse
      this.tableData2 = this.echartData.questionDistributeAnalyse
    },
    getTableData() {
      let data = {
        dateType: this.dateType,
        startTime: this.startTime != '' ? moment(this.startTime).format('YYYY-MM-DD') : '',
        endTime: this.endTime != '' ? moment(this.endTime).format('YYYY-MM-DD') : ''
      };
      this.axios.postContralHostBase("getDangeranAlysis", data, (res) => {
        if (res.code == '200') {
          this.echartData = res.data
          this.initChart();
        }
      })
    },
    changeBtn(val) {
      this.dateType = val;
      if (val == '0') {
        this.show = true
      } else {
        this.getTableData();
      }
    },
    goBack () {
      api.closeFrame({});
    },
    onClose() {
      this.show = false
      this.timeInterval = []
    },
    getData() {
      this.getTableData()
      this.show = false
    }
  },
};
</script>

<style scoped>
.container {
  width: 100vw;
  background-color: #fff;
  padding-bottom: 5vh;
}
.nav {
  /* display: flex; */
  align-items: center;
  /* justify-content: space-between; */
  /* height: 60px; */
  background-color: #fff;
  /* padding: 0 16px; */
}
.nav{
  padding: 0 16px;
  font-size: 16px;
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid #CCC4C4;
  color: #353535;
  font-weight: 600;
}
.btns {
  padding: 0 16px;
  margin: 10px 0 0 0;
  display: flex;
  width: 52%;
}
.btns .btn {
  width: 50px;
  height: 26px;
  border: 1px solid #797979;
  color: #333333;
  font-size: 13px;
  text-align: center;
  line-height: 26px;
}
.btn:nth-child(2) {
  border-left: none;
}
.btn:nth-child(3) {
  border-left: none;
}
.active-btn {
  background-color: #29bebc;
  color: #fff !important;
}
.time-text {
  font-size: 13px;
  margin: 0 auto;
  text-align: center;
}
.items {
  padding: 5px 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.title {
  padding: 10px 0;
  text-align: center;
  font-size: 16px;
  font-weight: 600px;
}
>>>.van-cell {
  width: auto;
}
.date-picker {
  padding: 10px 8px;
  min-height: 24px;
  width: 60%;
}
</style>