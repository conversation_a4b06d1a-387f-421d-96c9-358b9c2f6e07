<template>
  <div class='content'>
    <Header title="隐患知识库" @backFun="goBack"> </Header>
    <van-search v-model="searchValue" placeholder="隐患描述/整改建议" />
    <div class="inner">
      <van-pull-refresh
        v-model="refreshing"
        pulling-text="下拉刷新..."
        @refresh="onRefresh"
      >
        <van-list
          v-if="listData.length"
          v-model="loading"
          :finished="listFinished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            class="listWrap"
            v-for="(item, index) in listData"
            :key="index"
            @click="$route.query.type && $route.query.type == 'pro' ? clickItem(index) : ''">
            <div class="titleItem">
              <span>隐患等级</span>
              <span>{{ item.questionLevelName }}</span>
            </div>
            <div class="contentItem">
              <span>隐患描述：</span>
              <div>{{ item.questionDescription }}</div>
            </div>
            <div class="contentItem">
              <span>整改建议：</span>
              <div>{{ item.rectifySuggestion }}</div>
            </div>
            <div class="titleItem" style="margin-top: 10px;">
              <span>适用行业：{{ item.applicableIndustryName }}</span>
              <span>依据强度：{{ item.basisIntensityName }}</span>
            </div>
            <div v-if="subSign === 'sub' + index" class="subWrap">
              <van-button color="#00cac8" @click="dangerReported(item)">提交隐患</van-button>
            </div>
          </div>
        </van-list>
        <van-empty v-else description="暂无更多" />
      </van-pull-refresh>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      searchValue: '',
      refreshing: false,
      loading: false,
      listFinished: false,
      listData: [],
      params : {
        pageNo: 1,
        pageSize: 10,
        questionLevelId: '', // 隐患等级id
        descriptionOrSuggestion: '', // 隐患描述/整改建议
        applicableIndustryId: '', // 适用行业id
        basisIntensityId: '' // 依据强度id
      },
      subSign: ''
    }
  },
  created() {
    this.getDataList()
  },
  mounted() {
    this.sysClickBack()
  },
  watch: {
    searchValue(newVal, oldVal) {
      this.params.pageNo = 1
      this.listData = []
      this.getDataList()
    }
  },
  methods: {
    // 点击按钮返回
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    goBack() {
      api.closeFrame({});
    },
    getDataList() {
      this.params.descriptionOrSuggestion = this.searchValue
      this.axios.postContralHostBase("repositoryList", this.params, (res) => {
        if (res.code == "200") {
          this.$nextTick(() => {
            this.listData = this.listData.concat(res.data.list)
            if (res.data.list.length < 10) {
              this.listFinished = true
            } else {
              this.listFinished = false
            }
            this.refreshing = false
            this.loading = false
          })
        }
      });
    },
    onRefresh() {
      this.params.pageNo = 1
      this.loading = true
      this.listData = []
      this.getDataList()
    },
    onLoad() {
      this.params.pageNo++
      this.listFinished = false
      this.loading = true
      this.getDataList()
    },
    clickItem(index) {
      if (this.subSign) {
        this.subSign = ''
      } else {
        this.subSign = 'sub' + index
      }
    },
    dangerReported(item) {
      api.openWin({
        name: 'imas/snapshotPro',
        url: 'widget://html/common_window.html',
        bgColor: 'rgba(250, 250, 250, 0)',
        hideHomeIndicator: true,
        bounces: false,
        scrollEnabled: false,
        useWKWebView: true,
        pageParam: {
          title: "隐患上报",
          questionDescription: item.questionDescription,
          fromPage: 'repository'
        }
      });
    }
  }
}
</script>
<style lang="scss" scoped>
  .content {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    font-size: 16px;
    .inner {
      height: calc(100vh - 114px);
      width: calc(100vw - 20px);
      padding: 0 10px;
      background-color: #F2F4F9;
      overflow: auto;
      .listWrap {
        margin-top: 10px;
        padding: 16px;
        border-radius: 8px;
        background-color: #fff;
        .titleItem {
          display: flex;
          justify-content: space-between;
        }
        .contentItem {
          display: flex;
          margin-top: 10px;
          span {
            width: 80px;
            color: #4E5969;
          }
          div {
            width: calc(100% - 80px);
            color: #1D2129;
          }
        }
        .subWrap {
          padding-top: 10px;
          text-align: center;
          /deep/ .van-button {
            height: 38px;
            padding: 0 20px;
          }
        }
      }
      /deep/ .van-empty {
        background-color: #fff;
        height: calc(100vh - 124px);
      }
    }
  }
</style>