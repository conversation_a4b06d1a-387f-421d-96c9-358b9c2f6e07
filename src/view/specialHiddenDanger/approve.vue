<template>
  <div>
    <Header title="专项审批" @backFun="backFn"></Header>
    <div class="content">
      <div class="item">
        <span>审批时间</span>
        <span>{{ currentDate }}</span>
      </div>
      <div class="item">
        <span>审批结果</span>
        <van-radio-group v-model="submitform.auditResultCode" direction="horizontal">
          <van-radio name="1" checked-color="#3ecac7">通过</van-radio>
          <van-radio name="2" checked-color="#3ecac7">驳回</van-radio>
        </van-radio-group>
      </div>
      <div class="item2">
        <span>审批意见</span>
        <van-field
          v-model="submitform.auditOpinion"
          rows="1"
          autosize
          type="textarea"
          maxlength="500"
          placeholder="请输入审核意见，字数限制500字以内"
          show-word-limit
          class="message"
        />
      </div>
      <div class="item2">
        <div class="explain" style="justify-content: space-between">
          <span style="color: #333">附件</span>
          <p class="upload-img" style="color: #646566">
            注：上传本地文件，最多上传三张
          </p>
        </div>
        <div style="width: 95%; margin: 0 auto">
          <van-uploader
            :max-size="maxSize * 1024 * 1024"
            @oversize="onOversize"
            ref="uplodImg"
            accept=""
            v-model="fileList"
            multiple
            :after-read="afterRead"
            @delete="deleteImg"
            :max-count="3"
          />
        </div>
      </div>
    </div>
    <div class="footer">
      <van-button type="primary" color="#3ecac7" @click="approveSubmit">确定审批</van-button>
    </div>
  </div>
</template>

<script>
import YBS from "../../centralControl/utils/utils.js";
import moment from "moment";
export default {
  components: {},
  data() {
    return {
      submitform: {
        auditOpinion: "",
        auditResultCode: "",
        attachmentUrl: []
      },
      maxSize: 20,
      fileList: [],
      currentDate: "",
      hospitalCode: ""
    };
  },
  mounted() {
    setTimeout(() => {
      this.power();
    }, 1000);
  },
  created() {
    this.currentDate = moment().format("YYYY-MM-DD HH:mm");
    this.id = this.$route.query.id;
    this.hospitalCode = this.$route.query.hospitalCode;
    apiready = () => {
      var userInfo = api.getPrefs({
        sync: true,
        key: "userInfo"
      });
      userInfo = JSON.parse(userInfo);
      if (userInfo.id) {
        const virtualToken = encodeURIComponent(userInfo.hospitalName);
        localStorage.setItem("token", virtualToken);
        localStorage.setItem("loginInfo", JSON.stringify(userInfo));
      }
      this.sysClickBack();
    };
  },
  methods: {
    power() {
      if (!YBS.hasPermission("storage")) {
        let pageParam = {
          title: "存储权限使用说明",
          cont: "用于存储图片、视频等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["storage"], function(ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
              if (!YBS.hasPermission("camera")) {
              YBS.reqPermission(["camera"], function(ret) {
                if (ret && ret.list.length > 0 && ret.list[0].granted) {
                }
              });
              return;
              }
            }
          });
        })
        return;
      }
      if (!YBS.hasPermission("camera")) {
        let pageParam = {
          title: "摄像机权限使用说明",
          cont: "用于扫描巡检码、空间码、拍照上传等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["camera"], function(ret) {
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
          }
          });
        })
        return;
      }
    },
    sysClickBack() {
      api.addEventListener(
        {
          name: "keyback666"
        },
        (ret, err) => {
          this.backFn();
        }
      );
    },
    backFn() {
      api.closeFrame({});
      api.clearCache(function() {});
    },
    onOversize() {
      this.$toast.fail(`文件大小不能超过${this.maxSize}M`);
    },
    afterRead(files) {
      this.fileList.forEach(i => {
        return (i.status = "uploading");
      });
      const params = {
        file: ""
      };
      if (files.length) {
        let formData = new FormData();
        files.forEach(item => {
          formData.append("file", item.file);
        });
        formData.append("hospitalCode", this.hospitalCode);
        axios
          .post(__PATH.BASEURL + "file/upload", formData)
          .then(res => {
            if (res.data.code == 200) {
              this.fileList.forEach(i => {
                return (i.status = "done");
              });
              const imgUrl = res.data.data.fileKey.split(",");
              imgUrl.forEach((i, index) => {
                const item = {
                  name: files[index].file.name,
                  fileKey: i
                };
                this.submitform.attachmentUrl.push(item);
                this.fileList.push(item);
              });
            }
          })
          .catch(() => {
            this.fileList.forEach(i => {
              return (i.status = "failed");
            });
            this.$toast.fail("上传失败");
          });
      } else {
        params.file = files.file;
        this.subImg(params);
      }
    },
    subImg(params) {
      params.hospitalCode = this.hospitalCode;
      this.axios.postContralHostBase("uploadImg", params, res => {
        if ((res.code = "200")) {
          const item = {
            name: params.file.name,
            url: res.data.fileKey
          };
          // this.form.fileList.push(item);
          this.submitform.attachmentUrl.push(item);
          console.log(this.submitform.attachmentUrl, " this.attachmentUrl");
          this.fileList.forEach(i => {
            return (i.status = "done");
          });
        }
      });
    },
    //删除图片
    deleteImg(e) {
      console.log(e, "eee");
      this.submitform.attachmentUrl = this.submitform.attachmentUrl.filter(i => i.name != e.file.name);
    },
    approveSubmit() {
      if (!this.submitform.auditResultCode) {
        this.$toast.fail("请选择审批结果");
        return;
      }
      if (!this.submitform.auditOpinion) {
        this.$toast.fail("请输入审批意见");
        return;
      }
      let params = {
        specialRectificationId: this.id,
        auditType: "0",
        auditResultCode: this.submitform.auditResultCode,
        auditOpinion: this.submitform.auditOpinion,
        auditAttachmentArr: JSON.stringify(this.submitform.attachmentUrl)
      };
      this.axios.postContralHostBase("auditSpecialHiddenDanger", params, res => {
        if ((res.code = "200")) {
          this.$toast.success("审批成功");
          setTimeout(() => {
            this.$router.go(-2);
          }, 1000);
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
@import "../../assets/stylus/theme";
.content {
  padding: 0 16px;
  padding-top: 16px;
}
.message {
  background-color: #f4f5f9;
  margin-top: 0.3125rem;
}
.item {
  font-size: 16px;
  color: #333;
  display: flex;
  justify-content: space-between;
  height: 30px;
}
.item2 {
  color: #333;
  font-size: 16px;
  margin-bottom: 16px;
}
.explain {
  background-color: #fff;
  min-height: 48px;
  display: flex;
  align-items: center;
}
.footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  height: 10vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
.van-button {
  width: 200px;
  font-size: 16px;
}
</style>
