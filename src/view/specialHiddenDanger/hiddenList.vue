<template>
  <div>
    <van-pull-refresh v-model="overlayShow" @refresh="onRefresh" v-if="!showHidden">
      <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoads" :immediate-check="false" :offset="5">
        <div>
          <div v-for="item in accidentList" :key="item.id" class="list" @click="accident(item)">
            <div class="apellation">
              <i>{{ item.projectName }}</i
              ><i>{{ item.statusName }}</i>
            </div>
            <div class="txt">
              <div>
                申报医院：<span>{{ item.hospitalName }}</span>
              </div>
              <div>
                申报部门：<span>{{ item.declareDeptName }}</span>
              </div>

              <div>
                &emsp;联系人：<span>{{ item.contactPerson }}&emsp;{{ item.contactNumber }}</span>
              </div>
              <div>
                申报时间：<span class="scene">{{ item.createTime }}</span>
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
    <!-- <subclass2 v-if="showHidden" :rowList="rowList" v-on:button="button($event)" class="top1"></subclass2> -->
  </div>
</template>

<script>
// import subclass2 from "./subclass2.vue";
export default {
  props: ["statusCode"],
  components: {
    // subclass2
  },
  data() {
    return {
      pageSize: 5,
      pageNo: 1,
      total: 0,
      accidentList: [],
      noMore: false,
      index: 0,
      showHidden: false,
      rowList: {},
      overlayShow: false,
      loading: true,
      finished: false
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.onLoad();
  },
  methods: {
    onLoads() {
      setTimeout(() => {
        this.pageNo++;
        this.onLoad();
      }, 500);
    },
    onRefresh() {
      setTimeout(() => {
        this.pageNo = 1;
        this.accidentList = [];
        this.finished = false;
        this.loading = true;
        this.onLoad();
      }, 500);
    },
    button(val) {
      console.log(val);
      this.showHidden = val;
      this.pageNo = 1;
      this.accidentList = [];
      this.onLoad();
    },
    accident(row) {
      this.$router.push({
        path: "/specialDetails",
        query: {
          id: row.id,
          hospitalCode: row.hospitalCode
        }
      });
    },
    loadMore() {
      this.pageNo++;
      this.onLoad();
    },
    onLoad() {
      this.overlayShow = true;
      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        pageSize: this.pageSize,
        pageNo: this.pageNo,
        statusCode: this.statusCode
      };
      this.axios.postContralHostBase("getspecialHiddenDangerList", params, res => {
        this.loading = false;
        if (res.code == 200) {
          this.overlayShow = false;
          res.data.list.forEach(item => {
            this.accidentList.push(item);
          });
          if (this.accidentList.length >= res.data.total) {
            this.finished = true;
          }
        }
      });
    }
  }
};
</script>
#
<style scoped lang="scss">
@import "../../assets/stylus/theme";
.list {
  font-size: 16px;
  margin-bottom: 5px;
  .txt {
    padding: 0 20px;
    border-bottom: 8px solid #e6eaf0;
  }
  .apellation {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #f3f3f7;
    padding: 0 10px;
    height: 38px;
    line-height: 38px;
    i:nth-child(1) {
      display: inline-block;
      width: 100px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      color: $main-bgColor;
    }
    i:nth-child(2) {
      display: inline-block;
      color: $main-bgColor;
    }
  }
  div {
    line-height: 35px;
    font-size: 15px;
  }
  span {
    color: #a7a2a5;
  }
  i {
    font-style: normal;
  }
  .scene {
    display: inline-block;
    width: 200px;
    vertical-align: middle;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
.txet {
  text-align: center;
  font-size: 14px;
  color: #a7a2a5;
  padding-bottom: 15px;
}
.blank {
  text-align: center;
  img {
    width: 100%;
    height: 100%;
  }
}
.van-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
}
.top1 {
  // margin-top: 40px;
  margin-bottom: 70px;
}
/deep/ .van-list__finished-text {
  line-height: 28px !important;
  height: 28px;
}
</style>
