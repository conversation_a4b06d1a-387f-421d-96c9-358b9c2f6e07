<template>
  <div>
    <Header title="专项隐患" @backFun="backFn"></Header>
    <van-tabs v-if="delay" v-model="active" title-active-color="#29BEBC" offset-top="60px" sticky color="#29BEBC" swipe-threshold="4" line-width="50" ellipsis>
      <van-tab title="待审核">
        <hiddenList v-if="active == 0" :statusCode="4"></hiddenList>
      </van-tab>
      <van-tab title="已审核">
        <hiddenList v-if="active == 1" :statusCode="5"></hiddenList>
      </van-tab>
      <van-tab title="已完结">
        <hiddenList v-if="active == 2" :statusCode="6"></hiddenList>
      </van-tab>
    </van-tabs>
    <div></div>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
import hiddenList from "./hiddenList.vue";
export default {
  components: {
    hiddenList,
    topNav
  },
  data() {
    return {
      active: 0,
      delay: false,
      timer: ""
    };
  },
  mounted() {
    setTimeout(() => {
      this.sysClickBack();
    }, 500);
  },
  created() {
    this.timer = setInterval(() => {
      console.log("times");
      if (localStorage.getItem("loginInfo")) {
        this.delay = true;
        clearInterval(this.timer);
      }
    }, 100);
  },
  methods: {
    sysClickBack() {
      api.addEventListener(
        {
          name: "keyback666"
        },
        (ret, err) => {
          this.backFn();
        }
      );
    },
    backFn() {
      console.log('列表页返回')
      api.closeFrame({});
      api.clearCache(function() {});
    }
  }
};
</script>

<style scoped lang="scss">
@import "../../assets/stylus/theme";
/deep/ .van-tabs__nav--card {
  border: none !important;
  color: $main-bgColor;
}

/deep/ .van-tabs__nav--card .van-tab.van-tab--active {
  background: #fff;
  color: $main-bgColor;
}
/deep/ .van-tabs__nav--card .van-tab {
  color: $text-black-secondTitle;
  border: none !important;
}
.top {
  position: fixed;
  width: 100%;
  z-index: 99;
  background-color: #fff;
}
/deep/ .van-tabs__wrap {
  border-bottom: solid 8px #e6eaf0;
}
header {
  position: fixed;
}
</style>
