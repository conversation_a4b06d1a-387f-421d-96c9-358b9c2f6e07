<template>
  <div>
    <Header title="详情" @backFun="backFn"></Header>
    <div :class="['content', detailData.statusCode == '3' || detailData.statusCode == '4' ? '' : 'fullheight']">
      <div class="title">基本信息</div>
      <div class="base-info">
        <div class="item" v-for="(item, index) in itemList" :key="item.label">
          <span>{{ item.label }}</span>
          <div v-if="index == 0">{{ detailData[item.value] }}</div>
          <span v-else>{{ detailData[item.value] }}</span>
        </div>
      </div>
      <div class="title">项目信息</div>
      <div class="proj-info">
        <span>建设依据说明</span>
        <div>{{ detailData.projectBasisDescription }}</div>
        <span>建设情况说明</span>
        <div>{{ detailData.projectSituationDescription }}</div>
      </div>
      <div class="title">附件</div>
      <van-uploader @click-preview="lookDown" ref="uplodImg" accept="" v-model="detailData.attachmentArr" multiple :max-count="3" disabled :deletable="false" />
      <div v-for="(item, index) in detailData.auditRecordList" :key="item.id">
        <div class="title">审批信息（第{{ index + 1 }}次）</div>
        <div class="item">
          <span>审批结论</span>
          <span>{{ item.auditResult }}</span>
        </div>
        <div class="item">
          <span>审批时间</span>
          <span>{{ item.auditTime }}</span>
        </div>
        <div class="item">
          <span>审批部门</span>
          <span>{{ item.auditDeptName }}</span>
        </div>
        <div class="item">
          <span>审批人</span>
          <span>{{ item.auditPersonName }}</span>
        </div>
        <div class="item2">
          <span style="display:inline-block;margin:5px 0">审批意见</span>
          <div>{{ item.auditOpinion }}</div>
        </div>
        <div class="item-file">
          <div style="margin:8px 0">附件资料</div>
          <van-uploader @click-preview="lookDown" ref="uplodImg" accept="" v-model="item.auditAttachmentArr" multiple :max-count="3" disabled :deletable="false" />
        </div>
      </div>
      <div v-if="detailData.statusCode == '6'">
        <div class="title">招标采购信息</div>
        <div class="item">
          <span>是否招标</span>
          <span>{{ detailData.tenderType == "1" ? "否" : "是" }}</span>
        </div>
        <div class="item">
          <span>招标采购形式</span>
          <span>{{ detailData.tenderPurchaseName }}</span>
        </div>
        <div class="item">
          <span>招标日期</span>
          <span>{{ detailData.tenderTime }}</span>
        </div>
        <div class="item">
          <span>中标/成交金额（万元）</span>
          <span>{{ detailData.bidAmount }}</span>
        </div>
        <div class="item">
          <span>中标/承建公司</span>
          <span>{{ detailData.bidCompany }}</span>
        </div>
        <div class="item-file">
          <div style="margin:8px 0">附件资料</div>
          <van-uploader @click-preview="lookDown" ref="uplodImg" accept="" v-model="detailData.tenderAttachmentArr" multiple :max-count="3" disabled :deletable="false" />
        </div>

        <div class="title">项目建设信息</div>
        <div class="item">
          <span>建设开始日期</span>
          <span>{{ detailData.constructionStartDate }}</span>
        </div>
        <div class="item">
          <span>项目竣工日期</span>
          <span>{{ detailData.constructionEndDate }}</span>
        </div>
        <div class="item">
          <span>初验日期</span>
          <span>{{ detailData.initialTestDate }}</span>
        </div>
        <div class="item">
          <span>试运行结束日期</span>
          <span>{{ detailData.trialRunEndDate }}</span>
        </div>
        <div class="item">
          <span>终验日期</span>
          <span>{{ detailData.finalInspectionDate }}</span>
        </div>
        <div class="item-file">
          <div style="margin:8px 0">附件资料</div>
          <van-uploader @click-preview="lookDown" ref="uplodImg" accept="" v-model="detailData.constructionAttachmentArr" multiple :max-count="3" disabled :deletable="false" />
        </div>

        <div class="title">项目绩效信息</div>
        <div class="item2">
          <span>项目绩效情况</span>
          <div>{{ detailData.projectPerformanceSituation }}</div>
        </div>
        <div class="item-file">
          <div style="margin:8px 0">附件资料</div>
          <van-uploader @click-preview="lookDown" ref="uplodImg" accept="" v-model="detailData.projectAttachmentArr" multiple :max-count="3" disabled :deletable="false" />
        </div>

        <div class="title">项目关闭信息</div>
        <div class="item">
          <span>关闭原因</span>
          <div>{{ detailData.closeTypeCode == "4" ? detailData.closeOtherReason : detailData.closeTypeName }}</div>
        </div>
      </div>
    </div>
    <div class="footer" v-if="detailData.statusCode == '3' || detailData.statusCode == '4'">
      <van-button type="primary" color="#3ecac7" @click="approve">审批</van-button>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      id: "",
      detailData: {},
      itemList: [
        {
          label: "关联隐患",
          value: "correlationQuestionCodes"
        },
        {
          label: "申报编号",
          value: "declareNumber"
        },
        {
          label: "项目名称",
          value: "projectName"
        },
        {
          label: "医院名称",
          value: "hospitalName"
        },
        {
          label: "申报部门",
          value: "declareDeptName"
        },
        {
          label: "预计开始建设日期",
          value: "estimateStartConstructionDate"
        },
        {
          label: "预计结束建设日期",
          value: "estimateEndConstructionDate"
        },
        {
          label: "资金来源",
          value: "capitalSourceName"
        },
        {
          label: "预算年度",
          value: "budgetYear"
        },
        {
          label: "联系人",
          value: "contactPerson"
        },
        {
          label: "联系人电话",
          value: "contactNumber"
        },
        {
          label: "项目类型",
          value: "projectTypeName"
        },
        {
          label: "预算金额（万元）",
          value: "budgetAmount"
        }
      ],
      fileList: []
    };
  },
  mounted() {
    this.getData();
    setTimeout(() => {
      this.sysClickBack();
    }, 500);
  },
  created() {
    this.id = this.$route.query.id;
  },
  methods: {
    sysClickBack() {
      api.addEventListener(
        {
          name: "keyback666"
        },
        (ret, err) => {
          this.backFn();
        }
      );
    },
    backFn() {
      console.log("详情页返回");
      this.$router.go(-1);
      // api.closeFrame({});
      // api.clearCache(function() {});
    },
    getData() {
      this.axios.postContralHostBase("getSpecialHiddenDangerDetail", { id: this.id }, res => {
        console.log("res", res);
        if (res.code == 200) {
          this.detailData = res.data;
          this.detailData.attachmentArr = JSON.parse(this.detailData.attachmentArr);
          this.detailData.tenderAttachmentArr = JSON.parse(this.detailData.tenderAttachmentArr);
          this.detailData.constructionAttachmentArr = JSON.parse(this.detailData.constructionAttachmentArr);
          this.detailData.projectAttachmentArr = JSON.parse(this.detailData.projectAttachmentArr);
          this.detailData.auditRecordList.forEach(item => {
            item.auditAttachmentArr = JSON.parse(item.auditAttachmentArr);
          });
        }
      });
    },
    approve() {
      this.$router.push({
        path: "/approve",
        query: {
          id: this.id,
          hospitalCode: this.detailData.hospitalCode
        }
      });
    },
    lookDown(file) {
      var Type = file.name.split(".").pop();
      if (Type != "jpg" && Type != "jpeg" && Type != "png" && Type != "JPG" && Type != "JPEG" && Type != "gif") {
        var localPath = api.cacheDir + "/personalDocuments/" + file.name;

        api.download(
          {
            url: file.url,
            savePath: localPath,
            cache: true,
            allowResume: true
          },
          function(ret, err) {
            console.log(JSON.stringify(ret));
            if (ret.state == 1) {
              if (api.systemType == "ios") {
                var docReader = api.require("docReader");
                docReader.open(
                  {
                    path: ret.savePath,
                    autorotation: false
                  },
                  function(ret, err) {}
                );
              } else {
                // 安卓
                // 文档  图片
                var fileType = file.name.split(".").pop();
                if (fileType == "mp4" || fileType == "mp3" || fileType == "amr" || fileType == "MP4" || fileType == "MP3") {
                  // 安卓 视屏
                  api.openVideo({
                    url: ret.savePath
                  });
                } else {
                  var docReader = api.require("docReader");
                  docReader.open(
                    {
                      path: ret.savePath,
                      autorotation: false
                    },
                    function(ret, err) {}
                  );
                }
              }
            } else {
            }
          }
        );
      }
    }
  }
};
</script>

<style scoped lang="scss">
@import "../../assets/stylus/theme";
.title {
  font-size: 16px;
  border-left: 3px solid #3ecac7;
  color: #3ecac7;
  margin-top: 16px;
  margin-bottom: 8px;
  padding-left: 8px;
}
.content {
  padding: 0 16px;
  height: 80vh;
  overflow-y: auto;
}
.fullheight {
  height: 90vh !important;
}
.item > span {
  font-size: 14px;
}
.item {
  display: flex;
  justify-content: space-between;
  height: 34px;
  line-height: 34px;
}
.item > div {
  font-size: 14px;
  color: #888;
  display: flex;
  flex-wrap: wrap;
  white-space: normal;
  word-break: break-all;
}
.item > span:nth-child(1) {
  min-width: 100px;
}
.item > span:nth-child(2) {
  color: #888;
  overflow-x: auto;
}
.proj-info > span {
  font-size: 14px;
}
.proj-info > div {
  font-size: 14px;
  margin-top: 8px;
  color: #888;
}
.footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  height: 10vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
.van-button {
  width: 200px;
  font-size: 16px;
}
.base-info > div:nth-child(1) {
  height: auto;
}
.item2 {
  font-size: 14px;
  color: #000;
}
.item2 > div {
  margin: 3px 0;
  color: #888;
}
.item-file {
  font-size: 14px;
  color: #000;
}
</style>
