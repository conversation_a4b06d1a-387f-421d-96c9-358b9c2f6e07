<template>
  <div class="container">
    <Header title="台账详情" @backFun="goBack"> </Header>
    <div style="padding: 15px">
      <Table :type="3" :option="option" :tableData="tableData"></Table>
    </div>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
import Table from "../analysis/table.vue"
export default {
  components: {
    topNav,
    Table
  },
  data() {
    return {
      option: {},
      tableData:[
        {
          title: '基本信息'
        },
        {
          title: '医院名称',
          value: '-'
        },
        {
          title: '院区名称',
          value: '-'
        },
        {
          title: '填表人',
          value: '-'
        },
        {
          title: '联系电话',
          value: '-'
        },
        {
          title: '设备名称',
          value: '-'
        },
        {
          title: '设备型号',
          value: '-'
        },
        {
          title: '设备编码',
          value: '-'
        },
        {
          title: '专业类别',
          value: '-'
        },
        {
          title: '系统大类',
          value: '-'
        },
        {
          title: '系统小类',
          value: '-'
        },
        {
          title: '设备大类',
          value: '-'
        },
        {
          title: '设备小类',
          value: '-'
        },
        {
          title: '类目名称',
          value: '-'
        },
        {
          title: '类目代码',
          value: '-'
        },
        {
          title: '安装地点',
          value: '-'
        },
        {
          title: '首次投运日期',
          value: '-'
        },
        {
          title: '整体拆除日期',
          value: '-'
        },
        {
          title: '安全责任信息'
        },
        {
          title: '使用部门',
          value: '-'
        },
        {
          title: '安全责任人',
          value: '-'
        },
        {
          title: '联系电话',
          value: '-'
        },
        {
          title: '状态',
          value: '-'
        },
        {
          title: '生产单位名称',
          value: '-'
        },
        {
          title: '生产单位地址',
          value: '-'
        },
        {
          title: '生产单位电话',
          value: '-'
        },
        {
          title: '安装单位名称',
          value: '-'
        },
        {
          title: '安装单位地址',
          value: '-'
        },
        {
          title: '安装单位电话',
          value: '-'
        },
        {
          title: '运维单位名称',
          value: '-'
        },
        {
          title: '运维单位地址',
          value: '-'
        },
        {
          title: '运维单位电话',
          value: '-'
        },
        {
          title: '设备信息'
        }
      ],
      from: '',
      detail: '',
    };
  },
  created () {
    this.detail = this.$route.query.detail
    this.from = this.$route.query.from
    window.onscroll = null
    this.getData()
  },
  mounted() {
    this.sysClickBack()
  },
  methods: {
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    getData() {
      this.tableData[1].value = this.detail.hospitalName || '-'
      this.tableData[2].value = this.detail.hospitalName || '-'
      this.tableData[3].value = this.detail.writeName || '-'
      this.tableData[4].value = this.detail.telphone || '-'
      this.tableData[5].value = this.detail.eqName || '-'
      this.tableData[6].value = this.detail.eqType || '-'
      this.tableData[7].value = this.detail.eqCode || '-'
      this.tableData[8].value = this.detail.majorName || '-'
      this.tableData[9].value = this.detail.systemBigName || '-'
      this.tableData[10].value = this.detail.systemSmallName || '-'
      this.tableData[11].value = this.detail.eqBigName || '-'
      this.tableData[12].value = this.detail.eqSamllName || '-'
      this.tableData[13].value = this.detail.className || '-'
      this.tableData[14].value = this.detail.classCode || '-'
      this.tableData[15].value = this.detail.installLocation || '-'
      this.tableData[16].value = this.detail.firstTime || '-'
      this.tableData[17].value = this.detail.removeTime || '-'
      this.tableData[19].value = this.detail.useDept || '-'
      this.tableData[20].value = this.detail.safeDuty || '-'
      this.tableData[21].value = this.detail.safePhone || '-'
      this.tableData[22].value = this.detail.facilityStateName || '-'
      this.tableData[23].value = this.detail.produceUnit || '-'
      this.tableData[24].value = this.detail.produceLocation || '-'
      this.tableData[25].value = this.detail.producePhone || '-'
      this.tableData[26].value = this.detail.installUnit || '-'
      this.tableData[27].value = this.detail.installUnitLocation || '-'
      this.tableData[28].value = this.detail.installPhone || '-'
      this.tableData[29].value = this.detail.maintainUnit || '-'
      this.tableData[30].value = this.detail.maintainLocation || '-'
      this.tableData[31].value = this.detail.maintainPhone || '-'
      const equipment = JSON.parse(this.detail.eqParam)
      equipment.forEach(i => {
        const item = {
          title: i.fieldName,
          value: '-'
        }
        if (i.evalue) {
          if(!i.units){
            i.units=""
          }
          if (i.units == '-' || i.units == ' ') {
            item.value = i.evalue
          } else {
            item.value = i.evalue + ' ' + i.units
          }
        }
        this.tableData.push(item)
      })
    },
    goBack() {
      document.documentElement.scrollTop = 0
      document.body.scrollTop = 0
      this.$router.push({
        path: "account",
        query: {
          detail: this.detail,
          from: this.from
        }
      });
    }
  },
};
</script>

<style scoped>
.container {
  width: 100vw;
  background-color: #f5f6fb;
}
>>> .van-tab__text {
  font-size: 15px;
}
>>> .van-tabs__wrap {
  border-bottom: solid 5px #e6eaf0;
}
>>> .van-tabs__content {
  margin-top: 10px;
  background-color: #fff;
}
.list {
  border-bottom: 1px solid #e6eaf0;
  font-size: 14px;
  padding: 8px 16px;
  position: relative;
}
.list .title {
  display: inline-block;
  color: #353535;
  font-weight: 600;
  margin: 14px 0 12px 0;
}
.list-wrap {
  width: 94%;
  margin: 0 auto;
  background-color: #F3F3F3;
  border: 1px solid #797979;
  border-radius: 5px;
}
.itme {
  display: flex;
  padding: 5px 0;
  align-items: center;
}
.itme:nth-child(odd) {
  justify-content: space-between;
}
.itme span {
  display: inline-block;
}
>>>.van-pull-refresh__track {
  min-height: 80vh;
}
.child-text {
  color: #888;
}
</style>