<template>
  <div class="container">
    <Header :title="titleText" @backFun="goBack"> </Header>
    <div class="content">
      <div class="item-wrap">
        <van-empty description="暂无数据" v-if="listData.length == 0" style="padding: 50% 0"/>
        <div class="item" v-else v-for="(item,index) in listData" :key="index" @click="toAccountList(item)" >
          <img class="logo" :src="item.hospitalLogoUrl ? item.hospitalLogoUrl : imgUrl" alt="" height="100%" width="20%">
          <span class="name">{{ item.hospitalName + '：' }}</span>
          <span class="count">{{ item.amount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import imgUrl from '@/assets/images/hospital.png'
export default {
  components: {},
  data() {
    return {
      titleText: '',
      active: 0,
      listData: [],
      imgUrl,
      ledgerId: null,
    };
  },
  created() {
    this.ledgerId = this.$route.query.ledgerId
    this.titleText = this.$route.query.name
    if (this.ledgerId == '-4') {
      this.getCertificate()
    } else {
      this.getDataList()
    }
  },
  mounted() {
    this.sysClickBack()
  },
  methods: {
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    onRefresh() {
      this.getDataList()
    },
    toAccountList(item) {
      item.ledgerId = this.ledgerId
      item.specialEquipmentFlag = this.$route.query.specialEquipmentFlag
      sessionStorage.setItem('rowInfo', JSON.stringify(item))
        this.$router.push({
          path: "equipmentList",
          query: {
            row: item,
            from: 'accountDistribution',
            name: this.titleText,
            id: item.ledgerId == '-4' ? 'certificate' : 'special'
          }
        });
    },
    getCertificate() {
      this.axios.postContralHostBase('getCertificateStatistics',{}, res => {
        if (res.code == '200') {
          this.listData = res.data
        }
      })
    },
    getDataList() {
      const params = {
        ledgerId: this.ledgerId,
        specialEquipmentFlag: this.$route.query.specialEquipmentFlag
      }
      this.axios.postContralHostBase('getAccountDistribution',params, res => {
        if (res.code == '200') {
          this.listData = res.data
        }
      })
    },
    goBack() {
      sessionStorage.removeItem('rowInfo')
      this.$router.go(-1)
    }
  },
};
</script>

<style lang="scss" scoped>
.container {
  width: 100vw;
  height: 100vh;
  background-color: #F2F4F9;
  .content {
    height: calc(100% - 70px);
    padding: 0 3vw;
    font-size: 14px;
    .item-wrap {
      height: 100%;
      .item {
        padding: 10px  16px;
        margin-top: 10px;
        width: calc(100% - 32px);
        background-color: #fff;
        color: #1D2129;
        border-radius: 4px;
        height: 10vh;
        display: flex;
        align-items: center;
        .name {
          margin-left: 16px;
        }
        .count {
          margin-left: 10px;
        }
      }
    }
  }
}
</style>