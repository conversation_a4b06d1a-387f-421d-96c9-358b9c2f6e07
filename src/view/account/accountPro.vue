<template>
  <div class='container'>
    <Header title="重点台账" @backFun="goBack"> </Header>
    <div class="contentWrap">
      <div class="titleWrap">后勤设备</div>
      <div class="tableInfo">
        <div class="tableTitle">
          <div class="titleItem">专业类别</div>
          <div class="titleItem">设备总量</div>
          <div class="titleItem">特种设备</div>
        </div>
        <div v-for="(item, index) in tableData1" :key="index" class="tableItem">
          <span v-if="item.amount != 0" class="itemTitle">{{ item.ledgerName }}</span>
          <span v-if="item.amount != 0" class="itemCount" @click="toHospitalList(item, 'total')">{{ item.amount }}</span>
          <span v-if="item.amount != 0" class="itemCount" @click="toHospitalList(item, 'special')">{{ item.specialEquipmentCount }}</span>
        </div>
      </div>
      <div class="titleWrap">消防安全</div>
      <div class="tableInfo">
        <div class="tableTitle">
          <div class="titleItem">专业类别</div>
          <div class="titleItem">设备总量</div>
          <div class="titleItem">特种设备</div>
        </div>
        <div v-for="(item, index) in tableData2" :key="index" class="tableItem">
          <span v-if="item.amount != 0" class="itemTitle">{{ item.ledgerName }}</span>
          <span v-if="item.amount != 0" class="itemCount" @click="toHospitalList(item, 'total')">{{ item.amount }}</span>
          <span v-if="item.amount != 0" class="itemCount" @click="toHospitalList(item, 'special')">{{ item.specialEquipmentCount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      tableData1: [],
      tableData2: []
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    goBack() {
      api.closeFrame({});
    },
    getTableData() {
      this.axios.postContralHostBase('getAccountProStatistics',{}, res => {
        if (res.code == '200') {
          this.tableData1 = res.data.majorTypeList
          this.tableData2 = res.data.fireProtectionList
        }
      })
    },
    toHospitalList(row, type) {
      if (row.ledgerId == '-4') {
        if (type == 'total') {
          if (row.amount == 0) {
            this.$message.info('暂无更多')
          } else {
            this.$router.push({
              path: "accountDistribution",
              query: {
                ledgerId: row.ledgerId,
                name: row.ledgerName
              }
            })
          }
        } else {
          this.$message.info('暂无更多')
        }
      } else {
        if (type == 'total') {
          if (row.amount == 0) {
            this.$message.info('暂无更多')
          } else {
            this.$router.push({
              path: "accountDistribution",
              query: {
                ledgerId: row.ledgerId,
                name: row.ledgerName,
                specialEquipmentFlag: ''
              }
            })
          }
        } else {
          if (row.specialEquipmentCount == 0) {
            this.$message.info('暂无更多')
          } else {
            this.$router.push({
              path: "accountDistribution",
              query: {
                ledgerId: row.ledgerId,
                name: row.ledgerName,
                specialEquipmentFlag: '0'
              }
            })
          }
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .container {
    width: 100vw;
    height: 100vh;
    background-color: #F2F4F9;
    .contentWrap {
      font-size: 16px;
      .titleWrap {
        padding: 0 16px;
        height: 40px;
        line-height: 40px;
        color: #353535;
        font-weight: bold;
      }
      .tableInfo {
        padding: 16px;
        background-color: #fff;
        .tableTitle {
          display: flex;
          justify-content: space-between;
          height: 30px;
          color: #86909C;
          font-size: 12px;
          .titleItem {
            width: 40%;
          }
        }
        .tableItem {
          margin-bottom: 15px;
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .itemTitle {
            color: #1D2129;
            width: 40%;
          }
          .itemCount {
            width: 40%;
            color: #29BEBC;
          }
        }
        .tableItem:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
</style>