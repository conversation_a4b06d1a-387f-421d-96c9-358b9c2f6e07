<template>
  <div class="container">
    <Header title="重点台账" @backFun="goBack"> </Header>
    <div class="topWrap">
      <div class="titleWrap">
        <img src="../../assets/images/<EMAIL>" height="100%" alt="">
        <span class="titleText">后勤设备</span>
      </div>
      <div class="contenWrap">
        <div class="asideWrap">
          <div v-for="(i, index) in topData1" :key="index" class="barItem" @click="toList(i.ledgerName, i.amount, i.ledgerId)">
            <div class="barTitle">{{ i.ledgerName }}</div>
            <div class="barVal">
              {{ i.amount }}
              <span class="unit">个</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="centerWrap">
      <div class="titleWrap">
        <img src="../../assets/images/Frame@2x(1).png" height="100%" alt="">
        <span class="titleText">消防安全</span>
      </div>
      <div class="contenWrap">
        <div v-for="i in centerData" :key="i.id" class="barItem" @click="toList(i.name, i.val, i.id)">
          <div class="barTitle">{{ i.name }}</div>
            <div class="barVal">
              {{ i.val }}
              <span class="unit">个</span>
            </div>
          </div>
      </div>
    </div>
    <div class="centerWrap">
      <div class="titleWrap">
        <img src="../../assets/images/Frame@2x(2).png" height="100%" alt="">
        <span class="titleText">特种设备</span>
      </div>
      <div class="contenWrap">
        <div v-for="i in bottomData" :key="i.id" class="barItem" @click="toList(i.name, i.val, i.id)">
          <div class="barTitle">{{ i.name }}</div>
            <div class="barVal">
              {{ i.val }}
              <span class="unit">个</span>
            </div>
          </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      loginInfo: {},
      topData1: [],
      centerData: [],
      bottomData: []
    }
  },
  created() {
    this.getData()
    this.timer = setInterval(() => {
      if (localStorage.getItem("loginInfo")) {
        this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
        clearInterval(this.timer)
        this.sysClickBack()
      }
    }, 100);
  },
  methods: {
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    goBack() {
      if (this.from == 'accountDistribution') {
        this.$router.push({
          path: "accountDistribution",
          query: {
            detail: this.$route.query.detail
          }
        });
      } else {
        api.closeFrame({});
      }
    },
    getData() {
      this.axios.postContralHostBase('getAccountStatistics',{}, res => {
        if (res.code == '200') {
          this.topData1 = res.data.majorTypeList
          this.centerData = []
          for(let i in res.data.specialEquipment) {
            if (i == 'fireControlEquipmentCount') {
              this.centerData.push({
                name: '消防设备',
                val: res.data.specialEquipment[i],
                id: 'fireControl'
              })
            } else if (i == 'certificate') {
              this.centerData.push({
                name: '特种设备证书',
                val: res.data.specialEquipment[i],
                id: 'certificate'
              })
            } else if (i == 'specialEquipmentCount') {
              this.bottomData.push({
                name: '特种设备',
                val: res.data.specialEquipment[i],
                id: 'special'
              })
            }
          }
        }
      })
    },
    toList(name, count, id) {
      if (count == 0) return this.$toast('暂无更多')
      this.$router.push({
        path: '/equipmentList',
        query: {
          name,
          id
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .container {
    width: 100vw;
    height: 100vh;
    background-color: #F2F4F9;
    .topWrap {
      padding: 16px 10px;
      min-height: 30%;
      background-color: #fff;
      .titleWrap {
        height: 22px;
        display: flex;
        align-items: center;
        .titleText {
          margin-left: 2px;
          font-size: 16px;
          color: #353535;
          font-weight: 600;
        }
      }
      .contenWrap {
        margin-top: 10px;
        // height: calc(100% - 32px);
        .asideWrap {
          height: calc(50% - 5px);
        }
        .asideWrap::after {
          content: "";
          display: table;
          clear: both;
        }
        .barItem {
          float: left;
          display: flex;
          justify-content: space-around;
          flex-direction: column;
          border-radius: 8px;
          padding: 16px;
          height: calc(100% - 32px);
          width: calc(30% - 27px);
          background-color: #F7F8FA;
          .barTitle {
            font-size: 14px;
            color: #4E5969;
          }
          .barVal {
            font-size: 18px;
            font-weight: bold;
            color: #1D2129; 
          }
          .unit {
            font-size: 13px;
            color: #86909C;
          }
        }
        .barItem:nth-child(3n-1) {
          margin: 0 10px;
        }
        .barItem:nth-child(n + 4) {
          margin-top: 10px;
        }
      }
    }
    .centerWrap {
      margin-top: 10px;
      padding: 16px 10px;
      height: 15%;
      background-color: #fff;
      .titleWrap {
        height: 22px;
        display: flex;
        align-items: center;
        .titleText {
          margin-left: 2px;
          font-size: 16px;
          color: #353535;
          font-weight: 600;
        }
      }
      .contenWrap {
        margin-top: 10px;
        height: calc(100% - 32px);
        display: flex;
        justify-content: space-between;
        .barItem {
          display: flex;
          justify-content: space-around;
          flex-direction: column;
          border-radius: 8px;
          padding: 16px;
          height: calc(100% - 32px);
          width: calc(50% - 37px);
          background-color: #F7F8FA;
          .barTitle {
            font-size: 14px;
            color: #4E5969;
          }
          .barVal {
            font-size: 18px;
            font-weight: bold;
            color: #1D2129; 
          }
          .unit {
            font-size: 13px;
            color: #86909C;
          }
        }
      }
    }
  }
</style>