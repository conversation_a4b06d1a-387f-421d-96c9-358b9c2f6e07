<template>
  <div class='container'>
    <Header :title="titleText" @backFun="goBack"> </Header>
    <div class="contentWrap">
      <div v-if="isEarlyWarning" class="titleWrap">预警信息</div>
      <div v-if="isEarlyWarning" class="earlyWarningWrap">
        <div class="detailItem">
          <div class="title">预警类型</div>
          <div class="content">{{ earlyWarningData.earlyWarningName }}</div>
        </div>
        <div class="detailItem">
          <div class="title">复审到期日期</div>
          <div class="content">{{ earlyWarningData.expirationTime }}天</div>
        </div>
        <div class="detailItem">
          <div class="title">专业类别</div>
          <div class="content">{{ earlyWarningData.majorName }}</div>
        </div>
      </div>
      <div class="titleWrap">设备安全</div>
      <!-- 设备信息 -->
      <div v-if="type != 'certificate'" class="detailInfo" :style="{'height': isEarlyWarning ? 'calc((100% - 274px) * 0.6)' : ''}">
        <!-- 消防设备且为非特种设备 -->
        <template v-if="detailInfo.majorType == '-3' && detailInfo.specialEquipmentFlag == '1'">
          <div class="detailItem">
            <div class="title">专业类别</div>
            <div class="content">{{ detailInfo.majorName }}</div>
          </div>
          <div class="detailItem">
            <div class="title">设备名称</div>
            <div class="content">{{ detailInfo.eqName }}</div>
          </div>
          <div class="detailItem">
            <div class="title">规格型号</div>
            <div class="content">{{ detailInfo.specificationsModels || detailInfo.eqType }}</div>
          </div>
          <div class="detailItem">
            <div class="title">生产厂家</div>
            <div class="content">{{ detailInfo.produceUnit }}</div>
          </div>
          <div class="detailItem">
            <div class="title">运行场所</div>
            <div class="content">{{ detailInfo.installLocation }}</div>
          </div>
          <div class="detailItem">
            <div class="title">厂内编号</div>
            <div class="content">{{ detailInfo.inPlantCode }}</div>
          </div>
          <div class="detailItem">
            <div class="title">出厂日期</div>
            <div class="content">{{ detailInfo.leaveFactoryDate }}</div>
          </div>
          <div class="detailItem">
            <div class="title">投用时间</div>
            <div class="content">{{ detailInfo.firstTime }}</div>
          </div>
          <div class="detailItem">
            <div class="title">过期时间</div>
            <div class="content">{{ detailInfo.expirationDate }}</div>
          </div>
          <div class="detailItem">
            <div class="title">上次检修时间</div>
            <div class="content">{{ detailInfo.lastInspectionTime }}</div>
          </div>
          <div class="detailItem">
            <div class="title">下次检修时间</div>
            <div class="content">{{ detailInfo.nextTimeOverhaul }}</div>
          </div>
          <div class="detailItem">
            <div class="title">设备负责人</div>
            <div class="content">{{ detailInfo.safeDuty }}</div>
          </div>
        </template>
        <template v-else>
          <div class="detailItem">
            <div class="title">医院名称</div>
            <div class="content">{{ detailInfo.hospitalName }}</div>
          </div>
          <div class="detailItem">
            <div class="title">填表人</div>
            <div class="content">{{ detailInfo.writeName }}</div>
          </div>
          <div class="detailItem">
            <div class="title">联系电话</div>
            <div class="content">{{ detailInfo.telphone }}</div>
          </div>
          <div class="detailItem">
            <div class="title">设备名称</div>
            <div class="content">{{ detailInfo.eqName }}</div>
          </div>
          <div class="detailItem">
            <div class="title">设备型号</div>
            <div class="content">{{ detailInfo.eqType }}</div>
          </div>
          <div class="detailItem">
            <div class="title">设备编码</div>
            <div class="content">{{ detailInfo.eqCode }}</div>
          </div>
          <div class="detailItem">
            <div class="title">专业类别</div>
            <div class="content">{{ detailInfo.majorName }}</div>
          </div>
          <div class="detailItem">
            <div class="title">系统大类</div>
            <div class="content">{{ detailInfo.systemBigName }}</div>
          </div>
          <div class="detailItem">
            <div class="title">系统小类</div>
            <div class="content">{{ detailInfo.systemSmallName }}</div>
          </div>
          <div class="detailItem">
            <div class="title">设备大类</div>
            <div class="content">{{ detailInfo.eqBigName }}</div>
          </div>
          <div class="detailItem">
            <div class="title">设备小类</div>
            <div class="content">{{ detailInfo.eqSamllName }}</div>
          </div>
          <div class="detailItem">
            <div class="title">类目名称</div>
            <div class="content">{{ detailInfo.className }}</div>
          </div>
          <div class="detailItem">
            <div class="title">类目代码</div>
            <div class="content">{{ detailInfo.classCode }}</div>
          </div>
          <div class="detailItem">
            <div class="title">安装地点</div>
            <div class="content">{{ detailInfo.installLocation }}</div>
          </div>
          <div class="detailItem">
            <div class="title">首次投运日期</div>
            <div class="content">{{ detailInfo.firstTime }}</div>
          </div>
          <div class="detailItem">
            <div class="title">整体拆除日期</div>
            <div class="content">{{ detailInfo.removeTime }}</div>
          </div>
          <div class="detailItem">
            <div class="title">使用部门</div>
            <div class="content">{{ detailInfo.useDept }}</div>
          </div>
          <div class="detailItem">
            <div class="title">安全责任人</div>
            <div class="content">{{ detailInfo.safeDuty }}</div>
          </div>
          <div class="detailItem">
            <div class="title">联系电话</div>
            <div class="content">{{ detailInfo.safePhone }}</div>
          </div>
          <div class="detailItem">
            <div class="title">状态</div>
            <div class="content">{{ detailInfo.facilityStateName }}</div>
          </div>
          <div class="detailItem">
            <div class="title">生产单位名称</div>
            <div class="content">{{ detailInfo.produceUnit }}</div>
          </div>
          <div class="detailItem">
            <div class="title">生产单位地址</div>
            <div class="content">{{ detailInfo.produceLocation }}</div>
          </div>
          <div class="detailItem">
            <div class="title">生产单位电话</div>
            <div class="content">{{ detailInfo.producePhone }}</div>
          </div>
          <div class="detailItem">
            <div class="title">安装单位名称</div>
            <div class="content">{{ detailInfo.installUnit }}</div>
          </div>
          <div class="detailItem">
            <div class="title">安装单位地址</div>
            <div class="content">{{ detailInfo.installUnitLocation }}</div>
          </div>
          <div class="detailItem">
            <div class="title">安装单位电话</div>
            <div class="content">{{ detailInfo.installPhone }}</div>
          </div>
          <div class="detailItem">
            <div class="title">运维单位名称</div>
            <div class="content">{{ detailInfo.maintainUnit }}</div>
          </div>
          <div class="detailItem">
            <div class="title">运维单位地址</div>
            <div class="content">{{ detailInfo.maintainLocation }}</div>
          </div>
          <div class="detailItem">
            <div class="title">运维单位电话</div>
            <div class="content">{{ detailInfo.maintainPhone }}</div>
          </div>
          <!-- 特种设备信息 -->
          <template v-if="detailInfo.specialEquipmentFlag == '0'">
            <div class="detailItem">
              <div class="title">设备名称</div>
              <div class="content">{{ detailInfo.eqName }}</div>
            </div>
            <div class="detailItem">
              <div class="title">定检周期</div>
              <div class="content">{{ detailInfo.periodVerification + '月/次' }}</div>
            </div>
            <div class="detailItem">
              <div class="title">上次检验日期</div>
              <div class="content">{{ detailInfo.lastInspectionTime }}</div>
            </div>
            <div class="detailItem">
              <div class="title">注册证号</div>
              <div class="content">{{ detailInfo.registrationCertificate }}</div>
            </div>
            <div class="detailItem">
              <div class="title">使用证号</div>
              <div class="content">{{ detailInfo.usePermitCode }}</div>
            </div>
            <div class="detailItem">
              <div class="title">下次检修日期</div>
              <div class="content">{{ detailInfo.nextTimeOverhaul }}</div>
            </div>
            <div class="detailItem">
              <div class="title">使用证名称</div>
              <div class="content">{{ detailInfo.usePermitName }}</div>
            </div>
            <div class="detailItem">
              <div class="title">出厂日期</div>
              <div class="content">{{ detailInfo.leaveFactoryDate }}</div>
            </div>
            <div class="detailItem">
              <div class="title">厂内编号</div>
              <div class="content">{{ detailInfo.inPlantCode }}</div>
            </div>
            <div class="detailItem">
              <div class="title">设备负责人</div>
              <div class="content">{{ detailInfo.safeDuty }}</div>
            </div>
            <div class="detailItem">
              <div class="title">规格型号</div>
              <div class="content">{{ detailInfo.specificationsModels }}</div>
            </div>
          </template>
        </template>
      </div>
      <div v-else class="detailInfo" :style="{'height': isEarlyWarning ? 'calc((100% - 274px) * 0.6)' : ''}">
        <div class="detailItem">
          <div class="title">证书名称</div>
          <div class="content">{{ detailInfo.equipmentName }}</div>
        </div>
        <div class="detailItem">
          <div class="title">证书编号</div>
          <div class="content">{{ detailInfo.equipmentNumber }}</div>
        </div>
        <div class="detailItem">
          <div class="title">持证人</div>
          <div class="content">{{ detailInfo.holderName }}</div>
        </div>
        <div class="detailItem">
          <div class="title">持证人手机号</div>
          <div class="content">{{ detailInfo.holderPhone }}</div>
        </div>
        <div class="detailItem">
          <div class="title">作业类别</div>
          <div class="content">{{ detailInfo.jobClassName }}</div>
        </div>
        <div class="detailItem">
          <div class="title">准操项目</div>
          <div class="content">{{ detailInfo.scopeProject }}</div>
        </div>
        <div class="detailItem">
          <div class="title">初次领证日期</div>
          <div class="content">{{ detailInfo.firstIssueDate }}</div>
        </div>
        <div class="detailItem">
          <div class="title">领证日期</div>
          <div class="content">{{ detailInfo.issueDate }}</div>
        </div>
        <div class="detailItem">
          <div class="title">有效期</div>
          <div class="content">{{ detailInfo.periodValidity }}</div>
        </div>
        <div class="detailItem">
          <div class="title">复审期限</div>
          <div class="content">{{ detailInfo.recheckDeadline }}</div>
        </div>
        <div class="detailItem">
          <div class="title">复审日期</div>
          <div class="content">{{ detailInfo.recheckDate }}</div>
        </div>
        <div class="detailItem">
          <div class="title">发证单位</div>
          <div class="content">{{ detailInfo.issueUnit }}</div>
        </div>
      </div>
      <div class="pictureWrap">
        <div class="pictureTitle">
          <span>照片</span>
          <span>{{ '共' + srcList.length + '张' }}</span>
        </div>
        <div class="pictureList">
          <el-image
            v-if="pictureUrl"
            id="images"
            style="height: 100%; color:#fff"
            :src="pictureUrl" 
            :preview-src-list="srcList">
          </el-image>
          <div v-else class="noMore">暂无更多</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      titleText: '',
      detailInfo: {},
      pictureUrl: '',
      srcList: [],
      type: '',
      isEarlyWarning: false,
      earlyWarningData: {}
    }
  },
  created() {
    this.getDetailData()
    if (!this.$route.query.from) {
      this.isEarlyWarning = true
      this.getEarlyWarning()
    }
  },
  methods: {
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    goBack() {
      if (this.$route.query.from == 'H5') {
        this.$router.go(-1)
      } else {
        api.sendEvent({
          name: 'refreshList'
        })
        api.closeFrame({});
      }
    },
    getDetailData() {
      this.type = this.$route.query.type
      if (this.type == 'certificate') {
        this.axios.postContralHostBase('equipmentCertificateDetail', {
          id: this.$route.query.id || this.$route.query.row.id
        }, res => {
          if (res.code == '200') {
            this.detailInfo = res.data
            if (this.$route.query.isSpecial && this.$route.query.isSpecial == '1') {
              this.titleText = '特种设备'
            } else {
              this.titleText = res.data.majorTypeName
            }
            if (this.type == 'certificate') {
              this.pictureUrl = res.data.certificatePicture ? __PATH.DOWNLOAD_URL + res.data.certificatePicture : ''
              this.srcList = res.data.certificatePicture ? [__PATH.DOWNLOAD_URL + res.data.certificatePicture] : []
            }
          }
        })
      } else {
        this.axios.postContralHostBase('equipmentDetail', {
          id: this.$route.query.id || this.$route.query.row.id
        }, res => {
          if (res.code == '200') {
            this.detailInfo = res.data
            if (this.$route.query.isSpecial && this.$route.query.isSpecial == '1') {
              this.titleText = '特种设备'
            } else {
              this.titleText = res.data.majorName
            }
            if (res.data.specialEquipmentFlag == '0' && res.data.examiningReportArr) {
              this.pictureUrl = res.data.examiningReportArr ? __PATH.DOWNLOAD_URL + res.data.examiningReportArr : ''
              this.srcList = res.data.examiningReportArr ? [__PATH.DOWNLOAD_URL + res.data.examiningReportArr] : []
            }
          }
        })
      }
    },
    getEarlyWarning() {
      this.axios.postContralHostBase('earlyWarningList', {
        pageNo: 1,
        pageSize: 999
      }, res => {
        if (res.code == '200') {
          this.earlyWarningData = res.data.list.find(i => i.id == this.$route.query.id) || {}
          console.log(this.earlyWarningData)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .container {
    width: 100vw;
    height: 100vh;
    background-color: #F2F4F9;
    .contentWrap {
      font-size: 16px;
      height: calc(100% - 60px);
      .titleWrap {
        padding: 0 16px;
        height: 40px;
        line-height: 40px;
        color: #353535;
        font-weight: bold;
      }
      .earlyWarningWrap {
        overflow: auto;
        padding: 16px;
        background-color: #fff;
        .detailItem {
          margin-bottom: 10px;
          width: 100%;
          display: flex;
          .title {
            width: 40%;
            color: #4E5969;
          }
          .content {
            width: 60%;
            color: #1D2129;
          }
        }
        .detailItem:last-child {
          margin-bottom: 0;
        }
      }
      .detailInfo {
        height: 60%;
        overflow: auto;
        padding: 16px;
        background-color: #fff;
        .detailItem {
          margin-bottom: 10px;
          width: 100%;
          display: flex;
          .title {
            width: 35%;
            color: #4E5969;
          }
          .content {
            width: 65%;
            color: #1D2129;
          }
        }
        .detailItem:last-child {
          margin-bottom: 0;
        }
      }
      .pictureWrap {
        height: 120px;
        margin-top: 10px;
        padding: 16px;
        background-color: #fff;
        .pictureTitle {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: #4E5969;
          padding-bottom: 10px;
        }
        .pictureList {
          display: flex;
          justify-content: space-between;
          height: calc(100% - 30px);
        }
        .noMore {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
</style>