<template>
  <div class="container">
    <Header title="重点台账" @backFun="goBack"> </Header>
    <!-- <top-nav title="巡检任务"></top-nav> -->
    <van-tabs
      v-model="active"
      sticky
      offset-top="60px"
      color="#29BEBC"
      line-width="80"
      title-active-color="#29BEBC"
    >
      <van-tab title="设备安全">
        <van-pull-refresh
          v-model="refreshing"
          @refresh="onRefresh"
          success-text="刷新成功"
        >
          <div
            v-for="(item, index) in listData"
            :key="index"
          >
            <van-empty description="暂无数据" v-if="!item" />
            <div class="list-wrap" v-else>
              <div class="list" @click="goTaskDetail(item)">
                <div class="itme">
                  <div>
                    <span>设备名称：</span>
                    <span class="child-text">{{ item.eqName || '-' }}</span>
                  </div>
                  <span :class="item.facilityStateName ==''">{{ item.facilityStateName || '-' }}</span>
                </div>
                <div class="itme">
                  <span>首次投入使用日期：</span>
                  <span class="child-text">{{ item.firstTime || '-' }}</span>
                </div>
                <div class="itme">
                  <div>
                    <span>使用部门：</span>
                    <span class="child-text">{{ item.useDept || '-' }}</span>
                  </div>
                  <div>
                    <span>安全责任人：</span>
                  <span class="child-text">{{ item.safeDuty || '-' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bottom-tips">
            <div v-if="finished">加载中<img style="vertical-align:middle;" src="../../assets/images/加载.png"></div>
            <div v-else>暂无更多</div>
          </div>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
export default {
  components: {
    topNav,
  },
  data() {
    return {
      active: 0,
      loading: false,
      finished: true,
      refreshing: false,
      listData:[],
      params : {
        hospitalCode: '',
        unitCode: '',
        pageNo: 1,
        pageSize: 10,
        ledgerId: ''
      },
      from: '',
      loginInfo: '',
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
      timer: '',
      delay: false
    };
  },
  created() {
    this.from = this.$route.query.from || ''
    this.params.pageNo = 1
    this.timer = setInterval(() => {
      if (localStorage.getItem("loginInfo")) {
        this.delay = true;
        this.params.unitCode = JSON.parse(localStorage.getItem("loginInfo")).unitCode;
        if (JSON.parse(localStorage.getItem("loginInfo")).isHospital == 0) {
          let ygjUserInfo = api.getPrefs({
            sync: true,
            key: "currentHospital"
          });
          if (this.from == 'accountDistribution') {
            this.params.ledgerId = this.$route.query.detail.ledgerId
            this.params.hospitalCode = this.$route.query.detail.hospitalCode
          } else {
            this.params.hospitalCode = JSON.parse(ygjUserInfo).hospitalCode
          }
          this.onRefresh()
        } else {
          this.params.hospitalCode = JSON.parse(localStorage.getItem("loginInfo")).hospitalCode;
          this.getDataList()
        }
        this.sysClickBack()
        clearInterval(this.timer);
      }
    }, 100);
    // if (this.from == 'accountDistribution') {
    //   console.log('query', this.$route.query.detail)
    //   this.params.hospitalCode = this.$route.query.detail.hospitalCode
    //   this.params.ledgerId = this.$route.query.detail.ledgerId
    //   this.getDataList()
    // }
    window.onscroll = this.isscroll
  },
  mounted() {},
  methods: {
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    onRefresh() {
      this.params.pageNo = 1
      this.params.pageSize = 10
      this.finished = true
      this.getDataList()
    },
    goTaskDetail(item) {
      if (this.from == 'accountDistribution') {
        item.ledgerId = this.$route.query.detail.ledgerId
      }
      this.$router.push({
        path: "accountDetail",
        query: {
          detail: item,
          from: this.from
        }
      });
    },
    getDataList(type) {
      this.axios.postContralHostBase('getAccountList',this.params, res => {
        if (res.code == '200') {
          if (type == 'pull') {
            if (res.data.count == this.listData.length) return
            res.data.list.forEach(item => {
              this.listData.push(item)
            });
          } else {
            this.listData = res.data.list
          }
          if (res.data.list == 0 || this.listData.length == res.data.count) {
            console.log('加载结束')
            this.finished = false
          }
          this.refreshing = false;
        }
      })
    },
    isscroll () {
      let scrollY = document.documentElement.scrollTop || document.body.scrollTop
      let vh = document.documentElement.clientHeight;
      const scrollHeight = document.documentElement.scrollHeight
      if (scrollY + vh >= scrollHeight - 10 && this.finished && this.$route.name == 'account') {
        console.log('满足加载条件')
        this.params.pageNo++
        this.thorttle(this.getDataList('pull'))
      }
    },
    // 节流
    thorttle (fn) {
      let timer = null
      return () => {
        if (!timer) {
          timer = setTimeout(() => {
            fn.call(this)
            timer = null
          }, 1000)
        }
      }
    },
    goBack() {
      if (this.from == 'accountDistribution') {
        this.$router.push({
          path: "accountDistribution",
          query: {
            detail: this.$route.query.detail
          }
        });
      } else {
        api.closeFrame({});
      }
    }
  }
};
</script>

<style scoped>
.container {
  width: 100vw;
  background-color: #f5f6fb;
}
>>> .van-tab__text {
  font-size: 15px;
}
>>> .van-tabs__wrap {
  border-bottom: solid 5px #e6eaf0;
}
>>> .van-tabs__content {
  margin-top: 10px;
  background-color: #fff;
}
.list {
  border-bottom: 1px solid #e6eaf0;
  font-size: 14px;
  padding: 8px 16px;
  position: relative;
}
.list .title {
  display: inline-block;
  color: #353535;
  font-weight: 600;
  margin: 14px 0 12px 0;
}
.list-wrap {
  width: 94%;
  margin: 8px auto;
  /* background-color: #F3F3F3; */
  border: 1px solid #797979;
  border-radius: 5px;
}
.itme {
  display: flex;
  padding: 5px 0;
  align-items: center;
}
.itme:nth-child(odd) {
  justify-content: space-between;
}
.itme span {
  display: inline-block;
}
.child-text {
  color: #888;
}
.bottom-tips {
  font-size: 12px;
  text-align: center;
  color: #969799;
}
</style>