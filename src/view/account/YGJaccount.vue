<template>
  <div class="container">
    <Header title="重点台账" @backFun="goBack"> </Header>
    <!-- <top-nav title="巡检任务"></top-nav> -->
    <van-tabs
      v-model="active"
      sticky
      offset-top="2rem"
      color="#29BEBC"
      line-width="80"
      title-active-color="#29BEBC"
    >
      <van-tab title="设备安全">
        <div style="padding: 10px">
          <table cellspacing="0" cellpadding="0" style="width:100%" class="table">
            <tr>
              <th class="th" v-for="(item, index) in option.column" :key="index">{{ item.label }}</th>
            </tr>
            <tr v-for="(item, index) in tableData" :key="index" class="list-tr">
              <td
                v-for="(context, i) in option.column"
                :key="i"
                @click="context.tableDataprop == 'amount' ? goTaskDetail(item) : ''"
                :class="context.tableDataprop == 'amount' ? 'count' : ''"
              >{{ item[context.tableDataprop] }}</td>
            </tr>
          </table>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
import Table from "../analysis/table.vue"
export default {
  components: {
    topNav,
    Table
  },
  data() {
    return {
      active: 0,
      option: {
        column: [
          {
            label: '专业类别',
            tableDataprop: 'ledgerName'
          },
          {
            label: '数量',
            tableDataprop: 'amount'
          },
          {
            label: '百分比',
            tableDataprop: 'proportion'
          }
        ]
      },
      tableData: [],
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
    };
  },
  created() {
    this.getDataList()
    apiready = () => {
      // if (!localStorage.getItem("loginInfo")) {
      var userInfo = api.getPrefs({
        sync: true,
        key: "userInfo"
      });
      userInfo = JSON.parse(userInfo);
      // userInfo.hospitalCode = api.pageParam.hospitalCode
      // userInfo.hospitalName = api.pageParam.hospitalName
      if (userInfo.id) {
        const virtualToken = encodeURIComponent(userInfo.hospitalName);
        localStorage.setItem('token', virtualToken);
        localStorage.setItem("loginInfo", JSON.stringify(userInfo));
      }
      // }
      this.getDataList()
      this.sysClickBack()
    }
  },
  mounted() {
    this.sysClickBack()
  },
  methods: {
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    onRefresh() {
      this.getDataList()
    },
    goTaskDetail(item) {
        this.$router.push({
          path: "accountDistribution",
          query: {
            detail: item
          }
        });
    },
    getDataList() {
      const params = {}
      this.axios.postContralHostBase('getYGJAccountList',params, res => {
        if (res.code == '200') {
          this.tableData = res.data
        }
      })
    },
    goBack() {
      api.closeFrame({});
    }
  },
};
</script>

<style scoped>
.container {
  width: 100vw;
  background-color: #f5f6fb;
}
>>> .van-tab__text {
  font-size: 15px;
}
>>> .van-tabs__wrap {
  border-bottom: solid 5px #e6eaf0;
}
>>> .van-tabs__content {
  margin-top: 10px;
  background-color: #fff;
}
.table {
  table-layout:fixed;
  border-collapse: collapse;
  border-radius: .185185rem;
  border-right:1px solid #797979 ;
  border-bottom:1px solid #797979 ;
  font-size: 14px;
  background-color: #fff;
}
.th {
  font-weight: normal;
  height: 1.5rem;
  line-height: 1.5rem;
  background-color: #e6eaf0;
  text-align: center;
  border-left:1px solid #797979 ;
  border-top:1px solid #797979 ;
}
.th:last-child {
  border-right: 1px solid #797979;
}
.list-tr {
  height: 1.074074rem;
  line-height: 1.074074rem;
}
td {
  text-align: center;
  height: 30px;
  border-left:1px solid #797979 ;
  border-top:1px solid #797979 ;
}
.typt-title {
  background-color: #ccc;
}
.count {
  color: #00cac8;
  text-decoration: underline;
}
</style>