<template>
  <div class='container'>
    <Header :title="titleText" @backFun="goBack"> </Header>
    <div class="listWrap">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad">
          <van-cell v-for="(item, index) in listData" :key="index">
            <div v-if="$route.query.id == 'certificate'" class="itemConten" @click="toDetail(item, 'certificate')">
              <div class="eqTitle">
                <span>证书名称：
                  {{ item.equipmentName }}
                </span>
                <van-icon name="arrow" color="#C9CDD4"/>
              </div>
              <div class="itemBar">
                <div class="leftTitle">证书编号 </div>
                <span class="rigthTitle">{{ item.equipmentNumber }}</span>
              </div>
              <div class="itemBar">
                <div class="leftTitle">有效期 </div>
                <span class="rigthTitle">{{ item.periodValidity }}</span>
              </div>
              <div class="itemBar">
                <div class="leftTitle">持证人 </div>
                <span class="rigthTitle">{{ item.holderName }}</span>
              </div>
            </div>
            <div v-else class="itemConten" @click="toDetail(item, 'equipment')">
              <div class="eqTitle">
                <span>设备名称：
                  {{ item.eqName }}
                </span>
                <van-icon name="arrow" color="#C9CDD4"/>
              </div>
              <div class="itemBar">
                <div class="leftTitle">首次投入使用日期 </div>
                <span class="rigthTitle">{{ item.firstTime }}</span>
              </div>
              <div class="itemBar">
                <div class="leftTitle">使用部门 </div>
                <span class="rigthTitle">{{ item.useDept }}</span>
              </div>
              <div class="itemBar">
                <div class="leftTitle">安全负责人 </div>
                <span class="rigthTitle">{{ item.safeDuty }}</span>
              </div>
            </div>
          </van-cell>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>
<script>
import axios from 'axios';
export default {
  components: {},
  data() {
    return {
      titleText: '特种设备',
      refreshing: false,
      loading: false,
      finished: false,
      listData: [],
      params : {
        hospitalCode: '',
        unitCode: '',
        pageNo: 1,
        pageSize: 10,
        ledgerId: ''
      },
      from: '',
      loginInfo: {}
    }
  },
  created() {
    this.from = this.$route.query.from || ''
    this.titleText = this.$route.query.name
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.params.unitCode = this.loginInfo.unitCode;
    if (this.loginInfo.isHospital == 1) {
      this.params.hospitalCode = this.loginInfo.hospitalCode;
    }
    if (this.from == 'accountDistribution') {
      const rowInfo = JSON.parse(sessionStorage.getItem('rowInfo'))
      this.params.hospitalCode = rowInfo.hospitalCode
      this.params.ledgerId = rowInfo.ledgerId
      this.params.specialEquipmentFlag = rowInfo.specialEquipmentFlag
    } else {
      if (this.$route.query.id == 'special') {
        this.params.specialEquipmentFlag = '0'  
      } else {
        this.params.ledgerId = this.$route.query.id
        if (this.$route.query.id == 'fireControl') {
          this.params.type = '2'
          this.params.ledgerId = ''
        } else if (this.$route.query.id == 'certificate') {
          this.params.type = '3'
          this.params.ledgerId = ''
        }
      }
    }
  },
  methods: {
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    onLoad() {
      this.params.staffId = this.loginInfo.id
      this.params.sysForShort = 'ipsm'
      this.params.platformFlag = 2
      this.params.userName = this.loginInfo.name
      this.params.userId = this.loginInfo.id
      this.params.officeCode = this.loginInfo.teamId || this.loginInfo.officeId
      this.params.officeId = this.loginInfo.teamId || this.loginInfo.officeId
      this.params.officeName = this.loginInfo.teamNames || this.loginInfo.officeName
      this.params.companyCode = this.loginInfo.companyCode
      this.params.phone = this.loginInfo.phone
      if (this.$route.query.id == 'certificate') {
        const qs = require("qs");
        axios.post(__PATH.BASEURL +'specialEquipmentCertificate/listData',qs.stringify(this.params)).then(res => {
          const { code, data, message } = res.data
          if (code == 200) {
            if (this.listData.length == data.count) {
              this.finished = true
            } else {
              this.listData = this.listData.concat(data.list)
              this.params.pageNo++
            }
          } else {
            this.$toast(message || '请求失败')
          }
          this.refreshing = false
          this.loading = false
        })
      } else {
        const qs = require("qs");
        axios.post(__PATH.BASEURL +'standingBook/listData',qs.stringify(this.params)).then(res => {
          const { code, data, message } = res.data
          if (code == 200) {
            if (this.listData.length == data.count) {
              this.finished = true
            } else {
              this.listData = this.listData.concat(data.list)
              this.params.pageNo++
            }
          } else {
            this.$toast(message || '请求失败')
          }
          this.refreshing = false
          this.loading = false
        })
      }
    },
    onRefresh() {
      this.params.pageNo = 1
      // 清空列表数据
      this.finished = false;
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.onLoad();
    },
    toDetail(row, type) {
      this.$router.push({
        path: '/equipmentDetail',
        query: {
          row,
          type,
          from: 'H5',
          isSpecial: this.$route.query.id == 'special' ? '1' : '0'
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .container {
    width: 100vw;
    height: 100vh;
    .listWrap {
      background-color: #F2F4F9;
      padding: 10px;
      height: calc(100% - 80px);
      width: calc(100% - 20px);
      overflow: auto;
      /deep/ .van-cell {
        margin-bottom: 10px;
        padding: 16px;
        border-radius: 8px;
        border: none;
        background-color: #fff;
        .itemConten {
          font-size: 16px;
          color: #1D2129;
          .eqTitle {
            padding-bottom: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          .itemBar {
            margin-top: 10px;
            display: flex;
            align-items: center;
          }
          .leftTitle {
            color: #4E5969;
          }
          .rigthTitle {
            margin-left: 16px;
          }
        }
      }
    }
  }
</style>