<template>
  <div>
    <Header title="事故管理" @backFun="backFn"></Header>
    <van-tabs
      v-model="active"
      title-active-color="#29BEBC"
      offset-top="3rem"
      sticky
      color="#29BEBC"
      swipe-threshold="4"
      line-width="50"
      ellipsis
    >
      <van-tab v-for="item in tasksList" :title="item.dictName" :key="item.id">
      </van-tab>

      <accidentReport v-if="active == 0"></accidentReport>
      <accidentAudit v-if="active == 1"></accidentAudit>
      <accidentLedger v-if="active == 2"></accidentLedger>
    </van-tabs>
    <div></div>
  </div>
</template>

<script>
import accidentAudit from "./components/accidentAudit.vue";
import accidentLedger from "./components/accidentLedger.vue";
import accidentReport from "./components/accidentReport.vue";
import topNav from "../components/topNav.vue";

export default {
  components: {
    accidentReport,
    accidentAudit,
    accidentLedger,
    topNav
  },
  data() {
    return {
      active: 0,
      tasksList: [
        {
          id: 0,
          dictName: "事故上报"
        },
        {
          id: 1,
          dictName: "事故审核"
        },
        {
          id: 2,
          dictName: "事故台账"
        }
      ]
    };
  },
  mounted() {
    this.sysClickBack();
  },
  created() {
    apiready = () => {
      var userInfo = api.getPrefs({
        sync: true,
        key: "userInfo"
      });
      userInfo = JSON.parse(userInfo);
      if (userInfo.id) {
        const virtualToken = encodeURIComponent(userInfo.hospitalName);
        localStorage.setItem("token", virtualToken);
        localStorage.setItem("loginInfo", JSON.stringify(userInfo));
      }
      this.sysClickBack();
    };
  },
  methods: {
    sysClickBack() {
      api.addEventListener(
        {
          name: "keyback666"
        },
        (ret, err) => {
          this.backFn();
        }
      );
    },
    backFn() {
      api.closeFrame({});
      api.clearCache(function() {
      });
    }
  }
};
</script>

<style scoped lang="scss">
@import "../../assets/stylus/theme";
/deep/ .van-tabs__nav--card {
  border: none !important;
  color: $main-bgColor;
}

/deep/ .van-tabs__nav--card .van-tab.van-tab--active {
  background: #fff;
  color: $main-bgColor;
}
/deep/ .van-tabs__nav--card .van-tab {
  color: $text-black-secondTitle;
  border: none !important;
}
.top {
  position: fixed;
  width: 100%;
  z-index: 99;
  background-color: #fff;
}
/deep/ .van-tabs__wrap {
  border-bottom: solid 5px #e6eaf0;
}
header {
  position: fixed;
}
</style>
