<template>
  <div class="connent">
    <!-- <van-overlay :show="overlayShow">
      <van-loading type="spinner" vertical>加载中...</van-loading>
    </van-overlay> -->
    <div class="top">基本信息</div>
    <van-form>
      <van-field
        readonly
        v-model.trim="form.name"
        label="事故名称"
        placeholder="请输入事故名称"
      />
      <van-field
        readonly
        v-model.trim="form.situs"
        label="事故发生地点"
        placeholder="请输入事故发生地点"
      />
      <van-field
        readonly
        clickable
        v-model="form.time"
        label="事故发生时间"
        placeholder="请选择事故发生时间"
        right-icon="arrow"
      />
      <van-field
        readonly
        clickable
        v-model="form.department"
        label="事故责任部门"
        placeholder="请选择事故责任部门"
        right-icon="arrow"
      />
      <div class="remarks">
        <span>事故发生简要经过</span>
        <van-field
          readonly
          v-model="form.accidentSfter"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入事故发生简要经过"
          show-word-limit
          class="message"
        />
      </div>
      <div class="remarks">
        <span>事故人员伤亡情况</span>
        <van-field
          readonly
          v-model="form.accidentCasualty"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入事故人员伤亡情况"
          show-word-limit
          class="message"
        />
      </div>
      <div class="remarks">
        <span>现场处理情况</span>
        <van-field
          readonly
          v-model="form.troubleRemoval"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入现场处理情况"
          show-word-limit
          class="message"
        />
      </div>
      <div class="remarks">
        <span>已采取措施</span>
        <van-field
          readonly
          v-model="form.takeSteps"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入已采取措施"
          show-word-limit
          class="message"
        />
      </div>
      <div class="instructions" style="border: none">
        <div class="explain" style="justify-content: space-between">
          <span style="color: #646566">附件</span>
        </div>
        <div style="width: 95%; margin: 0 auto">
          <van-uploader
            @click-preview="lookDown"
            ref="uplodImg"
            accept=""
            v-model="form.fileList"
            multiple
            :max-count="3"
            disabled
            :deletable="false"
          />
        </div>
      </div>
      <div class="top">审核信息</div>
      <van-field
        readonly
        clickable
        v-model="form.auditDepartment"
        label="审核部门"
        placeholder="请选择审核部门"
        right-icon="arrow"
      />
      <van-popup
        v-model="showDepartment"
        position="bottom"
        v-if="this.rowList != undefined"
      >
        <van-picker
          value-key="teamName"
          show-toolbar
          :columns="typeDepartment"
          @confirm="onDepartment"
          @cancel="showDepartment = false"
        />
      </van-popup>
      <van-field
        readonly
        clickable
        v-model="form.auditPersonnel"
        label="审核人员"
        placeholder="请选择审核人员"
        right-icon="arrow"
      />
    </van-form>
    <div class="radio">
      <van-radio-group v-model="radio" direction="horizontal">
        <span style="color: #646566">审核结果</span>&nbsp;&nbsp;
        <van-radio name="1" icon-size="16px">通过</van-radio>
        <van-radio name="2" icon-size="16px">驳回</van-radio>
      </van-radio-group>
    </div>
    <div class="remarks2">
      <span>审核意见</span>
      <van-field
        v-model="opinion"
        rows="1"
        autosize
        type="textarea"
        maxlength="130"
        placeholder="请输入审核意见"
        show-word-limit
        class="message"
      />
    </div>
    <div class="bottom">
      <van-button
        style="background-color: #00cac8; color: #fff; width: 30%"
        @click="confirm"
        >提交审核</van-button
      >
      <van-button
        style="background-color: #00cac8; color: #fff; width: 25%"
        @click="back"
        >返回</van-button
      >
    </div>
  </div>
</template>

<script>
import moment from "moment";

export default {
  props: {
    rowList: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      form: {
        name: "", //事故名称
        situs: "", //事故地点
        time: "", //事故时间
        department: "", //责任部门
        departmentId: "", //责任部门ID
        accidentSfter: "", //简要经过
        accidentCasualty: "", //伤亡
        troubleRemoval: "", //处理
        takeSteps: "", //措施
        fileList: [],
        auditDepartment: "", //审核部门
        auditDepartmentId: "", //审核部门ID
        auditPersonnel: "", //审核人员
        auditPersonnelId: "", //审核人员ID
        attachmentUrl: [], //附件
      },
      showType: false,
      showTypeChildren: false,
      showDepartment: false,
      showPersonnel: false,
      typeDepartment: [], //审核部门数据
      radio: "",
      opinion: "", //审核意见
      overlayShow: false,
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.detail();
  },
  methods: {
    lookDown(file) {
      var Type = file.name.split(".").pop();
      if (
        Type != "jpg" &&
        Type != "jpeg" &&
        Type != "png" &&
        Type != "JPG" &&
        Type != "JPEG" &&
        Type != "gif"
      ) {
        var localPath = api.cacheDir + "/personalDocuments/" + file.name;

        api.download(
          {
            url: file.url,
            savePath: localPath,
            cache: true,
            allowResume: true,
          },
          function (ret, err) {
            console.log(JSON.stringify(ret));
            if (ret.state == 1) {
              if (api.systemType == "ios") {
                var docReader = api.require("docReader");
                docReader.open(
                  {
                    path: ret.savePath,
                    autorotation: false,
                  },
                  function (ret, err) {}
                );
              } else {
                // 安卓
                // 文档  图片
                var fileType = file.name.split(".").pop();
                if (
                   fileType == "mp4" ||
                  fileType == "mp3" ||
                  fileType == "amr" ||
                  fileType == "MP4" ||
                  fileType == "MP3"
                ) {
                  // 安卓 视屏
                  api.openVideo({
                    url: ret.savePath,
                  });
                } else {
                  var docReader = api.require("docReader");
                  docReader.open(
                    {
                      path: ret.savePath,
                      autorotation: false,
                    },
                    function (ret, err) {}
                  );
                }
              }
            } else {
            }
          }
        );
      }
    },
    //审核部门
    onDepartment(val) {
      this.form.auditDepartment = val.teamName;
      this.form.auditDepartmentId = val.id;
      this.showDepartment = false;
      let params = {
        platformFlag: 2,
        roleCode: this.loginInfo.roleCode,
        departId: val.id,
      };
      this.axios.postContralHostBase("getUseListByDeptId", params, (res) => {
        if (res.code == 200) {
          this.typePersonnel = res.data;
        }
      });
    },
    //返回
    back() {
      this.$emit("button", false);
    },
    //获取详情
    detail() {
      this.overlayShow = true;

      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        id: this.rowList.id,
      };
      this.axios.postContralHostBase("details", params, (res) => {
        if (res.code == 200) {
          this.overlayShow = false;

          this.form.name = res.data.accident.accidentName;
          this.form.situs = res.data.accident.accidentPlace;
          this.form.time = res.data.accident.createTime;
          this.form.department = res.data.accident.dutyDeptName;
          this.form.departmentId = res.data.accident.dutyDeptId;
          this.form.accidentSfter = res.data.accident.accidentCourse;
          this.form.accidentCasualty = res.data.accident.accidentCasualties;
          this.form.troubleRemoval = res.data.accident.accidentDispose;
          this.form.takeSteps = res.data.accident.accidentMeasures;
          res.data.accident.attachmentList.forEach((item) => {
            item.file = new File([], item.name, {});
          });
          this.form.fileList = res.data.accident.attachmentList;

          this.onDepartment(res.data.accident.auditDeptId);
          this.form.auditDepartment = res.data.accident.auditDeptName;
          this.form.auditDepartmentId = res.data.accident.auditDeptId;
          this.form.auditPersonnel = res.data.accident.auditPersonName;
          this.form.auditPersonnelId = res.data.accident.auditPersonId;
          this.form.attachmentUrl = JSON.parse(res.data.accident.attachmentArr);
        }
      });
    },
    //点击提交
    confirm() {
      let inf0obj = this.loginInfo;
      let params = {
        // unitCode: this.loginInfo.unitCode,
        // hospitalCode: this.loginInfo.hospitalCode,
        id: this.rowList.id,
        auditStatus: this.radio,
        auditOpinion: this.opinion,
      };
      if (params.auditStatus == "") {
        return this.$toast.fail("请勾选审批结果");
      }
      this.axios.postContralHostBase(
        "audit",
        params,
        (res) => {
          if (res.code == 200) {
            this.$toast.success(res.message);
            this.back();
          } else {
            this.$toast.fail(res.message);
          }
        },
        inf0obj
      );
    },
  },
};
</script>

<style scoped lang="scss">
@import "../../../assets/stylus/theme";
.connent {
  padding: 0 15px;
  font-size: 14px;
  .top {
    border-left: 5px solid $main-bgColor;
    padding-left: 3px;
    color: $main-bgColor;
  }
  .remarks {
    background-color: #fff;
    margin: 0.3125rem 0 0 0;
    padding: 0.3125rem;
    border-bottom: 1px solid #f6f7f8;
    .van-field {
      height: 70px;
      overflow-y: scroll;
    }
    span {
      color: #646566;
    }
    .message {
      background-color: #f4f5f9;
      margin-top: 0.3125rem;
    }
  }
  .instructions {
    border-bottom: 4px solid #ebedf0;
  }
  .instructions .explain {
    background-color: #fff;
    min-height: 48px;
    display: flex;
    align-items: center;
    padding: 0 10px;
  }
  .instructions .explain span {
    font-size: 14px;
  }
  .bottom {
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
    width: 100%;
    height: 60px;
    padding-bottom: 15px;
    border-top: 1px solid #f6f7f8;
    position: fixed;
    left: 0;
    bottom: 0;
    background-color: #fff;
  }
}
.radio {
  // padding: 0.3125rem;
  padding-left: 0.3125rem;
  line-height: 35px;
}
.remarks2 {
  background-color: #fff;
  padding: 0 0.3125rem 0 0.3125rem;
  border-bottom: 1px solid #f6f7f8;
  .van-field {
    height: 70px;
    overflow-y: scroll;
  }
  span {
    color: #646566;
  }
  .message {
    background-color: #f4f5f9;
    margin-top: 0.3125rem;
  }
}
.van-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
}
.van-button {
  border-radius: 5px;
}
.van-cell {
  padding: 10px 7px !important;
}
</style>
