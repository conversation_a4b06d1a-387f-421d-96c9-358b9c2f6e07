<template>
  <div class="connent">
    <!-- <van-overlay :show="overlayShow">
      <van-loading type="spinner" vertical>加载中...</van-loading>
    </van-overlay> -->
    <div class="top">基本信息</div>
    <van-form>
      <van-field
        readonly
        v-model.trim="form.name"
        label="事故名称"
        placeholder="请输入事故名称"
      />
      <van-field
        readonly
        v-model.trim="form.situs"
        label="事故发生地点"
        placeholder="请输入事故发生地点"
      />
      <van-field
        readonly
        clickable
        v-model="form.time"
        label="事故发生时间"
        placeholder="请选择事故发生时间"
        right-icon="arrow"
      />
      <van-field
        readonly
        clickable
        v-model="form.department"
        label="事故责任部门"
        placeholder="请选择事故责任部门"
        right-icon="arrow"
      />
      <div class="remarks">
        <span>事故发生简要经过</span>
        <van-field
          readonly
          v-model="form.accidentSfter"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入事故发生简要经过"
          show-word-limit
          class="message"
        />
      </div>
      <div class="remarks">
        <span>事故人员伤亡情况</span>
        <van-field
          readonly
          v-model="form.accidentCasualty"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入事故人员伤亡情况"
          show-word-limit
          class="message"
        />
      </div>
      <div class="remarks">
        <span>现场处理情况</span>
        <van-field
          readonly
          v-model="form.troubleRemoval"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入现场处理情况"
          show-word-limit
          class="message"
        />
      </div>
      <div class="remarks">
        <span>已采取措施</span>
        <van-field
          readonly
          v-model="form.takeSteps"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入已采取措施"
          show-word-limit
          class="message"
        />
      </div>
      <div class="instructions" style="border: none">
        <div class="explain" style="justify-content: space-between">
          <span style="color: #646566">附件</span>
        </div>
        <div style="width: 95%; margin: 0 auto">
          <van-uploader
            @click-preview="lookDown"
            ref="uplodImg"
            accept=""
            v-model="form.fileList"
            multiple
            :max-count="3"
            disabled
            :deletable="false"
          />
        </div>
      </div>
      <div class="top">审核信息</div>
      <van-field
        readonly
        clickable
        v-model="form.auditDepartment"
        label="审核部门"
        placeholder="请选择审核部门"
        right-icon="arrow"
      />
      <van-popup
        v-model="showDepartment"
        position="bottom"
        v-if="this.rowList != undefined"
      >
        <van-picker
          value-key="teamName"
          show-toolbar
          :columns="typeDepartment"
          @confirm="onDepartment"
          @cancel="showDepartment = false"
        />
      </van-popup>
      <van-field
        readonly
        clickable
        v-model="form.auditPersonnel"
        label="审核人员"
        placeholder="请选择审核人员"
        right-icon="arrow"
      />
    </van-form>
    <div class="radio">
      <van-radio-group v-model="radio" direction="horizontal">
        审核结果
        <van-radio name="1" disabled icon-size="16px">通过</van-radio>
        <van-radio name="2" disabled icon-size="16px">驳回</van-radio>
      </van-radio-group>
    </div>
    <div class="remarks2">
      <span style="color: #646566">审核意见</span>
      <van-field
        readonly
        v-model="opinion"
        rows="1"
        autosize
        type="textarea"
        maxlength="130"
        placeholder="请输入审核意见"
        show-word-limit
        class="message"
      />
    </div>
    <div v-if="rowList.accidentStatusName == '已调查处理'">
      <div class="top">事故调查处理信息</div>
      <div class="radio1">
        <div>事故调查处理类型</div>
        <van-radio-group v-model="radio1" class="lh" direction="horizontal">
          <van-radio name="1" disabled icon-size="16px"
            >单位内部调查处理</van-radio
          >
          <van-radio name="2" disabled icon-size="16px">单位外部调查</van-radio>
        </van-radio-group>
      </div>
      <div class="top">调查人员信息</div>
      <div class="number">
        <div
          v-for="(item, index) in form.information"
          :key="index"
          class="information"
        >
          <div>
            姓名<span> {{ item.name }}</span>
          </div>
          <div>
            职务<span> {{ item.job }}</span>
          </div>
          <div>
            单位/部门<span> {{ item.department }}</span>
          </div>
          <div>
            联系方式<span> {{ item.phone }}</span>
          </div>
        </div>
      </div>
      <div class="top">调查处理结果</div>
      <van-field
        readonly
        v-model.trim="form.type"
        label="事故类型"
        placeholder="请输入事故类型"
      />
      <van-field
        readonly
        v-model.trim="form.grade"
        label="事故等级"
        placeholder="请输入事故等级"
      />
      <van-field
        readonly
        v-model.trim="form.deathToll"
        label="死亡人数"
        placeholder="请输入死亡人数"
      />
      <van-field
        readonly
        v-model.trim="form.seriousInjury"
        label="重伤人数"
        placeholder="请输入重伤人数"
      />
      <van-field
        readonly
        v-model.trim="form.minorInjuries"
        label="轻伤人数"
        placeholder="请输入轻伤人数"
      />
      <van-field
        readonly
        v-model.trim="form.missingNumber"
        label="失踪人数"
        placeholder="请输入失踪人数"
      />
      <van-field
        readonly
        v-model.trim="form.directDamage"
        label="直接经济损失(万元)"
        placeholder="请输入直接经济损失"
      />
      <van-field
        readonly
        v-model.trim="form.indirectloss"
        label="间接经济损失(万元)"
        placeholder="请输入间接经济损失"
      />
      <div class="remarks2">
        <span>事故发生经过</span>
        <van-field
          readonly
          v-model="form.through"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入事故发生经过"
          show-word-limit
          class="message"
        />
      </div>
      <van-field
        readonly
        v-model.trim="form.principal"
        label="事故主要负责人"
        placeholder="请输入事故主要负责人"
      />
      <van-field
        readonly
        v-model.trim="form.secondary"
        label="事故次要负责人"
        placeholder="请输入事故次要负责人"
      />
      <div class="remarks2">
        <span>事故处置意见</span>
        <van-field
          readonly
          v-model="form.dispositionOpinions"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入事故处置意见"
          show-word-limit
          class="message"
        />
      </div>
      <div class="remarks2">
        <span>事故防范建议</span>
        <van-field
          readonly
          v-model="form.preventiveAdvice"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入事故防范建议"
          show-word-limit
          class="message"
        />
      </div>
      <div class="remarks2">
        <span>防范措施建议</span>
        <van-field
          readonly
          v-model="form.suggest"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入防范措施建议"
          show-word-limit
          class="message"
        />
      </div>
      <div class="instructions" style="border: none">
        <div class="explain" style="justify-content: space-between">
          <span style="color: #646566">附件</span>
        </div>
        <div style="width: 95%; margin: 0 auto">
          <van-uploader
            @click-preview="lookDown"
            ref="uplodImg"
            accept=""
            v-model="form.fileList2"
            multiple
            :max-count="3"
            disabled
            :deletable="false"
          />
        </div>
      </div>
    </div>
    <div class="bottom">
      <!-- <van-button
        style="background-color: #00cac8; color: #fff; width: 30%"
        @click="confirm"
        >提交审核</van-button
      > -->
      <van-button
        style="background-color: #00cac8; color: #fff; width: 25%"
        @click="back"
        >返回</van-button
      >
    </div>
  </div>
</template>

<script>
import moment from "moment";

export default {
  props: {
    rowList: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      form: {
        name: "", //事故名称
        situs: "", //事故地点
        time: "", //事故时间
        department: "", //责任部门
        departmentId: "", //责任部门ID
        accidentSfter: "", //简要经过
        accidentCasualty: "", //伤亡
        troubleRemoval: "", //处理
        takeSteps: "", //措施
        fileList: [],
        auditDepartment: "", //审核部门
        auditDepartmentId: "", //审核部门ID
        auditPersonnel: "", //审核人员
        auditPersonnelId: "", //审核人员ID
        attachmentUrl: [], //附件
        ///////////
        information: [], //调查人员信息
        type: "", //事故类型
        grade: "", //事故等级
        deathToll: "", //死亡人数
        seriousInjury: "", //重伤人数
        minorInjuries: "", //轻伤人数
        missingNumber: "", //失踪人数
        directDamage: "", //直接损失
        indirectloss: "", //间接损失
        through: "", //事故发生经过
        principal: "", //主要负责人
        secondary: "", //次要负责人
        dispositionOpinions: "", //处置意见
        preventiveAdvice: "", //防范建议
        suggest: "", //措施建议
        fileList2: [],
      },
      showType: false,
      showTypeChildren: false,
      showDepartment: false,
      showPersonnel: false,
      typeDepartment: [], //审核部门数据
      radio: "",
      opinion: "", //审核意见
      radio1: "",
      overlayShow: false,
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.detail();
  },
  methods: {
    lookDown(file) {
      var Type = file.name.split(".").pop();
      if (
        Type != "jpg" &&
        Type != "jpeg" &&
        Type != "png" &&
        Type != "JPG" &&
        Type != "JPEG" &&
        Type != "gif"
      ) {
        var localPath = api.cacheDir + "/personalDocuments/" + file.name;

        api.download(
          {
            url: file.url,
            savePath: localPath,
            cache: true,
            allowResume: true,
          },
          function (ret, err) {
            console.log(JSON.stringify(ret));
            if (ret.state == 1) {
              if (api.systemType == "ios") {
                var docReader = api.require("docReader");
                docReader.open(
                  {
                    path: ret.savePath,
                    autorotation: false,
                  },
                  function (ret, err) {}
                );
              } else {
                // 安卓
                // 文档  图片
                var fileType = file.name.split(".").pop();
                if (
                  fileType == "mp4" ||
                  fileType == "mp3" ||
                  fileType == "amr" ||
                  fileType == "MP4" ||
                  fileType == "MP3"
                ) {
                  // 安卓 视屏
                  api.openVideo({
                    url: ret.savePath,
                  });
                } else {
                  var docReader = api.require("docReader");
                  docReader.open(
                    {
                      path: ret.savePath,
                      autorotation: false,
                    },
                    function (ret, err) {}
                  );
                }
              }
            } else {
            }
          }
        );
      }
    },
    //审核部门
    onDepartment(val) {
      this.form.auditDepartment = val.teamName;
      this.form.auditDepartmentId = val.id;
      this.showDepartment = false;
      let params = {
        platformFlag: 2,
        roleCode: this.loginInfo.roleCode,
        departId: val.id,
      };
      this.axios.postContralHostBase("getUseListByDeptId", params, (res) => {
        if (res.code == 200) {
          this.typePersonnel = res.data;
        }
      });
    },
    //返回
    back() {
      this.$emit("button", false);
    },
    //获取详情
    detail() {
      this.overlayShow = true;
      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        id: this.rowList.id,
      };
      this.loading = true;
      this.axios.postContralHostBase("details", params, (res) => {
        if (res.code == 200) {
          this.overlayShow = false;
          this.form.name = res.data.accident.accidentName;
          this.form.situs = res.data.accident.accidentPlace;
          this.form.time = res.data.accident.createTime;
          this.form.department = res.data.accident.dutyDeptName;
          this.form.departmentId = res.data.accident.dutyDeptId;
          this.form.accidentSfter = res.data.accident.accidentCourse;
          this.form.accidentCasualty = res.data.accident.accidentCasualties;
          this.form.troubleRemoval = res.data.accident.accidentDispose;
          this.form.takeSteps = res.data.accident.accidentMeasures;
          res.data.accident.attachmentList.forEach((item) => {
            item.file = new File([], item.name, {});
          });
          this.form.fileList = res.data.accident.attachmentList;

          this.onDepartment(res.data.accident.auditDeptId);
          this.form.auditDepartment = res.data.accident.auditDeptName;
          this.form.auditDepartmentId = res.data.accident.auditDeptId;
          this.form.auditPersonnel = res.data.accident.auditPersonName;
          this.form.auditPersonnelId = res.data.accident.auditPersonId;
          console.log(this.form.departmentId, "ssssssss");
          this.radio = res.data.accident.auditStatus;
          this.opinion = res.data.accident.auditOpinion;
          ////////////////
          this.form.information = JSON.parse(
            res.data.accidentDispose.surveyUser
          );

          this.form.type = res.data.accidentDispose.accidentTypeName;
          this.form.grade = res.data.accidentDispose.accidentLevelName;
          this.form.deathToll = res.data.accidentDispose.deathNum;
          this.form.seriousInjury = res.data.accidentDispose.seriousNum;
          this.form.minorInjuries = res.data.accidentDispose.minorNum;
          this.form.missingNumber = res.data.accidentDispose.missingNum;
          this.form.directDamage = res.data.accidentDispose.directLoss;
          this.form.indirectloss = res.data.accidentDispose.indirectLoss;
          this.form.through = res.data.accidentDispose.happenCourse;
          this.form.principal = res.data.accidentDispose.mainDutyPerson;
          this.form.secondary = res.data.accidentDispose.minorDutyPerson;
          this.form.dispositionOpinions =
            res.data.accidentDispose.handleOpinion;
          this.form.preventiveAdvice =
            res.data.accidentDispose.preventionAdvice;
          this.form.suggest = res.data.accidentDispose.measureAdvice;
          res.data.accidentDispose.attachmentList.forEach((item) => {
            item.file = new File([], item.name, {});
          });

          this.form.fileList2 = res.data.accidentDispose.attachmentList;
          this.radio1 = res.data.accidentDispose.disposeType;
          this.form.attachmentUrl = JSON.parse(res.data.accident.attachmentArr);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
@import "../../../assets/stylus/theme";
.connent {
  padding: 0 15px;
  font-size: 14px;
  .top {
    border-left: 5px solid $main-bgColor;
    padding-left: 3px;
    color: $main-bgColor;
  }
  .remarks {
    background-color: #fff;
    margin: 0.3125rem 0 0 0;
    padding: 0.3125rem;
    border-bottom: 1px solid #f6f7f8;
    .van-field {
      height: 70px;
      overflow-y: scroll;
    }
    span {
      color: #646566;
    }
    .message {
      background-color: #f4f5f9;
      margin-top: 0.3125rem;
    }
  }
  .instructions {
    border-bottom: 4px solid #ebedf0;
  }
  .instructions .explain {
    background-color: #fff;
    min-height: 48px;
    display: flex;
    align-items: center;
    padding: 0 10px;
  }
  .instructions .explain span {
    font-size: 14px;
  }
  .bottom {
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
    width: 100%;
    height: 60px;
    padding-bottom: 15px;
    border-top: 1px solid #f6f7f8;
    position: fixed;
    left: 0;
    bottom: 0;
    background-color: #fff;
  }
}
.radio {
  line-height: 35px;
  color: #646566;
  div {
    padding-left: 7px;
  }
}
.radio1 {
  padding: 0.3125rem 0;
  color: #646566;
  div {
    padding-left: 8px;
  }
  .van-radio {
    padding: 0 !important;
  }
}
.remarks2 {
  background-color: #fff;
  padding: 0.3125rem;
  border-bottom: 1px solid #f6f7f8;
  .van-field {
    height: 70px;
    overflow-y: scroll;
  }
  span {
    color: #646566;
  }
  .message {
    background-color: #f4f5f9;
    margin-top: 0.3125rem;
  }
}
/deep/ .van-field__label {
  width: 9.2em;
}
.information {
  padding: 10px 10px 10px 10px;
  div {
    line-height: 40px;
    border-bottom: 1px solid #f6f7f8;
    color: #646566;
    span {
      color: #000;
    }
  }
}
.lh {
  line-height: 15px;
  padding-left: 8px;
  /deep/ .van-radio {
    margin-top: 10px;
  }
}
.van-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
}
.van-button {
  border-radius: 5px;
}
.van-cell {
  padding: 10px 7px !important;
}
.number {
  height: 185px;
  overflow-y: scroll;
}
</style>
