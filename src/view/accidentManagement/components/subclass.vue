<template>
  <div class="connent">
    <!-- <van-overlay :show="overlayShow">
      <van-loading type="spinner" vertical>加载中...</van-loading>
    </van-overlay> -->
    <div class="top">基本信息</div>
    <van-form ref="form">
      <van-field
        :readonly="
          this.rowList.accidentStatus == '4' ||
          this.rowList.accidentStatus == '2' ||
          this.rowList.accidentStatus == '5'
        "
        v-model.trim="form.name"
        label="事故名称"
        placeholder="请输入事故名称"
        :rules="[{ required: true }]"
      />
      <van-field
        :readonly="
          this.rowList.accidentStatus == '4' ||
          this.rowList.accidentStatus == '2' ||
          this.rowList.accidentStatus == '5'
        "
        v-model.trim="form.situs"
        label="事故发生地点"
        placeholder="请输入事故发生地点"
        :rules="[{ required: true }]"
      />
      <van-field
        readonly
        clickable
        v-model="form.time"
        label="事故发生时间"
        placeholder="请选择事故发生时间"
        right-icon="arrow"
        @click="claimClassification()"
        :rules="[{ required: true }]"
      />
      <van-popup
        v-model="showType"
        position="bottom"
        v-if="
          this.rowList.accidentStatus == '1' ||
          this.rowList.accidentStatus == '3'
        "
      >
        <van-datetime-picker
          type="datetime"
          title="选择完整时间"
          @confirm="onType"
          @cancel="showType = false"
        />
      </van-popup>
      <van-field
        readonly
        clickable
        v-model="form.department"
        label="事故责任部门"
        placeholder="请选择事故责任部门"
        right-icon="arrow"
        @click="claimDepartment()"
        :rules="[{ required: true }]"
      />
      <van-popup
        v-model="showTypeChildren"
        position="bottom"
        v-if="
          this.rowList.accidentStatus == '1' ||
          this.rowList.accidentStatus == '3'
        "
      >
        <van-picker
          value-key="teamName"
          show-toolbar
          :columns="typeChildrenList"
          @confirm="onTypeChildren"
          @cancel="showTypeChildren = false"
        />
      </van-popup>
      <div class="remarks">
        <span>事故发生简要经过</span>
        <van-field
          :readonly="
            this.rowList.accidentStatus == '4' ||
            this.rowList.accidentStatus == '2' ||
            this.rowList.accidentStatus == '5'
          "
          v-model="form.accidentSfter"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入事故发生简要经过"
          show-word-limit
          class="message"
        />
      </div>
      <div class="remarks">
        <span>事故人员伤亡情况</span>
        <van-field
          :readonly="
            this.rowList.accidentStatus == '4' ||
            this.rowList.accidentStatus == '2' ||
            this.rowList.accidentStatus == '5'
          "
          v-model="form.accidentCasualty"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入事故人员伤亡情况"
          show-word-limit
          class="message"
        />
      </div>
      <div class="remarks">
        <span>现场处理情况</span>
        <van-field
          :readonly="
            this.rowList.accidentStatus == '4' ||
            this.rowList.accidentStatus == '2' ||
            this.rowList.accidentStatus == '5'
          "
          v-model="form.troubleRemoval"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入现场处理情况"
          show-word-limit
          class="message"
        />
      </div>
      <div class="remarks">
        <span>已采取措施</span>
        <van-field
          :readonly="
            this.rowList.accidentStatus == '4' ||
            this.rowList.accidentStatus == '2' ||
            this.rowList.accidentStatus == '5'
          "
          v-model="form.takeSteps"
          rows="1"
          autosize
          type="textarea"
          maxlength="1000"
          placeholder="请输入已采取措施"
          show-word-limit
          class="message"
        />
      </div>
      <div class="instructions" style="border: none">
        <div class="explain" style="justify-content: space-between">
          <span style="color: #646566">附件</span>
          <p class="upload-img" style="color: #646566">
            注：上传本地文件，最多上传三张
          </p>
        </div>
        <div style="width: 95%; margin: 0 auto">
          <van-uploader
            @click-preview="lookDown"
            :max-size="maxSize * 1024 * 1024"
            @oversize="onOversize"
            ref="uplodImg"
            accept=""
            v-model="form.fileList"
            multiple
            :after-read="afterRead"
            @delete="deleteImg"
            :max-count="3"
            :disabled="
              this.rowList.accidentStatus == '4' ||
              this.rowList.accidentStatus == '2' ||
              this.rowList.accidentStatus == '5'
            "
            :deletable="
              this.rowList.accidentStatus == '1' ||
              this.rowList.accidentStatus == '3'
            "
          />
        </div>
      </div>
      <div class="top">审核信息</div>
      <van-field
        readonly
        clickable
        v-model="form.auditDepartment"
        label="审核部门"
        placeholder="请选择审核部门"
        right-icon="arrow"
        @click="claimauditDepartment()"
        :rules="[{ required: true }]"
      />
      <van-popup
        v-model="showDepartment"
        position="bottom"
        v-if="
          this.rowList.accidentStatus == '1' ||
          this.rowList.accidentStatus == '3'
        "
      >
        <van-picker
          value-key="teamName"
          show-toolbar
          :columns="typeDepartment"
          @confirm="onDepartment"
          @cancel="showDepartment = false"
        />
      </van-popup>
      <van-field
        readonly
        clickable
        v-model="form.auditPersonnel"
        label="审核人员"
        placeholder="请选择审核人员"
        right-icon="arrow"
        @click="claimPersonnel()"
        :rules="[{ required: true }]"
      />
      <van-popup
        v-model="showPersonnel"
        position="bottom"
        v-if="
          this.rowList.accidentStatus == '1' ||
          this.rowList.accidentStatus == '3'
        "
      >
        <van-picker
          value-key="name"
          show-toolbar
          :columns="typePersonnel"
          @confirm="onPersonnel"
          @cancel="showPersonnel = false"
        />
      </van-popup>
    </van-form>
    <div v-if="this.rowList.accidentStatus == 3">
      <div class="top">审核意见</div>

      <div class="radio">
        <van-radio-group v-model="radio" direction="horizontal">
          <span style="color: #646566">审核结果</span>&nbsp;&nbsp;
          <van-radio name="1" icon-size="16px" disabled>通过</van-radio>
          <van-radio name="2" icon-size="16px" disabled>驳回</van-radio>
        </van-radio-group>
      </div>
      <div class="remarks2">
        <span>审核意见</span>
        <van-field
          disabled
          v-model="opinion"
          rows="1"
          autosize
          type="textarea"
          maxlength="130"
          placeholder="请输入审核意见"
          show-word-limit
          class="message"
        />
      </div>
    </div>
    <div class="bottom">
      <van-button
        v-if="
          this.rowList.accidentStatus == '1' ||
          this.rowList.accidentStatus == '3'
        "
        style="background-color: #00cac8; color: #fff; width: 30%"
        @click="confirm"
        >提交审核</van-button
      >
      <van-button
        v-if="
          this.rowList.accidentStatus == '1' ||
          this.rowList.accidentStatus == '3' ||
          this.rowList.accidentStatus == '2'
        "
        style="background-color: #00cac8; color: #fff; width: 25%"
        @click="dele"
        >删除</van-button
      >
      <van-button
        style="background-color: #00cac8; color: #fff; width: 25%"
        @click="back"
        >返回</van-button
      >
    </div>
  </div>
</template>

<script>
import moment from "moment";
import YBS from "../../../centralControl/utils/utils.js";

export default {
  props: {
    rowList: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      form: {
        name: "", //事故名称
        situs: "", //事故地点
        time: "", //事故时间
        department: "", //责任部门
        departmentId: "", //责任部门ID
        accidentSfter: "", //简要经过
        accidentCasualty: "", //伤亡
        troubleRemoval: "", //处理
        takeSteps: "", //措施
        fileList: [],
        auditDepartment: "", //审核部门
        auditDepartmentId: "", //审核部门ID
        auditPersonnel: "", //审核人员
        auditPersonnelId: "", //审核人员ID
        attachmentUrl: [], //附件
      },
      showType: false,
      showTypeChildren: false,
      showDepartment: false,
      showPersonnel: false,
      currentDate: "",
      typeChildrenList: [], //责任部门数据
      typeDepartment: [], //审核部门数据
      typePersonnel: [], //审核人员数据
      maxSize: 20,
      overlayShow: false,
      radio: "",
      opinion: "", //审核意见
    };
  },
  created() {
    console.log(this.rowList, "this.rowList");
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.typeChildrenLists();
    this.gettingData();
    if (this.rowList != undefined) {
      this.detail();
    } else {
      return;
    }
  },
  methods: {
    lookDown(file) {
      var Type = file.name.split(".").pop();

      if (
        Type != "jpg" &&
        Type != "jpeg" &&
        Type != "png" &&
        Type != "JPG" &&
        Type != "JPEG" &&
        Type != "gif"
      ) {
        var localPath = api.cacheDir + "/personalDocuments/" + file.name;
        api.download(
          {
            url: file.url,
            savePath: localPath,
            cache: true,
            allowResume: true,
          },
          function (ret, err) {
            console.log(JSON.stringify(ret));
            if (ret.state == 1) {
              if (api.systemType == "ios") {
                var docReader = api.require("docReader");
                docReader.open(
                  {
                    path: ret.savePath,
                    autorotation: false,
                  },
                  function (ret, err) {}
                );
              } else {
                // 安卓
                // 文档  图片
                var fileType = file.name.split(".").pop();
                if (
                  fileType == "mp4" ||
                  fileType == "mp3" ||
                  fileType == "amr" ||
                  fileType == "MP4" ||
                  fileType == "MP3"
                ) {
                  // 安卓 视屏
                  api.openVideo({
                    url: ret.savePath,
                  });
                } else {
                  var docReader = api.require("docReader");
                  docReader.open(
                    {
                      path: ret.savePath,
                      autorotation: false,
                    },
                    function (ret, err) {}
                  );
                }
              }
            } else {
            }
          }
        );
      }
    },
    //删除
    dele() {
      this.$dialog
        .confirm({
          message: "确认删除该事故信息？",
        })
        .then(() => {
          let params = {
            unitCode: this.loginInfo.unitCode,
            hospitalCode: this.loginInfo.hospitalCode,
            id: this.rowList.id,
          };
          this.axios.postContralHostBase("delete", params, (res) => {
            if (res.code == 200) {
              this.$toast.success(res.message);
              this.$emit("button", false);
            }
          });
        })
        .catch(() => {
          this.$message({
            message: "已取消",
          });
        });
    },
    //返回
    back() {
      this.$emit("button", false);
    },
    //获取详情
    detail() {
      this.overlayShow = true;
      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        id: this.rowList.id,
      };
      this.axios.postContralHostBase("details", params, (res) => {
        if (res.code == 200) {
          this.overlayShow = false;

          this.form.name = res.data.accident.accidentName;
          this.form.situs = res.data.accident.accidentPlace;
          this.form.time = res.data.accident.createTime;
          this.form.department = res.data.accident.dutyDeptName;
          this.form.departmentId = res.data.accident.dutyDeptId;
          this.form.accidentSfter = res.data.accident.accidentCourse;
          this.form.accidentCasualty = res.data.accident.accidentCasualties;
          this.form.troubleRemoval = res.data.accident.accidentDispose;
          this.form.takeSteps = res.data.accident.accidentMeasures;
          res.data.accident.attachmentList.forEach((item) => {
            item.file = new File([], item.name, {});
          });
          this.form.fileList = res.data.accident.attachmentList;

          //   this.form.fileList = res.data.accident.attachmentList[0];
          this.onDepartment(res.data.accident.auditDeptId);
          this.form.auditDepartment = res.data.accident.auditDeptName;
          this.form.auditDepartmentId = res.data.accident.auditDeptId;
          this.form.auditPersonnel = res.data.accident.auditPersonName;
          this.form.auditPersonnelId = res.data.accident.auditPersonId;
          this.form.attachmentUrl = JSON.parse(res.data.accident.attachmentArr);
          this.radio = res.data.accident.auditStatus;
          this.opinion = res.data.accident.auditOpinion;
        }
      });
    },
    //点击提交
    confirm() {
      this.$refs.form.validate().then(() => {
        let params = {
          hospitalCode: this.loginInfo.hospitalCode,
          unitCode: this.loginInfo.unitCode,
          accidentName: this.form.name, //事故名称
          accidentPlace: this.form.situs, //事故地点
          accidentTime: this.form.time, //事故时间
          dutyDeptId: this.form.departmentId, //事故责任部门ID
          dutyDeptName: this.form.department, //事故责任部门名称
          accidentCourse: this.form.accidentSfter, //事故发生简要经过
          accidentCasualties: this.form.accidentCasualty, //事故人员伤亡情况
          accidentDispose: this.form.troubleRemoval, //现场处理情况
          accidentMeasures: this.form.takeSteps, //已采取措施
          // attachmentArr: this.form.attachmentUrl, //附件
          accidentStatus: 2, //事故状态（1.待提交 2.待审核）
          auditDeptId: this.form.auditDepartmentId, //审核部门ID
          auditDeptName: this.form.auditDepartment, //审核部门名称
          auditPersonId: this.form.auditPersonnelId, //审核人员ID
          auditPersonName: this.form.auditPersonnel, //审核人员名称
          id: this.rowList.id,
        };
        if (this.form.attachmentUrl != 0) {
          params.attachmentArr = JSON.stringify(this.form.attachmentUrl);
        }
        this.axios.postContralHostBase("save", params, (res) => {
          if (res.code == 200) {
            this.back();
            this.$toast.success(res.message);
          } else {
            this.$toast.fail(res.message);
          }
        });
      });
    },
    gettingData() {
      let params = {
        platformFlag: 2,
        roleCode: this.loginInfo.roleCode,
      };
      this.axios.postContralHostBase("selectDepartList", params, (res) => {
        if (res.code == 200) {
          this.typeChildrenList = res.data;
          this.typeDepartment = res.data;
        }
      });
    },
    claimClassification() {
      this.showType = true;
    },
    onType(val) {
      this.form.time = moment(val).format("YYYY-MM-DD HH:mm:ss");
      this.showType = false;
    },
    //责任部门
    onTypeChildren(val) {
      this.form.department = val.teamName;
      this.form.departmentId = val.id;
      this.showTypeChildren = false;
    },
    claimDepartment() {
      this.showTypeChildren = true;
    },
    //审核部门
    onDepartment(val) {
      this.form.auditDepartment = val.teamName;
      this.form.auditDepartmentId = val.id;
      this.showDepartment = false;
      let params = {
        platformFlag: 2,
        roleCode: this.loginInfo.roleCode,
        departId: val.id,
      };
      this.axios.postContralHostBase("getUseListByDeptId", params, (res) => {
        if (res.code == 200) {
          this.typePersonnel = res.data;
        }
        console.log(res);
      });
    },
    claimauditDepartment() {
      this.showDepartment = true;
    },
    //审核人员
    onPersonnel(val) {
      this.form.auditPersonnel = val.name;
      this.form.auditPersonnelId = val.id;
      this.showPersonnel = false;
    },
    claimPersonnel() {
      if (!this.form.auditDepartment) {
        this.$toast.fail("请先选择审核部门");
        return;
      }
      this.showPersonnel = true;
    },
    typeChildrenLists() {},
    onOversize() {
      this.$toast.fail(`文件大小不能超过${this.maxSize}M`);
    },
    // 图片选择
    afterRead(files) {
      this.form.fileList.forEach((i) => {
        return (i.status = "uploading");
      });
      const params = {
        file: "",
      };
      if (files.length) {
        console.log(files, "ssss");
        let formData = new FormData();
        files.forEach((item) => {
          formData.append("file", item.file);
        });
        formData.append("hospitalCode", this.loginInfo.hospitalCode);
        axios
          .post(__PATH.BASEURL + "file/upload", formData)
          .then((res) => {
            if (res.data.code == 200) {
              this.form.fileList.forEach((i) => {
                return (i.status = "done");
              });
              const imgUrl = res.data.data.fileKey.split(",");
              imgUrl.forEach((i, index) => {
                const item = {
                  name: files[index].file.name,
                  fileKey: i,
                };
                this.form.attachmentUrl.push(item);
                // this.form.fileList.push(item);
              });
            }
          })
          .catch(() => {
            this.form.fileList.forEach((i) => {
              return (i.status = "failed");
            });
            this.$toast.fail("上传失败");
          });
      } else {
        params.file = files.file;
        this.subImg(params);
      }
    },
    subImg(params) {
      console.log(params, "params");
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        if ((res.code = "200")) {
          const item = {
            name: params.file.name,
            url: res.data.fileKey,
          };
          // this.form.fileList.push(item);
          this.form.attachmentUrl.push(item);
          console.log(this.form.attachmentUrl, " this.attachmentUrl");
          this.form.fileList.forEach((i) => {
            return (i.status = "done");
          });
        }
      });
    },
    //删除图片
    deleteImg(e) {
      this.form.attachmentUrl = this.form.attachmentUrl.filter(
        (i) => i.name != e.name
      );
    },
  },
};
</script>

<style scoped lang="scss">
@import "../../../assets/stylus/theme";
.connent {
  margin-bottom: 70px;
  padding: 0 15px;
  font-size: 14px;
  .top {
    border-left: 5px solid $main-bgColor;
    padding-left: 3px;
    color: $main-bgColor;
  }
  .remarks {
    background-color: #fff;
    margin: 0.3125rem 0 0 0;
    padding: 0.3125rem;
    border-bottom: 1px solid #f6f7f8;
    .van-field {
      height: 70px;
      overflow-y: scroll;
    }
    span {
      color: #646566;
    }
    .message {
      background-color: #f4f5f9;
      margin-top: 0.3125rem;
    }
  }
  .instructions {
    border-bottom: 4px solid #ebedf0;
  }
  .instructions .explain {
    background-color: #fff;
    min-height: 48px;
    display: flex;
    align-items: center;
    padding: 0 10px;
  }
  .instructions .explain span {
    font-size: 14px;
  }
  .bottom {
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
    width: 100%;
    height: 60px;
    padding-bottom: 15px;
    border-top: 1px solid #f6f7f8;
    position: fixed;
    bottom: 0;
    left: 0;
    background-color: #fff;
  }
}
.van-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
}
.van-button {
  border-radius: 5px;
}
.van-cell {
  padding: 10px 7px !important;
}
.remarks2 {
  background-color: #fff;
  padding: 0 0.3125rem 0 0.3125rem;
  border-bottom: 1px solid #f6f7f8;
  .van-field {
    height: 70px;
    overflow-y: scroll;
  }
  span {
    color: #646566;
  }
  .message {
    background-color: #f4f5f9;
    margin-top: 0.3125rem;
  }
}
.radio {
  // padding: 0.3125rem;
  padding-left: 0.3125rem;
  line-height: 35px;
}
</style>
