<template>
  <div class="van-datetimesec-picker">
    <van-datetime-picker
      v-model="dateValue"
      type="datetime"
      :min-date="minDate"
      :max-date="maxDate"
      title="选择检查时间"
      v-bind="datetimePickerProps"
      @cancel="$emit('cancel')"
      @confirm="handleConfirm"
    />
    <van-picker
      :columns="Array(60).fill().map((_, i) => `0${i}`.slice(-2))"
      v-bind="pickerProps"
      :default-index="secondIdx"
      @change="handleChange"
    />
  </div>
</template>

<script>
export default {
  name: 'van-datetimesec-picker',
  props: {
    datetimePickerProps: Object,
    pickerProps: Object,
    value: Date
  },
  data() {
    return {
      secondIdx: 0,
      dateValue: new Date(),
      minDate: new Date(2020, 1, 1),
      maxDate: new Date(2025, 12, 31),
    }
  },
  watch: {
    value: {
      handler() {
        this.dateValue = new Date(this.value)
        this.secondIdx = this.dateValue.getSeconds()
      },
      immediate: true
    }
  },
  methods: {
    handleConfirm(val) {
      console.log(val)
      val.setSeconds(this.secondIdx)
      this.$emit('confirmDate', val.getTime())
    },
    handleChange() {
      this.secondIdx = arguments[2]
    }
  }
}
</script>

<style lang="scss" scoped>
.van-datetimesec-picker {
  display: flex;
  position: relative;
  .van-picker:first-of-type {
    flex: 5;
    /deep/ .van-picker__toolbar {
      width: 100vw;
      position: absolute;
    }
    /deep/ .van-picker__columns {
      margin-top: 44px;
    }
    /deep/ .van-picker__frame {
      width: 100%;
    }
  }
  .van-picker:last-of-type {
    flex: 1;
    margin-top: 44px;
  }
}
</style>