<template>
  <div class='content'>
    <Header title="专项检查选择检查表" @backFun="goBack"></Header>
    <div class="inner">
      <!-- 选择检查表 -->
      <div class="titleWrap" ref="titleWrap">
        <div class="required" style="height: 20px;">选择专项检查</div>
        <van-field
          v-model="tableInfo.inspectionName"
          readonly
          right-icon="arrow"
          placeholder="请选择专项检查表"
          @click="popupShow1 = true"/>
        <div class="examinationExplain">
          检查表说明：{{ tableInfo.inspectionDescription || '暂无' }}
        </div>
        <div class="attachment">
          <div class="attachmentTitle">
            <img src="../../assets/images/<EMAIL>" alt="">
            <span>附件资料：</span>
          </div>
          <div v-if="tableInfo.attachedFiles.length" class="attachmentTextWrap">
            <span
              v-for="(item, index) in JSON.parse(tableInfo.attachedFiles)"
              :key="index"
              class="attachmentText"
              @click="lookDown(item)"
              >
              {{ item.name }}
            </span>
          </div>
          <span v-else style="color: #86909C;">暂无</span>
        </div>
      </div>
      <div class="centerWrap" :style="{'height': `calc(100% - 64px - ${titleWrapHeight}px)`}">
        <!-- 检查人信息 -->
        <van-collapse v-model="checkActive">
          <van-collapse-item name="1">
            <div slot="title" class="collapseItemTitle">
              <div class="titleIcon"></div>
              <span class="titleText">检查人信息</span>
            </div>
            <van-field
              v-model="checkInfo.inspectorName"
              required
              label="检查人"
              disabled
              placeholder="请输入检查人"
            />
            <van-field
              v-model="checkInfo.inspectorRole"
              required
              label="岗位角色"
              placeholder="请输入岗位角色"
              maxlength="20"
            />
            <van-field
              v-model="checkInfo.inspectorPhone"
              required
              label="检查人电话"
              disabled
              placeholder="请输入检查人电话"
            />
          </van-collapse-item>
        </van-collapse>
        <!-- 同行人信息 -->
        <van-collapse
          v-for="(item, index) in peerInfo.info"
          :key="index"
          v-model="peerInfo.activ">
          <van-collapse-item :name="index">
            <div slot="title" class="collapseItemTitle">
              <div class="titleIcon"></div>
              <span class="titleText">同行人信息</span>
              <div v-if="index == 0" class="addPeer" @click.stop="addPeerInfo()">
                <van-icon color="#29BEBC" name="plus" />
                同行检查人
              </div>
              <div v-else class="addPeer deletePeer" @click.stop="deletePeerInfo(index)">
                <van-icon color="#F53F3F" name="minus" />
                同行检查人
              </div>
            </div>
            <van-field
              v-model="item.peerReviewerName"
              required
              label="检查人"
              placeholder="请输入检查人"
              maxlength="20"
            />
            <van-field
              v-model="item.peerReviewerJobRole"
              required
              label="岗位角色"
              placeholder="请输入岗位角色"
              maxlength="20"
            />
            <van-field
              v-model="item.peerReviewerPhone"
              required
              type="number"
              label="检查人电话"
              placeholder="请输入检查人电话"
              onkeyup="value=value.replace(/[^\d]/g,'')"
              maxlength="11"
            />
            <van-checkbox
              v-model="item.isInspectionResults"
              shape="square"
              checked-color="#29BEBC">
              同行检查人已知晓检查结果
            </van-checkbox>
          </van-collapse-item>
        </van-collapse>
        <!-- 单位基础信息 -->
        <van-collapse v-model="unitActive">
          <van-collapse-item name="1">
            <div slot="title" class="collapseItemTitle">
              <div class="titleIcon"></div>
              <span class="titleText">单位基本信息</span>
            </div>
            <van-field
              v-model="unitInfo.unitName"
              required
              label="单位名称"
              disabled
              placeholder="请输入单位名称"
            />
            <van-field
              v-model="unitInfo.area"
              required
              label="院区"
              placeholder="请选择院区"
              right-icon="arrow"
              readonly
              @click="popupShow2 = true"
            />
            <van-field
              v-model="unitInfo.address"
              label="地址"
              disabled
              placeholder="请输入地址"
            />
          </van-collapse-item>
        </van-collapse>
        <!-- 现场负责人信息 -->
        <van-collapse v-model="sceneActive">
          <van-collapse-item name="1">
            <div slot="title" class="collapseItemTitle">
              <div class="titleIcon"></div>
              <span class="titleText">现场负责人信息</span>
            </div>
            <van-field
              v-model="sceneInfo.responsiblePersonName"
              required
              label="负责人姓名"
              placeholder="请输入负责人姓名"
              maxlength="20"
            />
            <van-field
              v-model="sceneInfo.responsiblePersonPhone"
              required
              label="负责人电话"
              type="number"
              placeholder="请输入负责人电话"
              onkeyup="value=value.replace(/[^\d]/g,'')"
              maxlength="11"
            />
          </van-collapse-item>
        </van-collapse>
      </div>
      <div class="bottomWrap">
        <van-button color="rgb(41,190,188, 0.15)" @click="goBack">取消</van-button>
        <van-button color="#29BEBC" @click="goCheck">开始检查</van-button>
      </div>
    </div>
    <van-popup v-model="popupShow1" position="bottom" :style="{ height: '30%' }">
      <van-picker
        title="选择专项检查表"
        show-toolbar
        :columns="checkListData.map(i => i.inspectionName)"
        @confirm="onConfirm1"
        @cancel="onCancel1"
      />
    </van-popup>
    <van-popup v-model="popupShow2" position="bottom" :style="{ height: '30%' }">
      <van-picker
        title="选择院区"
        show-toolbar
        :columns="areaList.map(i => i.hospitalAreaName)"
        @confirm="onConfirm2"
        @cancel="onCancel2"
      />
    </van-popup>
  </div>
</template>
<script>
import axios from 'axios'
import { ImagePreview } from "vant";
export default {
  components: {},
  data() {
    return {
      loginInfo: {},
      queryData: {},
      checkListData: [], // 检查表列表
      tableInfo: {
        inspectionName: '',
        attachedFiles: []
      },
      popupShow1: false,
      popupShow2: false,
      titleWrapHeight: 138,
      checkActive: ['1'],
      unitActive: ['1'],
      sceneActive: ['1'],
      checkInfo: {
        inspectorId: '',
        inspectorName: '',
        inspectorRole: '检查员',
        inspectorPhone: ''
      },
      peerInfo: {
        activ: [0],
        info: [
          {
            peerReviewerName: "",
            peerReviewerJobRole: "",
            peerReviewerPhone: "",
            isInspectionResults: false,
          }
        ]
      },
      unitInfo: {
        unitName: '',
        area: '',
        address: ''
      },
      areaList: [],
      areaInfo: {},
      sceneInfo: {
        responsiblePersonName: '',
        responsiblePersonPhone: ''
      },
      tsDetail: {}
    }
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.init()
    if (sessionStorage.getItem('checkParams')) {
      const checkAbout = JSON.parse(sessionStorage.getItem('checkParams'))
      this.setCheckAbout(checkAbout)
      this.unitInfo.area = checkAbout.hospitalAreaName
    }
  },
  mounted() {
    this.sysClickBack()
  },
  methods: {
    init() {
      this.checkInfo.inspectorId = this.loginInfo.id
      this.checkInfo.inspectorName = this.loginInfo.name
      this.checkInfo.inspectorPhone = this.loginInfo.phone
      this.queryData = this.$route.query
      this.unitInfo.unitName = this.queryData.hospitalName
      this.unitInfo.address = this.queryData.address
      this.mathHeight()
      this.getCheckList()
      this.getAreaList()
    },
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    resetCheck() {
      sessionStorage.removeItem('contentChildren')
      sessionStorage.removeItem('attachment')
      sessionStorage.removeItem('checkParams')
      this.checkInfo = {
        inspectorId: this.loginInfo.id,
        inspectorName: this.loginInfo.name,
        inspectorRole: '检查员',
        inspectorPhone: this.loginInfo.phone
      }
      this.peerInfo = {
        activ: [0],
        info: [
          {
            peerReviewerName: "",
            peerReviewerJobRole: "",
            peerReviewerPhone: "",
            isInspectionResults: false,
          }
        ]
      }
      this.unitInfo = {
        unitName: this.queryData.unitName,
        area: '',
        address: this.queryData.address
      }
      this.sceneInfo = {
        responsiblePersonName: '',
        responsiblePersonPhone: ''
      }
    },
    goBack() {
      this.resetCheck()
      this.$router.go(-1)
    },
    // 计算titleWrap高度
    mathHeight() {
      this.$nextTick(() => {
        this.titleWrapHeight = this.$refs.titleWrap.clientHeight
      })
    },
    // 检查表
    getCheckList() {
      this.axios.postContralHostBase('checkList', {
        unitCode: this.queryData.unitCode,
        hospitalCode: this.queryData.hospitalCode
      }, res => {
        if (res.code == '200') {
          this.checkListData = res.data
          if (sessionStorage.getItem('checkParams')) {
            const checkAbout = JSON.parse(sessionStorage.getItem('checkParams'))
            this.tableInfo = this.checkListData.find(i => i.id == checkAbout.inspectionTableId)
          }
        }
      })
    },
    // 院区列表
    getAreaList() {
      this.axios.postContralHostBase('areaList', {
        unitCode: this.queryData.unitCode,
        unitName: this.queryData.unitName,
        hospitalCode: this.queryData.hospitalCode,
        hospitalName: this.queryData.hospitalName
      }, res => {
        if (res.code == '200') {
          this.areaList = res.data
          if (sessionStorage.getItem('checkParams')) {
            const checkAbout = JSON.parse(sessionStorage.getItem('checkParams'))
            this.areaInfo = this.areaList.find(i => i.hospitalAreaCode == checkAbout.hospitalAreaCode)
          }
        }
      })
    },
    onConfirm1(val, index) {
      this.tableInfo = this.checkListData[index]
      this.getTableTS(this.tableInfo.id)
      this.mathHeight()
      this.onCancel1()
    },
    onConfirm2(val, index) {
      this.unitInfo.area = val
      this.areaInfo = this.areaList[index]
      this.onCancel2()
    },
    onCancel1() {
      this.popupShow1 = false
    },
    onCancel2() {
      this.popupShow2 = false
    },
    // 获取检查表暂存内容
    getTableTS(id) {
      this.$toast.loading({
        message: '加载中...',
        forbidClick: false
      })
      axios.get(__PATH.BASEURL +'/inspectionLedgerController/detailsTree', {
        params: {
          id,
          hospitalCode: this.queryData.hospitalCode
        }
      }).then((res) => {
        const {code, data, message} = res.data
        const {inspectionLedger, categoryList } = data
        if (code == 200) {
          this.tsDetail = data
          if ((data && inspectionLedger && inspectionLedger.hospitalName) && (data && categoryList && categoryList.constructor) === Array) {
            console.log('有暂存')
            this.areaInfo = this.areaList.find(i => i.hospitalAreaCode == inspectionLedger.hospitalAreaCode)
            this.setCheckAbout(inspectionLedger)
            if (categoryList) {
							categoryList.forEach((item) => {
								this.contentFormat(item.children)
							})
              sessionStorage.setItem('contentChildren', JSON.stringify(categoryList))
						}
            if (inspectionLedger.attachedFiles.length) {
              sessionStorage.setItem('attachment', inspectionLedger.attachedFiles)
            }
          } else {
            this.resetCheck()
          }
        }
        this.refreshing = false
        this.$toast.clear()
      }).catch((err) => {
        this.refreshing = false
        this.$toast.clear()
        this.$toast.fail(err.message || '获取失败')
      })
    },
    // 格式化内容数据
		contentFormat(arr) {
			for (let index = 0; index < arr.length; index++) {
				if (arr[index].contentList && arr[index].contentList.length) {
					arr[index].contentList.forEach((item) => {
						item.fileEcho = [];
						if (item.attachedFiles) {
							let attachedFiles = JSON.parse(item.attachedFiles);
							item.attachedFiles = attachedFiles;
							item.fileEcho = attachedFiles.map((v) => {
								return {
									name: v.name,
									url: __PATH.DOWNLOAD_URL + v.url,
								};
							});
						} else {
							item.attachedFiles = [];
						}
					});
					return;
				}
				if (arr[index].children && arr[index].children.length) {
					const result = this.contentFormat(arr[index].children);
					if (result !== undefined) {
						return result;
					}
				}
			}
			return null;
		},
    // 对检查内容赋值
    setCheckAbout(obj) {
      // 检查人
      this.checkInfo = {
        inspectorId: obj.inspectorId,
        inspectorName: obj.inspectorName,
        inspectorRole: obj.inspectorRole,
        inspectorPhone: obj.inspectorPhone
      }
      // 同行人
      this.peerInfo.info = JSON.parse(obj.assistantInspectors)
      this.peerInfo.activ = this.peerInfo.info.map((i, index) => index)
      // 单位信息
      this.unitInfo = {
        unitName: this.queryData.unitName,
        area: obj.hospitalAreaName,
        address: this.queryData.address
      }
      // 现场负责人
      this.sceneInfo = {
        responsiblePersonName: obj.responsiblePersonName,
        responsiblePersonPhone: obj.responsiblePersonPhone
      }
    },
    // 新增同行人
    addPeerInfo() {
      if (this.peerInfo.info.length < 10) {
        const item = {
          peerReviewerName: "",
          peerReviewerJobRole: "",
          peerReviewerPhone: "",
          isInspectionResults: false,
        }
        this.peerInfo.info.push(item)
        this.peerInfo.activ.push(this.peerInfo.info.length - 1)
      } else {
        this.$toast('最多添加十位同行检查人')
      }
    },
    // 删除同行人
    deletePeerInfo(index) {
      this.peerInfo.info = this.peerInfo.info.filter((i, ind) => ind != index)
    },
    // 附件预览
    lookDown(item) {
      let file = {
        name: item.name,
        url: __PATH.DOWNLOAD_URL + item.url
      }
      var Type = file.name.split(".").pop();
      if (Type != "jpg" && Type != "jpeg" && Type != "png" && Type != "JPG" && Type != "JPEG" && Type != "gif") {
        var localPath = api.cacheDir + "/personalDocuments/" + file.name;
        api.download(
          {
            url: file.url,
            savePath: localPath,
            cache: true,
            allowResume: true
          },
          function(ret, err) {
            console.log(JSON.stringify(ret));
            if (ret.state == 1) {
              if (api.systemType == "ios") {
                var docReader = api.require("docReader");
                docReader.open(
                  {
                    path: ret.savePath,
                    autorotation: false
                  },
                  function(ret, err) {}
                );
              } else {
                // 安卓
                // 文档  图片
                var fileType = file.name.split(".").pop();
                if (fileType == "mp4" || fileType == "mp3" || fileType == "amr" || fileType == "MP4" || fileType == "MP3") {
                  // 安卓 视屏
                  api.openVideo({
                    url: ret.savePath
                  });
                } else {
                  var docReader = api.require("docReader");
                  docReader.open(
                    {
                      path: ret.savePath,
                      autorotation: false
                    },
                    function(ret, err) {}
                  );
                }
              }
            } else {
            }
          }
        );
      } else {
        ImagePreview({
          images: [file.url],
          showIndex: true,
          loop: false,
          swipeDuration: 50
        })
      }
    },
    goCheck() {
      if (!this.tableInfo.inspectionName) {
        return this.$toast('请选择专项检查表')
      }
      if (!this.checkInfo.inspectorRole) {
        return this.$toast('请填写岗位角色')
      }
      if (this.peerInfo.info.some(i => !i.peerReviewerName || !i.peerReviewerJobRole || !i.peerReviewerPhone)) {
        return this.$toast('请完整填写同行人信息')
      }
      if (!this.unitInfo.area) {
        return this.$toast('请选择院区')
      }
      if (!this.sceneInfo.responsiblePersonName || !this.sceneInfo.responsiblePersonPhone) {
        return this.$toast('请填写现场负责人信息')
      }
      const checkAbout = JSON.parse(sessionStorage.getItem('checkParams'))
      const checkParams = {
        hospitalCode: this.queryData.hospitalCode,
        hospitalName: this.queryData.hospitalName,
        unitCode: this.queryData.unitCode,
        unitName: this.queryData.unitName,
        hospitalAreaCode: this.areaInfo.hospitalAreaCode,
        hospitalAreaName: this.unitInfo.area,
        address: this.queryData.address,
        inspectionTableId: this.tableInfo.id,
        inspectionTableName: this.tableInfo.inspectionName,
        inspectionName: this.tableInfo.inspectionName,
        ...this.checkInfo,
        assistantInspectors: JSON.stringify(this.peerInfo.info),
        ...this.sceneInfo,
        inspectionDateStr: this.tsDetail.inspectionLedger ? this.tsDetail.inspectionLedger.inspectionDate : checkAbout.inspectionDateStr || ''
      }
      sessionStorage.setItem('checkParams', JSON.stringify(checkParams))
      const queryData = this.queryData
      queryData.id = this.tableInfo.id
      this.$router.push({
        path: '/checkTableDetail',
        query: queryData
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .content {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    font-size: 16px;
    .inner {
      position: relative;
      height: calc(100vh - 60px);
      background-color: #F2F4F9;
      .titleWrap {
        padding: 16px;
        background-color: #fff;
        font-size: 14px;
        .van-cell {
          font-size: 16px;
          padding: 8px 16px;
        }
        .van-cell::after {
          border: none;
        }
        .examinationExplain {
          color: #86909C;
          max-height: 38px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .attachment {
          display: flex;
          align-items: center;
          margin-top: 8px;
          height: 19px;
          .attachmentTitle {
            width: 85px;
            display: flex;
            align-items: center;
            img {
              height: 14px;
            }
          }
          .attachmentTextWrap {
            width: calc(100% - 85px);
            overflow: auto;
            white-space: nowrap;
            .attachmentText {
              color: #29BEBC;
              display: inline-block;
              padding-right: 4px;
            }
          }
        }
      }
      .centerWrap {
        padding: 0 10px;
        overflow: auto;
        /deep/ .van-collapse {
          margin-top: 10px;
          background-color: #fff;
          border-radius: 8px;
          .van-collapse-item__title {
            border-radius: 8px;
            .collapseItemTitle {
              display: flex;
              align-items: center;
              position: relative;
              .addPeer {
                position: absolute;
                right: 10px;
                color: #29BEBC;
                font-size: 14px;
              }
              .deletePeer {
                color: #F53F3F;
              }
              .titleIcon {
                width: 4px;
                height: 16px;
                background-color: #29BEBC;
                margin-right: 8px;
              }
              .titleText {
                color: #1D2129;
                font-weight: bold;
                font-size: 16px;
              }
            }
          }
          .van-cell::after {
            border: none;
          }
          .van-collapse-item__content {
            padding: 0 16px 10px;
            border-radius: 8px;
            .van-cell {
              border-bottom: 1px solid #E5E6EB;
              .van-cell__title {
                color: #1D2129;
              }
            }
            .van-cell:last-child {
              border: none;
            }
            .van-checkbox {
              padding-left: 14px;
              margin: 8px 0;
              .van-checkbox__icon {
                font-size: 16px;
              }
            }
          }
        }
      }
      .bottomWrap {
        padding: 10px 16px;
        position: absolute;
        bottom: 0;
        left: 0;
        width: calc(100% - 32px);
        background-color: #fff;
        display: flex;
        align-content: center;
        justify-content: space-between;
        button {
          height: 44px;
          width: 50%;
        }
        button:first-child {
          color: #29BEBC !important;
          margin-right: 12px;
        }
      }
    }
    .van-popup {
      overflow: hidden;
    }
    .required::before {
      content: "*";
      color: #f56c6c;
      margin-right: 2px;
      vertical-align: middle;
    }
  }
</style>