<template>
  <div class='content'>
    <Header :title="isHospaital ? '专项检查结果' : listType == '0' ? '查看暂存记录' : '查看检查历史'" @backFun="goBack"></Header>
    <div class="inner">
      <div class="topSearch">
        <div class="inutWrap">
          <van-field
            :key="fieldKey[0]"
            v-model="dateInterval[0]"
            :placeholder="`请选择${isHospaital ? '检查' : listType == '0' ? '暂存' : '填表'}时间`"
            readonly
            :right-icon="dateInterval[0] ?'close' : ''"
            @click-right-icon.stop="clearDate('0')"
            @click="dateShow = true"/>
          <div class="line"></div>
          <van-field
            :key="fieldKey[1]"
            v-model="dateInterval[1]"
            :placeholder="`请选择${isHospaital ? '检查' : listType == '0' ? '暂存' : '填表'}时间`"
            readonly
            :right-icon="dateInterval[1] ?'close' : ''"
            @click-right-icon.stop="clearDate('1')"
            @click="dateShow = true"/>
        </div>
        <span @click="dialogShow = true">
          筛选
          <van-icon name="bars" />
        </span>
      </div>
      <div class="contentInner">
        <van-pull-refresh
          v-model="refreshing"
          pulling-text="下拉刷新..."
          @refresh="onRefresh"
        >
          <template v-if="listData.length">
            <div
              class="listWrap"
              v-for="(item, index) in listData"
              :key="index"
              @click="toDetail(item)">
              <div class="titleItem">
                <span>{{ item.hospitalName }}</span>
                <span  v-if="isHospaital" style="color: #29BEBC">{{ item.confirmationStatus == '1' ? '未确认' : '已确认' }}</span>
                <van-icon v-else color="#C9CDD4" name="arrow" />
              </div>
              <div class="contentItem">
                <span>专项检查表</span>
                <div>{{ item.inspectionTableName || item.inspectionName }}</div>
              </div>
              <div class="contentItem">
                <span>检查人</span>
                <div>{{ item.inspectorName }}</div>
              </div>
              <div class="contentItem">
                <span>检查时间</span>
                <div>{{ item.inspectionDate ? moment(item.inspectionDate).format('YYYY-MM-DD HH:mm:ss') : '' }}</div>
              </div>
              <div v-if="listType == '0'" class="contentItem">
                <span>暂存时间</span>
                <div>{{ item.fillDate ? moment(item.fillDate).format('YYYY-MM-DD HH:mm:ss') : '' }}</div>
              </div>
              <div v-else class="contentItem">
                <span>填表时间</span>
                <div>{{ item.fillDate ? moment(item.fillDate).format('YYYY-MM-DD HH:mm:ss') : '' }}</div>
              </div>
              <div class="bottomItem">
                <div class="btnWrap green">
                  <van-icon name="success" />
                  <span>是：{{ item.resultYes }}</span>
                </div>
                <div class="btnWrap red">
                  <van-icon name="fail" />
                  <span>否：{{ item.resultNo }}</span>
                </div>
              </div>
            </div>
          </template>
          <van-empty v-else description="暂无更多" />
        </van-pull-refresh>
      </div>
      <van-dialog
        v-model="dialogShow"
        confirm-button-color="#00cac8"
        title="筛选"
        show-cancel-button
        cancel-button-text='重置'
        @confirm="getListData"
        @cancel="cancelSearch">
        <van-field v-model="inspectionTableName" label="检查表名称" placeholder="请输入检查表名称" />
        <van-field v-model="inspectorName" label="检查人" placeholder="请输入检查人" />
      </van-dialog>
    </div>
    <van-calendar
      v-model="dateShow"
      type="range"
      :show-confirm="false"
      :min-date="new Date(moment().subtract(3, 'y'))"
      color="#29BEBC"
      @confirm="confirmDate" />
  </div>
</template>
<script>
import moment from 'moment'
export default {
  components: {},
  data() {
    return {
      moment,
      fieldKey: [Math.random(), Math.random()],
      endKey: Math.random(),
      dateInterval: ['', ''],
      showPopover: false,
      actions: [
        { text: '全部', id: '1,2' },
        { text: '未确认', id: '1' },
        { text: '已确认', id: '2' }
      ],
      listType: '1,2',
      dateShow: false,
      currentDate: '',
      queryData: {},
      listData: [],
      refreshing: false,
      loginInfo: {},
      isHospaital: false,
      dialogShow: false,
      inspectionTableName: '',
      inspectorName: ''
    }
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.queryData = { ...this.$route.query }
    this.listType = this.queryData.listType // 0：暂存  1、2：历史
    if (this.loginInfo.hospitalCode) {
      this.isHospaital = true
    }
    this.getListData()
  },
  mounted() {
    this.sysClickBack()
  },
  methods: {
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    goBack() {
      if (this.isHospaital) {
        api.closeFrame({});
      } else {
        this.$router.go(-1)
      }
    },
    onSelect(act, index) {
      this.listType = act.id
      this.getListData()
    },
    confirmDate(date) {
      this.dateInterval = date.map(i => moment(i).format('YYYY-MM-DD'))
      this.dateShow = false
      this.getListData()
    },
    clearDate(index) {
      this.dateInterval[index] = ''
      this.startKey = Math.random()
      this.getListData()
    },
    getListData() {
      this.listData = []
      let params = {
        inspectionTableName: this.inspectionTableName,
        inspectorName: this.inspectorName
      }
      let url = ''
      if (this.isHospaital) {
        url = 'hospitalCheckList'
        params.unitCode = this.loginInfo.unitCode
        params.hospitalCode = this.loginInfo.hospitalCode
        params.startTime = this.dateInterval[0]
        params.endTime = this.dateInterval[1]
        params.delFlag = '0'
        params.pageNo = '1'
        params.pageSize = '999'
      } else {
        url = 'checkHistoryList'
        params.unitCode = this.queryData.unitCode
        params.hospitalCode = this.queryData.hospitalCode
        params.startDate = this.dateInterval[0]
        params.endDate = this.dateInterval[1]
        params.confirmationStatus = this.listType
      }
      this.$toast.loading({
        message: '加载中...',
        forbidClick: false
      })
      this.axios.postContralHostBase(url, params, res => {
        if (res.code == '200') {
          if (this.isHospaital) {
            this.listData = res.data.list
          } else {
            this.listData = res.data
          }
          this.$toast.clear()
        } else {
          this.$toast.fail(res.message || '获取失败')
        }
        this.refreshing = false
      })
    },
    cancelSearch() {
      this.inspectionTableName = ''
      this.inspectorName = ''
      this.dialogShow = false
      this.getListData()
    },
    onRefresh() {
      this.getListData()
    },
    toDetail(item) {
      const queryData = {
        unitCode: item.unitCode,
        unitName: item.unitName,
        hospitalCode: item.hospitalCode,
        hospitalName: item.hospitalName,
        address: item.address,
        id: item.id,
        type: 'detail'
      }
      // 暂存
      if (this.listType == '0') {
        queryData.type = 'TS'
        queryData.id = item.inspectionTableId
      }
      // 医院端
      if (this.isHospaital) {
        queryData.isHospaital = true
        queryData.subType = item.confirmationStatus
        queryData.countDown = item.countDown
      } 
      this.$router.push({
        path: '/checkTableDetail',
        query: queryData
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .content {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    font-size: 16px;
    .inner {
      height: calc(100vh - 60px);
      width: 100vw;
      background-color: #F2F4F9;
      overflow: auto;
      .topSearch {
        background-color: #fff;
        width: calc(100% - 32px);
        padding: 8px 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .inutWrap {
          width: calc(100% - 58px);
          display: flex;
          align-items: center;
          justify-content: space-between;
          .line {
            height: 1px;
            width: 10px;
            margin: 0 8px;
            background-color: #C9CDD4;
          }
          /deep/ .van-cell {
            padding: 8px 10px;
            width: 45%;
            background-color: #F2F3F5;
          }
          .van-cell::after {
            border: none;
          }
        }
        .van-popover__wrapper {
          width: 58px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          color: #1D2129;
        }
      }
      .contentInner {
        overflow: auto;
        padding: 0 10px 10px;
        height: calc(100% - 76px);
        width: calc(100% - 20px);
        .listWrap {
          margin-top: 10px;
          padding: 18px 16px 16px;
          border-radius: 8px;
          background-color: #fff;
          .titleItem {
            display: flex;
            justify-content: space-between;
            align-items: center;
            span:first {
              color: #1D2129;
            }
          }
          .contentItem {
            display: flex;
            margin-top: 10px;
            span {
              width: 90px;
              color: #4E5969;
            }
            div {
              width: calc(100% - 90px);
              color: #1D2129;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
          .bottomItem {
            margin-top: 12px;
            padding-top: 10px;
            border-top: 1px solid #E5E6EB;
            display: flex;
            align-items: center;
            .btnWrap {
              padding: 4px 6px;
              display: flex;
              align-items: center;
              justify-items: center;
              /deep/ .van-icon {
                border-radius: 50%;
                color: #fff;
                font-size: 10px;
                display: inline-block;
                padding: 3px;
                margin-right: 4px;
              }
            }
            .green {
              margin-right: 10px;
              background-color: #E8FFEA;
              color: #00B42A;
              .van-icon {
                background-color: #00B42A;
              }
            }
            .red {
              background-color: #FFECE8;
              color: #F53F3F;
              .van-icon {
                background-color: #F53F3F;
              }
            }
          }
        }
      }
      /deep/ .van-empty {
        margin-top: 10px;
        background-color: #fff !important;
        height: calc(100vh - 146px);
      }
    }
  }

</style>