<template>
  <div class='content'>
    <Header title="专项检查" @backFun="goBack"></Header>
    <div class="inner">
      <van-pull-refresh
        v-model="refreshing"
        pulling-text="下拉刷新..."
        @refresh="onRefresh"
      >
        <template v-if="listData.length">
          <div
            class="listWrap"
            v-for="(item, index) in listData"
            :key="index">
            <div class="titleItem">
              <span style="font-size: 17px;">医院信息</span>
            </div>
            <div class="contentItem">
              <span>医院名称</span>
              <div>{{ item.hospitalName }}</div>
            </div>
            <div class="contentItem">
              <span>检查时间</span>
              <div>{{ item.inspectionDate ? moment.unix(item.inspectionDate / 1000).format('YYYY-MM-DD HH:mm:ss') : '' }}</div>
            </div>
            <div class="contentItem">
              <span>填表时间</span>
              <div>{{ item.writeDate ? moment.unix(item.writeDate / 1000).format('YYYY-MM-DD HH:mm:ss') : '' }}</div>
            </div>
            <div class="bottomItem">
              <van-button type="default" @click="inspectionHistory(item)">查看检查历史</van-button>
              <van-button color="#29BEBC" @click="toExamination(item)">进入检查</van-button>
              <van-button color="#29BEBC" @click="temporaryStorage(item, item.tempKeepCount)">
                暂存（{{ item.tempKeepCount || 0 }}）
              </van-button>
            </div>
          </div>
        </template>
        <van-empty v-else description="暂无更多" />
      </van-pull-refresh>
    </div>
  </div>
</template>
<script>
import axios from 'axios'
import moment from 'moment'
export default {
  components: {},
  data() {
    return {
      moment,
      refreshing: false,
      listData: [],
      loginInfo: {}
    }
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.getDataList()
  },
  mounted() {
    this.sysClickBack()
  },
  methods: {
    // 点击按钮返回
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    goBack() {
      api.closeFrame({});
    },
    onRefresh() {
      this.listData = []
      this.getDataList()
    },
    getDataList() {
      this.$toast.loading({
        message: '加载中...',
        forbidClick: false
      })
      axios.get(__PATH.BASEURL +'/inspectionLedgerController/unitInspectionCountList', {
        params: {
          unitCode: this.loginInfo.unitCode
        }
      }).then((res) => {
        if (res.data.code == 200) {
          this.listData = res.data.data
        }
        this.refreshing = false
        this.$toast.clear()
      }).catch((err) => {
        this.refreshing = false
        this.$toast.clear()
        this.$toast.fail(err.message || '获取失败')
      })
    },
    toExamination(item) {
      this.$router.push({
        path: '/examinationTable',
        query: {
          unitCode: item.unitCode,
          unitName: item.unitName,
          hospitalCode: item.hospitalCode,
          hospitalName: item.hospitalName,
          address: item.address
        }
      })
    },
    inspectionHistory(item) {
      this.$router.push({
        path: '/checkHistory',
        query: {
          unitCode: item.unitCode,
          unitName: item.unitName,
          hospitalCode: item.hospitalCode,
          hospitalName: item.hospitalName,
          address: item.address,
          listType: '1,2'
        }
      })
    },
    temporaryStorage(item, count) {
      if (!count) return this.$toast('暂无更多')
      this.$router.push({
        path: '/checkHistory',
        query: {
          unitCode: item.unitCode,
          unitName: item.unitName,
          hospitalCode: item.hospitalCode,
          hospitalName: item.hospitalName,
          address: item.address,
          listType: '0'
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .content {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    font-size: 16px;
    .inner {
      height: calc(100vh - 60px);
      width: calc(100vw - 20px);
      padding: 0 10px;
      background-color: #F2F4F9;
      overflow: auto;
      .listWrap {
        margin-top: 10px;
        padding: 18px 16px 16px;
        border-radius: 8px;
        background-color: #fff;
        .titleItem {
          display: flex;
          justify-content: space-between;
          span:first {
            color: #1D2129;
          }
        }
        .contentItem {
          display: flex;
          margin-top: 10px;
          span {
            width: 80px;
            color: #4E5969;
          }
          div {
            width: calc(100% - 80px);
            color: #1D2129;
          }
        }
        .bottomItem {
          margin-top: 12px;
          padding-top: 10px;
          border-top: 1px solid #E5E6EB;
          display: flex;
          justify-content: space-between;
          button {
            height: 30px;
            width: 35%;
            padding: 0 5px;
          }
          button:nth-child(2) {
            margin: 0 8px;
          }
        }
      }
      /deep/ .van-empty {
        background-color: #fff !important;
        height: calc(100vh - 70px);
      }
    }
  }
</style>