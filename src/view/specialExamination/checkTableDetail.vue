<template>
  <div class='content'>
    <Header title="专项检查表" @backFun="goBack"></Header>
    <div class="inner">
      <div class="detailListWrap">
        <el-tree
          v-for="(treeItem, treeIndex) in treeDetailList"
          :key="treeIndex"
          :ref="'tree' + treeIndex"
          :data="[treeItem]"
          :props="defaultProps"
          :indent="type == 'detail' ? 0 : 8"
          node-key="id"
          default-expand-all
          icon-class="el-icon-arrow-down"
          class="treeWrap">
          <div
            class="custom-tree-node"
            :class="node.data.contentList.length ? 'contentItem' : node.level == 1 ? 'titleTetx' : ''"
            slot-scope="{ node, data }">
            <div v-if="node.label.indexOf('、') != -1" class="titleWrap">
              <span v-if="node.level == 1" class="titleIcon"></span>
              <div style="overflow: auto;">{{ node.label }}</div>
            </div>
            <template v-if="node.data.contentList.length">
              <div
                v-for="(item, index) in node.data.contentList"
                :key="index"
                class="tabelContent">
                <div class="contentTitle">
                  <div class="contentText">{{ node.data.label + '.' + (index + 1) + '、' + item.content }}</div>
                  <span class="gist" @click="showGist(item.basisInspection)">
                    <img src="../../assets/images/genera.png" alt="" height="14px">
                    <span>查看依据</span>
                  </span>
                </div>
                <div class="fillInfo">
                  <div class="groupWrap">
                    <van-radio-group v-model="item.radio" direction="horizontal">
                      <van-radio
                        checked-color="#29BEBC"
                        v-for="(j, jindex) in JSON.parse(item.optionText)"
                        :key="jindex"
                        :name="j.option_name + '_' + jindex"
                        :disabled="type == 'detail'"
                        @click="mathCount">
                        {{ j.option_name }}
                      </van-radio>
                    </van-radio-group>
                    <van-button
                      v-if="type != 'detail'"
                      color="#29BEBC"
                      type="primary"
                      size="small"
                      @click="subHiddenDanger(treeItem, item)">
                      提交隐患
                    </van-button>
                  </div>
                  <div v-if="type == 'detail'" style="padding: 8px 0;font-size: 16px;">
                    <span style="color: #1D2129;">检查内容详情</span>
                    <span v-if="!item.contentDescription"> 暂无</span>
                  </div>
                  <van-field
                    v-if="(type != 'detail') || (type == 'detail' && item.contentDescription)"
                    v-model="item.contentDescription"
                    rows="2"
                    :autosize="type == 'detail' ? autosizeObj : false"
                    type="textarea"
                    maxlength="200"
                    :readonly="type == 'detail'"
                    :placeholder="type != 'detail' ? '请输入,字数限制200字以内' : ''"
                    :show-word-limit="type != 'detail'"
                    :class="type == 'detail' ? 'disArea' : ''"
                  />
                  <div class="attachmentTitle">
                    <span>
                      <span style="color: #1D2129;">附件资料</span>
                      <span v-if="type == 'detail' && !item.attachedFiles"> 暂无</span>
                    </span>
                    <span v-if="type != 'detail'">已上传{{ item.fileEcho ? item.fileEcho.length : 0 }}张</span>
                  </div>
                  <div
                    v-if="(type != 'detail') || (type == 'detail' && item.attachedFiles)"
                    style="min-height: 88px;overflow: auto;">
                    <van-uploader
                      v-if="type != 'detail'"
                      ref="uplodImg"
                      accept=".jpg, .jpeg, .png, .JPG, .JPEG, .pdf, .PDF, .doc, .docx, .DOCX, .xlsx, .xls"
                      v-model="item.fileEcho"
                      :after-read="(files) => afterReadItem(files, item)"
                      @delete="(files, obj) => deleteImg(files, obj, item)"
                      @click-preview="previewFile"
                      :max-count="10" />
                    <van-uploader
                      v-if="type == 'detail' && item.attachedFiles"
                      ref="uplodImg"
                      accept=".jpg, .jpeg, .png, .JPG, .JPEG, .pdf, .PDF, .doc, .docx, .DOCX, .xlsx, .xls"
                      :deletable="false"
                      v-model="item.attachedFiles"
                      :after-read="(files) => afterReadItem(files, item)"
                      @click-preview="previewFile"
                      :max-count="item.attachedFiles.length" />
                  </div>
                </div>
              </div>
            </template>
          </div>
        </el-tree>
      </div>
      <div v-if="type != 'detail'" class="checkAbout">
        <van-field
          v-model="checkAbout.date"
          required
          label="选择检查时间"
          placeholder="请选择检查时间"
          right-icon="arrow"
          readonly
          @click="dateShow = true"
        />
        <div class="attachmentTitle">
          <span>附件</span>
          <span>已上传{{ type != 'detail' ? filesList.length : checkAbout.attachment.length }}张</span>
        </div>
        <div style="height: 88px;overflow: auto;">
          <van-uploader
            ref="uplodImg"
            accept=".jpg, .jpeg, .png, .JPG, .JPEG, .pdf, .PDF, .doc, .docx, .DOCX, .xlsx, .xls"
            v-model="filesList"
            multiple
            :after-read="afterReadItem"
            @click-preview="previewFile"
            @delete="(files, obj) => deleteImg(files, obj)"
            :max-count="10" />
        </div>
      </div>
      <div v-else class="checkStaff">
        <div class="itemWrap">
          <span>检查人</span>
          <span>{{ inspectionLedger.inspectorName }}</span>
        </div>
        <div class="itemWrap">
          <span>检查时间</span>
          <span>{{ inspectionLedger.inspectionDate }}</span>
        </div>
        <div class="itemWrap">
          <span>填表时间</span>
          <span>{{ inspectionLedger.fillDateStr }}</span>
        </div>
        <div class="itemWrap">
          <span>附件</span>
          <span v-if="inspectionLedger.attachedFiles">
            <span
              v-for="(item, index) in inspectionLedger.attachedFiles"
              :key="index"
              class="fileItem"
              @click="lookDown(item)">
              {{ item.name + ' ' }}
            </span>
          </span>
          <span v-else>暂无</span>
        </div>
      </div>
      <div class="bottomBra">
        <div class="bottomText">
          <span>是：{{ mathCountObj.yesCount }}项</span>
          <span>否：{{ mathCountObj.noCount }}项</span>
        </div>
        <div v-if="type != 'detail' && !queryData.isHospaital" class="bottomBtn">
          <van-button type="primary" @click="subCheckContent('0')">暂存</van-button>
          <van-button type="primary" @click="subCheckContent('1')">提交</van-button>
        </div>
        <div v-if="type == 'detail' && queryData.isHospaital && queryData.subType == '1'" class="confirmBtn">
          <van-button color="#29BEBC" @click="checkConfirm">确认</van-button>
          <span>自动确认倒计时{{ queryData.countDown }}小时</span>
        </div>
      </div>
    </div>
    <van-popup
      v-model="gistShow"
      round
      style="height: 30%; padding: 16px; width: 50%;overflow: hidden;">
      <div style="text-align: center;padding-bottom: 10px;">检查依据</div>
      <div style="height: calc(100% - 36px);" class="popupClass">{{ gistContent }}</div>
    </van-popup>
    <van-popup
      v-model="dateShow"
      position="bottom"
      style="height: 30%">
      <van-datetimesec-picker
        v-model="checkDete"
        :datetimePickerProps="{ 'visible-item-count': 3, title: '选择完整时间' }"
        :pickerProps="{ 'visible-item-count': 3 }"
        @confirmDate="confirmDate"
        @cancel="cancel"
      />
    </van-popup>
  </div>
</template>
<script>
import axios from 'axios'
import moment from 'moment'
import ImageCompressor from 'image-compressor.js'
import { ImagePreview } from "vant";
import VanDatetimesecPicker  from './dateComponents.vue'
export default {
  components: {
    VanDatetimesecPicker
  },
  data() {
    return {
      baseUrl: __PATH.DOWNLOAD_URL,
      checkDete: new Date(),
      gistShow: false,
      queryData: {},
      gistContent: '',
      treeDetailList: [],
      checkAbout: {
        date: '',
        attachment: []
      },
      dateShow: false,
      filesList: [],
      defaultProps: {
        label: (data) => {
          if (data.name) {
            return data.label + '、' + data.name
          } else {
            return data.label
          }
        },
        children: 'children'
      },
      mathCountObj: {
        yesCount: 0,
        noCount: 0
      },
      type: '',
      inspectionLedger: {},
      autosizeObj: {
        maxHeight: 75,
        minHeight: 25
      },
      flatArray: [],
      loginInfo: {}
    }
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.queryData = this.$route.query
    this.type = this.queryData.type || ''
    // 暂存列表
    if (this.type == 'TS') {
      this.getTableTS(this.queryData.id)
    } else {
      // 有暂存
      if (sessionStorage.getItem('contentChildren')) {
        const checkParams = JSON.parse(sessionStorage.getItem('checkParams'))
        this.checkAbout.date = moment(checkParams.inspectionDateStr || new Date() ).format('YYYY-MM-DD HH:mm:ss')
        this.checkDete = checkParams.inspectionDateStr ? new Date(checkParams.inspectionDateStr) : new Date()
        const categoryList = JSON.parse(sessionStorage.getItem('contentChildren'))
        this.formatJson(categoryList)
        this.treeDetailList = this.formatChildren(categoryList)
        this.$nextTick(() => {
          const itemEle = document.getElementsByClassName('contentItem')
          for (let i = 0; i < itemEle.length; i++) {
            itemEle[i].parentNode.style.height = 'auto'
          }
        })
        // 有附件
        if (sessionStorage.getItem('attachment')) {
          const attachmentList = JSON.parse(sessionStorage.getItem('attachment'))
          this.checkAbout.attachment = attachmentList
          this.filesList = attachmentList.map(i => {
            const item = {
              name: i.name,
              url: __PATH.DOWNLOAD_URL + i.url,
            }
            return item
          })
        }
        this.mathCount()
      } else {
        this.checkAbout.date = moment(new Date() ).format('YYYY-MM-DD HH:mm:ss')
        this.checkDete = new Date()
        this.getTableDetail()
      }
    }
  },
  mounted() {
    this.sysClickBack()
  },
  methods: {
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    getTableDetail() {
      const params = {
        id: this.$route.query.id,
        hospitalCode: this.queryData.hospitalCode
      }
      this.$toast.loading({
        message: '加载中...',
        forbidClick: false
      })
      if (this.type == 'detail') {
        this.axios.postContralHostBase('checkHistoryDetail', params, res => {
          if (res.code == '200') {
            this.formatJson(res.data.categoryList)
            this.treeDetailList = this.formatChildren(res.data.categoryList)
            this.inspectionLedger = res.data.inspectionLedger
            this.inspectionLedger.attachedFiles = this.inspectionLedger.attachedFiles ? JSON.parse(this.inspectionLedger.attachedFiles) : ''
            this.mathCount()
            this.$nextTick(() => {
              const itemEle = document.getElementsByClassName('contentItem')
              for (let i = 0; i < itemEle.length; i++) {
                itemEle[i].parentNode.style.height = 'auto'
              }
            })
          } else {
            this.$toast.fail(res.message || '获取失败')
          }
          this.$toast.clear()
          this.refreshing = false
        })
      } else {
        axios.get(__PATH.BASEURL +'/inspection/table/detailsTree', { params }).then((res) => {
          if (res.data.code == 200) {
            this.treeDetailList = this.formatChildren(res.data.data.categoryTree)
            this.mathCount()
            this.$nextTick(() => {
              const itemEle = document.getElementsByClassName('contentItem')
              for (let i = 0; i < itemEle.length; i++) {
                itemEle[i].parentNode.style.height = 'auto'
              }
            })
            this.$toast.clear()
          }
        }).catch((err) => {
          this.$toast.clear()
          this.$toast.fail(err.message || '获取失败')
        })
      }
    },
    // 暂存
    getTableTS(id) {
      this.$toast.loading({
        message: '加载中...',
        forbidClick: false
      })
      axios.get(__PATH.BASEURL +'/inspectionLedgerController/detailsTree', {
        params: {
          id,
          hospitalCode: this.queryData.hospitalCode
        }
      }).then((res) => {
        const {code, data, message} = res.data
        const {inspectionLedger, categoryList } = data
        if (code == 200) {
          this.inspectionLedger = inspectionLedger
          this.checkAbout.date = moment(inspectionLedger.inspectionDateStr ).format('YYYY-MM-DD HH:mm:ss')
          if (categoryList) {
            this.formatJson(categoryList)
            this.treeDetailList = this.formatChildren(categoryList)
            this.mathCount()
            this.$nextTick(() => {
              const itemEle = document.getElementsByClassName('contentItem')
              for (let i = 0; i < itemEle.length; i++) {
                itemEle[i].parentNode.style.height = 'auto'
              }
            })
          }
          if (inspectionLedger.attachedFiles.length) {
            const attachmentList = JSON.parse(inspectionLedger.attachedFiles)
            this.checkAbout.attachment = attachmentList
            this.filesList = attachmentList.map(i => {
              const item = {
                name: i.name,
                url: __PATH.DOWNLOAD_URL + i.url,
              }
              return item
            })
          }
        }
        this.$toast.clear()
      }).catch((err) => {
        this.$toast.clear()
        this.$toast.fail(err.message || '获取失败')
      })
    },
    // 处理第一级children为空的情况（页面树结构第一级children不能为空）
    formatChildren(arr) {
      arr.forEach(i => {
        if (!i.children.length) {
          const childrenItem = {
            name: '',
            categoryName: i.name,
            id: i.id,
            label: i.label,
            pId: i.pId,
            contentList: i.contentList
          }
          i.contentList = []
          i.isFormat = true
          i.children.push(childrenItem)
        }
      })
      return arr
    },
    cancel() {
      dateShow = false
    },
    confirmDate(date) {
      this.dateShow = false
      this.checkAbout.date = moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    // 图片压缩
    compressImage(file) {
      return new Promise((resolve, reject) => {
        new ImageCompressor(file.file, {
          quality: 0.6,
          checkOrientation: false,
          success(res) {
            let file = new window.File([res], res.name, { type: res.type })
            resolve(file)
          },
          error(e) {
            reject();
          }
        })
      })
    },
    // 图片上传
    async afterReadItem(files, obj) {
      console.log(files)
      var Type = files.file.name.split(".").pop();
      const params = {
        hospitalCode: this.queryData.hospitalCode
      }
      if (Type != "jpg" && Type != "jpeg" && Type != "png" && Type != "JPG" && Type != "JPEG" && Type != "gif") {
        params.file = files.file
      } else {
        params.file = await this.compressImage(files)
      }
      files.status = 'uploading'
      this.axios.postContralHostBase('uploadImg',params, res => {
        if (res.code = '200') {
          const item = {
            name: res.data.fileName,
            url: res.data.fileKey
          }
          files.status = 'done'
          if (obj.fileEcho) {
            if (obj.attachedFiles) {
              obj.attachedFiles.push(item)
            } else {
              obj.attachedFiles = []
              obj.attachedFiles.push(item)
            }
          } else {
            this.checkAbout.attachment.push(item)
          }
        }
      })
    },
    //删除图片
    deleteImg(file, obj, item) {
      if (item) {
        item.attachedFiles = item.attachedFiles.filter((i, index) => index != obj.index)
      } else {
        this.checkAbout.attachment = this.checkAbout.attachment.filter((i, index) => index != obj.index)
      }
    },
    // 附件预览
    previewFile(file) {
      var Type = file.name.split(".").pop();
      if (Type != "jpg" && Type != "jpeg" && Type != "png" && Type != "JPG" && Type != "JPEG" && Type != "gif") {
        var localPath = api.cacheDir + "/personalDocuments/" + file.name;
        api.download(
          {
            url: file.url,
            savePath: localPath,
            cache: true,
            allowResume: true
          },
          function(ret, err) {
            if (ret.state == 1) {
              if (api.systemType == "ios") {
                var docReader = api.require("docReader");
                docReader.open(
                  {
                    path: ret.savePath,
                    autorotation: false
                  },
                  function(ret, err) {}
                );
              } else {
                // 安卓
                // 文档  图片
                var fileType = file.name.split(".").pop();
                if (fileType == "mp4" || fileType == "mp3" || fileType == "amr" || fileType == "MP4" || fileType == "MP3") {
                  // 安卓 视屏
                  api.openVideo({
                    url: ret.savePath
                  });
                } else {
                  var docReader = api.require("docReader");
                  docReader.open(
                    {
                      path: ret.savePath,
                      autorotation: false
                    },
                    function(ret, err) {}
                  );
                }
              }
            }
          }
        );
      }
    },
    showGist(val) {
      this.gistShow = true
      this.gistContent = val
    },
    // 递归函数，用于将树形结构转换为二维数组(每个树分支为一个数组)
		convertTreeToArray(node, path = []) {
			const newPath = [...path, node];
			if (node.children && node.children.length > 0) {
				node.children.forEach((child) => {
					this.convertTreeToArray(child, newPath);
				});
			} else {
				this.flatArray.push(newPath);
			}
		},
    processContentList(contentList) {
      return contentList.map((j, index) => {
        j.contentTableId = j.contentTableId || j.id
        delete j.fileEcho
        delete j.id
        if (j.attachedFiles && Array.isArray(j.attachedFiles) && j.attachedFiles.length) {
          // 全路径处理为半路径
          if (j.attachedFiles.some(k => k.url.includes(__PATH.DOWNLOAD_URL))) {
            j.attachedFiles.forEach(k => {
              const regex = new RegExp(__PATH.DOWNLOAD_URL, "g");
              k.url = k.url.replace(regex, '')
            })
          }
          j.attachedFiles = JSON.stringify(j.attachedFiles);
        }
        j.attachedFiles = Array.isArray(j.attachedFiles)
          ? j.attachedFiles.length > 0
            ? JSON.stringify(j.attachedFiles)
            : ""
          : j.attachedFiles;
        return { ...j }
      })
    },
    // 开始检查
    subCheckContent(type) {
      const subList = this.treeDetailList.map(i => {
        const itme = { ...i }
        return itme
      })
      let checkParams = JSON.parse(sessionStorage.getItem('checkParams'))
      let params = {
        categoryList: []
      }
      if (this.type == 'TS') {
        params.inspectionLedger = { ...this.inspectionLedger }
        params.inspectionLedger.hospitalCode = this.queryData.hospitalCode
        params.inspectionLedger.hospitalName = this.queryData.hospitalName
        params.inspectionLedger.checkSide = '1'
        params.inspectionLedger.attachedFiles = this.checkAbout.attachment.length ? JSON.stringify(this.checkAbout.attachment) : ''
        params.inspectionLedger.inspectionDate = moment(this.checkAbout.date).unix()
        params.inspectionLedger.inspectionDateStr = this.checkAbout.date
        params.inspectionLedger.fillDate = moment().unix()
        params.inspectionLedger.fillDateStr = moment().format('YYYY-MM-DD HH:mm:ss')
        params.inspectionLedger.confirmationStatus = type
      } else {
        params.inspectionLedger = {
          hospitalCode: this.queryData.hospitalCode,
          hospitalName: this.queryData.hospitalName,
          hospitalAreaCode: checkParams.hospitalAreaCode,
          hospitalAreaName: checkParams.hospitalAreaName,
          hospitalAddress: checkParams.address,
          inspectionTableId: checkParams.inspectionTableId,
          inspectorRole: checkParams.inspectorRole,
          // inspectorId: checkParams.inspectorId,
          // inspectorName: checkParams.inspectorName,
          // inspectorPhone: checkParams.inspectorPhone,
          assistantInspectors: checkParams.assistantInspectors,
          responsiblePersonName: checkParams.responsiblePersonName,
          responsiblePersonPhone: checkParams.responsiblePersonPhone,
          confirmationStatus: type,
          attachedFiles: this.checkAbout.attachment.length ? JSON.stringify(this.checkAbout.attachment) : '',
          inspectionDate: moment(this.checkAbout.date).unix(),
          inspectionDateStr: this.checkAbout.date,
          fillDate: moment().unix(),
          fillDateStr: moment().format('YYYY-MM-DD HH:mm:ss'),
          checkSide: "1"
        }
      }
      params.inspectionLedger.inspectorId = this.loginInfo.id
      params.inspectionLedger.inspectorName = this.loginInfo.name
      params.inspectionLedger.inspectorPhone = this.loginInfo.phone
      params.inspectionLedger.resultYes = this.mathCountObj.yesCount
      params.inspectionLedger.resultNo = this.mathCountObj.noCount
      let categoryList = []
      subList.forEach((item, index) => {
        if (item.isFormat) {
          delete item.isFormat
          const content = item.children[0].contentList
          item.contentList = content
          item.children = []
        }
        this.flatArray = [];
        this.convertTreeToArray(item);
        this.flatArray.forEach((i, idx) => {
          let obj = {
            categoryId: i[i.length - 1].id,
            categorySort: idx,
            categoryInfo: JSON.stringify(
              i.map((v) => {
                return { id: v.id, pId: v.pId, name: v.name };
              })
            ),
            contentList: this.processContentList(i[i.length - 1].contentList),
          };
          categoryList.push(obj);
        })
      })
      params.categoryList = categoryList;
      console.log('参数', params)
      this.$toast.loading({
        message: '正在提交...',
        forbidClick: false
      })
      this.axios.postContralHostBase('saveCheckTable', params, res => {
        this.$toast.clear()
        if (res.code == 200) {
          this.$toast(type == '0' ? '暂存成功' : '保存成功')
          setTimeout(() => {
            sessionStorage.clear()
            this.$router.go(-1)
          }, 800);
        } else {
          this.$message.error(res.message);
        }
      })
    },
    // 提交隐患
    subHiddenDanger(treeItem, item) {
      console.log(treeItem, item)
      const checkParams = JSON.parse(sessionStorage.getItem('checkParams'))
      const content = { ...treeItem }
      let typeTree = {
        treeId: [],
        treeName: []
      }
      if(content.children && content.children.length){
        typeTree.treeId = this.contentFormat(content.children,[], 'id')
        typeTree.treeName = this.contentFormat(content.children,[], treeItem.isFormat ? 'categoryName' : 'name')
      }
      if (!typeTree.treeId.includes(content.id)) {
        typeTree.treeId.unshift(content.id) // 分类id
        typeTree.treeName.unshift(content.name) // 分类name
      }
      typeTree.treeId.push(item.contentTableId) // 内容id
      typeTree.treeName.push(item.content) // 内容name
      if (checkParams) {
        typeTree.treeId.unshift(checkParams.inspectionTableId) // 表id
        typeTree.treeName.unshift(checkParams.inspectionName) // 表name
      } else {
        typeTree.treeId.unshift(this.inspectionLedger.inspectionTableId) // 表id
        typeTree.treeName.unshift(this.inspectionLedger.inspectionTableName) // 表name
      }
      const describe = '问题来源：' + typeTree.treeName[0] + '\n' + '检查项路径：' + typeTree.treeName.filter((item, index) => index != 0).join('>')
      api.openWin({
        name: 'imas/snapshotPro',
        url: 'widget://html/common_window.html',
        bgColor: 'rgba(250, 250, 250, 0)',
        hideHomeIndicator: true,
        bounces: false,
        scrollEnabled: false,
        useWKWebView: true,
        pageParam: {
          title: "隐患上报",
          fromPage: 'checkTable',
          typeTreeName: typeTree.treeName.join('>'),
          typeTreeId: typeTree.treeId.join(','),
          describe,
          hospitalCode: this.queryData.hospitalCode,
          hospitalName: this.queryData.hospitalName
        }
      });
    },
    // 处理str类型json
    formatJson(strJson) {
      function flattenTree(treeList, result = [], pId = null) {
        treeList.forEach(node => {
          const newNode = { ...node, pId }
          delete newNode.children
          result.push(newNode)
          if (node.children) {
            flattenTree(node.children, result, node.id); // 递归处理子节点
          }
        });
        return result;
      }
      const terrData = flattenTree(strJson)
      for (var i = 0; i < terrData.length; i++) {
        if (terrData[i].contentList.length) {
          terrData[i].contentList.forEach(k => {
            if (k.attachedFiles) {
              if (Array.isArray(k.attachedFiles)) {
                k.attachedFiles.forEach(g => {
                  g.url = this.baseUrl + g.url
                })
              } else {
                k.attachedFiles = JSON.parse(k.attachedFiles)
                k.attachedFiles.forEach(g => {
                  g.url = this.baseUrl + g.url
                })
              }
              k.fileEcho = k.attachedFiles
            }
          })
        }
      }
    },
    // 计算是否项
    mathCount() {
      this.$nextTick(() => {
        const treeList = this.treeDetailList.map(i => {
          const itme = { ...i }
          return itme
        })
        function flattenTree(treeList, result = [], pId = null) {
          treeList.forEach(node => {
            const newNode = { ...node, pId }
            delete newNode.children
            result.push(newNode)
            if (node.children) {
              flattenTree(node.children, result, node.id); // 递归处理子节点
            }
          });
          return result;
        }
        const terrData = flattenTree(treeList)
        const contentListArr = []
        for (var i = 0; i < terrData.length; i++) {
          if (terrData[i].contentList.length) {
            const optionPrent = terrData[i].contentList
            optionPrent.forEach(j => contentListArr.push(j))
          }
        }
        this.mathCountObj.yesCount = contentListArr.filter(i =>i.radio && i.radio.slice(0, i.radio.lastIndexOf('_')) == '是').length
        this.mathCountObj.noCount = contentListArr.filter(i => i.radio && i.radio.slice(0, i.radio.lastIndexOf('_')) == '否').length
      })
    },
    // 格式化内容数据
    contentFormat(arr,newArr, type) {
			for (let index = 0; index < arr.length; index++) {
        newArr.push(arr[index][type])
				if (arr[index].contentList && arr[index].contentList.length) {
					return newArr
				}
				if (arr[index].children && arr[index].children.length) {
					const result = this.contentFormat(arr[index].children,newArr,type);
					if (result !== undefined) {
						return result;
					}
				}
			}
			return null;
		},
    // 附件预览
    lookDown(item) {
      let file = {
        name: item.name,
        url: this.baseUrl + item.url
      }
      var Type = file.name.split(".").pop();
      if (Type != "jpg" && Type != "jpeg" && Type != "png" && Type != "JPG" && Type != "JPEG" && Type != "gif") {
        var localPath = api.cacheDir + "/personalDocuments/" + file.name;
        api.download(
          {
            url: file.url,
            savePath: localPath,
            cache: true,
            allowResume: true
          },
          function(ret, err) {
            console.log(JSON.stringify(ret));
            if (ret.state == 1) {
              if (api.systemType == "ios") {
                var docReader = api.require("docReader");
                docReader.open(
                  {
                    path: ret.savePath,
                    autorotation: false
                  },
                  function(ret, err) {}
                );
              } else {
                // 安卓
                // 文档  图片
                var fileType = file.name.split(".").pop();
                if (fileType == "mp4" || fileType == "mp3" || fileType == "amr" || fileType == "MP4" || fileType == "MP3") {
                  // 安卓 视屏
                  api.openVideo({
                    url: ret.savePath
                  });
                } else {
                  var docReader = api.require("docReader");
                  docReader.open(
                    {
                      path: ret.savePath,
                      autorotation: false
                    },
                    function(ret, err) {}
                  );
                }
              }
            } else {
            }
          }
        );
      } else {
        ImagePreview({
          images: [file.url],
          showIndex: true,
          loop: false,
          swipeDuration: 50
        })
      }
    },
    // 确认
    checkConfirm() {
      this.$toast.loading({
        message: '正在提交...',
        forbidClick: false
      })
      this.axios.postContralHostBase('checkConfirm', {
        id: this.inspectionLedger.id
      }, res => {
        this.$toast.clear()
        if (res.code == 200) {
          this.$toast('确认成功')
          setTimeout(() => {
            this.$router.go(-1)
          }, 1000);
        } else {
          this.$message.error(res.message);
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .content {
    width: 100vw;
    height: 100vh;
    font-size: 16px;
    .inner {
      position: relative;
      height: calc(100% - 60px);
      background-color: #F2F4F9;
      .detailListWrap {
        // height: calc(100% - 266px);
        max-height: calc(100% - 256px);
        padding: 10px 10px 0;
        overflow: auto;
        /deep/ .treeWrap {
          margin-bottom: 10px;
          border-radius: 8px;
          padding: 10px;
          .el-tree-node__expand-icon.expanded {
            transform: rotate(180deg);
          }
          .el-tree-node:focus>.el-tree-node__content {
            background-color: unset;
          }
          .el-tree-node__content:hover {
            background-color: unset;
          }
          .el-tree-node__content {
            position: relative;
            overflow: hidden;
            margin: 8px 0;
            .el-tree-node__expand-icon {
              position: absolute;
              right: 0;
            }
            .custom-tree-node {
              width: 100%;
              display: flex;
              align-items: center;
              .titleWrap {
                display: flex;
                align-items: center;
                width: 100%;
              }
              .titleIcon {
                display: inline-block;
                width: 4px;
                height: 16px;
                background-color: #29BEBC;
                margin-right: 8px;
              }
              .tabelContent {
                width: 100%;
                .contentTitle {
                  padding-left: 10px;
                  margin: 8px 0;
                  display: flex;
                  align-items: center;
                  .contentText {
                    width: calc(100% - 70px);
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  }
                  .gist {
                    width: 70px;
                    color: #29BEBC;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                  }
                }
                .fillInfo {
                  padding-top: 8px;
                  margin: 0 auto;
                  width: 90%;
                  .groupWrap {
                    margin-bottom: 8px;
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    button {
                      padding: 0 5px;
                      height: 24px;
                    }
                    .van-radio-group {
                      width: calc(100% - 60px);
                      .van-radio__icon {
                        font-size: 16px;
                      }
                      .van-radio__icon--checked {
                        .van-icon {
                          color: #fff;
                          background-color: #29BEBC;
                          border-color: #29BEBC;
                        }
                      }
                      .van-radio__label {
                        color: #323233 !important;
                        overflow: auto;
                      }
                    }
                  }
                  .van-cell {
                    padding: 0 10px;
                  }
                  .disArea::after {
                    border-bottom: none !important;
                  }
                }
              }
            }
            .titleTetx {
              color: #1D2129;
              font-weight: bold;
            }
            .contentItem {
              flex-direction: column;
              align-items: flex-start;
              height: auto;
              width: 100%;
            }
          }
        }
        .treeWrap:last-child {
          margin-bottom: 0px;
        }
      }
      .checkAbout {
        // position: absolute;
        width: calc(100% - 32px);
        margin-top: 10px;
        // bottom: 71px;
        background-color: #fff;
        padding: 0 16px 10px;
        /deep/ .van-cell {
          padding: 8px 0;
          .van-cell__title {
            color: #1D2129;
          }
          .van-field__control {
            text-align: right;
          }
        }
        .van-cell--required::before {
          left: -6px;
        }
      }
      .checkStaff {
        // position: absolute;
        // bottom: 71px;
        margin-top: 10px;
        left: 0;
        width: calc(100% - 32px);
        background-color: #fff;
        padding: 8px 16px;
        .itemWrap {
          display: flex;
          align-items: center;
          padding: 8px;
          span:first-child {
            color: #4E5969;
            width: 80px;
          }
          span:last-child {
            color: #1D2129;
            width: calc(100% - 80px);
            overflow: auto;
            .fileItem {
              color: #29BEBC;
              overflow: auto;
              white-space: nowrap;
            }
          }
        }
      }
      .bottomBra {
        // position: absolute;
        // bottom: 0;
        padding: 8px 16px;
        width: calc(100% - 32px);
        background-color: #fff;
        display: flex;
        justify-content: space-between;
        .bottomText {
          width: calc(50% - 32px);
          padding: 12px 16px;
          color: #4E5969;
        }
        .bottomBtn {
          width: 50%;
          display: flex;
          justify-content: space-between;
          .van-button {
            width: 50%;
            height: 44px;
            border: none;
          }
          .van-button:first-child {
            background-color: rgba(41,190,188,0.15);
            color: #29BEBC;
            margin-right: 10px;
          }
          .van-button:last-child {
            background-color: #29BEBC;
            color: #fff;
          }
        }
        .confirmBtn {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          .van-button {
            height: 30px;
            width: 100px;
          }
        }
      }
      .attachmentTitle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        span:first-child {
          color: #1D2129;
        }
        span:last-child {
          color: #86909C;
        }
      }
    }
  }
  .popupClass{
    overflow-x: scroll;
    -webkit-overflow-scrolling: touch;
  }
  .popupClass::-webkit-scrollbar-track-piece {
    background-color: rgba(0, 0, 0, 0);
    border-left: 1px solid rgba(0, 0, 0, 0);
  }
  .popupClass::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  }
  .popupClass::-webkit-scrollbar-thumb {
  background-color: rgba(191, 191, 191, 191);
  background-clip: padding-box;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  min-height: 28px;
  }
  .popupClass::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  }
</style>