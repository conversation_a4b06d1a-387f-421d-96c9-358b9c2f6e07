import configAr from './checkConfig'//配置
import checkObject from './checkObject'//校验函数
class CheckCentralControl {
    //启动主函数
    checkMainFunction(questionArr, answerArr) {
        console.log('传参',questionArr,answerArr)
        var checkReturnArr = [];
        //循环问题列表
        for (var a = 0; a < questionArr.length; a++) {
            //创建校验对象
            var obj = { question: questionArr[a], answer: [] }
            for (var b = 0; b < answerArr.length; b++) {
                //循环的题型填充答案
                if (questionArr[a].id == answerArr[b].questionId) {
                    //校验对象填充答案
                    obj.answer.push(answerArr[b]);
                }
            }
            console.log(obj,answerArr,"--+-+-+-+---")
            // 获取校验对象  启动校验函数
            var configObject = configAr[obj.question.type];
            for (var c = 0; c < configObject.checkBox.length; c++) {
                let conObj = configObject.checkBox[c];
                //所有校验都返回  是否校验成功
                var flag = checkObject[conObj.fun](conObj, obj, checkReturnArr);
                console.log(flag)
                //失败后直接终止当前题的校验
                if (flag) {
                    break;
                };
            }
        }
        return checkReturnArr;
    }
}
let checkCentralControl = new CheckCentralControl();
export default checkCentralControl;