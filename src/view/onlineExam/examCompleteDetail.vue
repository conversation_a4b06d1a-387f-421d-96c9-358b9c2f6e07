<template>
  <div class="container">
         <Header :title="title" @backFun="$router.go(-1)"> </Header>

    <div class="content_top">
      <div style="width:100%">
        <!-- <img style="width:100%" :src="require('../../assets/images/bg.png')" alt=""> -->
      </div>
      <div class="nav">
        <div>
          <div>
            <span class="sty">{{Number(analysisData.userTotalScore)}}</span>
          </div>
          <p class="p">您的得分</p>
        </div>

        <div>
          <div>
            <span class="sty1">{{Number(analysisData.rightQuestionCount)}}</span>
          </div>
          <p class="p">答对题数</p>
        </div>

        <div>
          <div>
            <span>试卷总分：{{Number(analysisData.totalScore)}}</span>
          </div>

          <p class="p">试卷题数：{{Number(analysisData.totalQuestion)}}</p>
        </div>

        <!-- <div class="table_Top">

          <div class="top_text">第 <span class="top_text_num">{{Number(analysisData.orderNum)}}</span> 名，参与人数 {{Number(analysisData.totalUser)}} 人</div>
        </div>
        <div class="table_Bottom">
          <div class="bottom_left">
            <div class="bottom_div">得分<span class="bottom_num">{{Number(analysisData.userTotalScore)}}</span></div>
            <div class="bottom_size">总分{{Number(analysisData.totalScore)}}分</div>
          </div>
          <div class="bottom_line"></div>
          <div class="bottpm_right">
            <div class="bottom_div">答对<span class="bottom_num">{{Number(analysisData.rightQuestionCount)}}</span></div>
            <div class="bottom_size">共{{Number(analysisData.totalQuestion)}}题</div>
          </div>
        </div> -->
      </div>
    </div>
    <!-- <table class="table" border="1" cellspacing="0">
      <tr>
        <td>{{Number(analysisData.userTotalScore)}}分</td>
        <td>第{{Number(analysisData.orderNum)}}名</td>
        <td>答对{{Number(analysisData.rightQuestionCount)}}题</td>
      </tr>
      <tr>
        <td>总分：{{Number(analysisData.totalScore)}}分</td>
        <td>参与人数：{{Number(analysisData.totalUser)}}人</td>
        <td>题数：{{Number(analysisData.totalQuestion)}}题</td>
      </tr>
    </table> -->
    <div class="answerTitle" @click="btnFlag = !btnFlag">
      <!-- <span>答案解析</span> -->
      <!-- <span>
        <i v-if="btnFlag" class="el-icon-arrow-down el-icon--right"></i>
        <i v-else class="el-icon-arrow-up el-icon--right"></i>
      </span> -->
    </div>
    <!-- :class="flag?'':'buttons'" -->
    <!-- <div v-if="btnFlag">
      <el-button @click="btnFlag = !btnFlag" type="primary" class="answerBtn"
        >查看答案解析<i class="el-icon-arrow-down el-icon--right"></i
      ></el-button>
    </div> -->
    <div style="position: relative" v-if="!btnFlag">
      <!-- <el-button @click="btnFlag = !btnFlag" type="primary" class="answerUpBtn"
        >答案解析<i class="el-icon-arrow-up el-icon--right"></i
      ></el-button> -->
      <!-- <van-tabs style="margin: 10px 0" @change="tabChange" v-model="activeName"> -->
      <!-- <van-tab
          v-for="item in tabList"
          :key="item.key"
          :name="item.key"
          :title="item.title"
        > -->
      <!-- 问卷题目列表 -->
      <div class="question-content">
        <div v-for="qSubject in questionPreviewList.questions" :key="qSubject.id" class="preview-container">
          <component :previewOption="qSubject" :list="questionPreviewList" :index="qSubject.numIndex" ref="child" :isQuestionNum="true" :is="questionPreview[qSubject.type]" />
        </div>
      </div>
      <!-- </van-tab> -->
      <!-- </van-tabs> -->
      <!-- <div @click="btnFlag = !btnFlag" class="upAnswer"><span>收起答案解析</span><i class="el-icon-arrow-up el-icon--right"></i></div> -->
    </div>
  </div>
</template>
<script>
import topPart from "./components/topPart";
import RadioAnalysis from "./components/RadioAnalysis.vue";
import CheckBoxAnalysis from "./components/CheckBoxAnalysis.vue";
import JudgeAnalysis from "./components/JudgeAnalysis.vue";
// import { getOnlineQuestionAnalyzeById } from "../../common/api/studyOnline";
export default {
  components: {
    topPart,
    RadioAnalysis,
    CheckBoxAnalysis,
    JudgeAnalysis
  },
  data() {
    return {
       title:JSON.parse(localStorage.getItem('questRow')).name,
      btnFlag: false, //btn点击切换
      activeName: "all", //默认选中全部 all/error
      analysisData: {}, //表格分析数据
      tabList: [
        {
          title: "全部题目",
          key: "all"
        },
        {
          title: "错题",
          key: "error"
        }
      ],
      quest: JSON.parse(localStorage.getItem("questRow")),

      questionPreviewList: [],
      questionPreviewAllList: [],
      questionPreviewErrorList: [],
      questionPreview: {
        radio: "RadioAnalysis",
        checkbox: "CheckBoxAnalysis",
        determine: "JudgeAnalysis"
      }
    };
  },
  mounted() {
    this.$route.query.hasOwnProperty("name") ? (document.title = this.quest.name) : "";
    window.scroll(0, 0);
    this.questionId = this.quest.id;
    this.getQuestionAnalyze(this.questionId);
  },
  methods: {
    //获取 分析数据
    getQuestionAnalyze(id) {
      const params = { pvqId: id, isdetail: true };
      this.axios.postContralHostBase("getOnlineQuestionAnalyzeById", params, res => {
        if (res.code == 200) {
          this.analysisData = res.data;
          const questionList = res.data.questions;
          const previewData = [];
          let numIndex = 0;
          for (let index = 0; index < questionList.length; index++) {
            const item = questionList[index];
            numIndex++;
            previewData.push({ ...item, numIndex });
          }
          this.questionPreviewList = { ...res.data, questions: previewData };
          let dataList = JSON.parse(JSON.stringify(this.questionPreviewList.questions));
          this.questionPreviewAllList = dataList;
          this.questionPreviewErrorList = dataList.filter(item => {
            return item.optionsData.some(e => e.isAnswer != e.isSelected);
          });
          // console.log(this.questionPreviewErrorList);
          //初始化赋值
        } else {
          this.$message.error(res.msg);
        }
      });
      // getOnlineQuestionAnalyzeById(params).then((res) => {
      //   if (res.code == 200) {
      //     this.analysisData = res.data
      //     const questionList = res.data.questions;
      //     const previewData = [];
      //     let numIndex = 0;
      //     for (let index = 0; index < questionList.length; index++) {
      //       const item = questionList[index];
      //       numIndex++;
      //       previewData.push({ ...item, numIndex });
      //     }
      //     this.questionPreviewList = { ...res.data, questions: previewData }
      //     let dataList = JSON.parse(JSON.stringify(this.questionPreviewList.questions))
      //     this.questionPreviewAllList = dataList;
      //     this.questionPreviewErrorList = dataList.filter(item=> {
      //       return item.optionsData.some(e=> e.isAnswer != e.isSelected)
      //     })
      //     // console.log(this.questionPreviewErrorList);
      //     //初始化赋值
      //   } else {
      //     this.$message.error(res.msg);
      //   }
      // });
    },
    //tab切换
    tabChange(val) {
      if (val == "all") {
        this.questionPreviewList.questions = this.questionPreviewAllList;
      } else {
        this.questionPreviewList.questions = this.questionPreviewErrorList;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.content {
  padding-top: 50px;
  .content_top {
    width: 100%;
    position: relative;
    .content_table {
      position: absolute;
      width: 90%;
      top: 30px;
      left: 5%;
      background: #fff;
      height: 100%;
      border-radius: 10px;
      display: flex;
      justify-content: space-around;
      flex-direction: column;
      padding: 0 30px;
      box-sizing: border-box;
      box-shadow: 1px 4px 5px #999;
      .top-img {
        height: 12vw;
        width: 12vw;
        border-radius: 50%;
        overflow: hidden;
        .imgSty {
          width: 100%;
          height: 100%;
        }
      }
      .table_Top {
        display: flex;
        justify-content: space-around;
        align-items: center;
        .top_text {
          font-size: 15px;
          color: #353535;
          font-weight: 600;
          .top_text_num {
            font-size: 22px;
            font-weight: 700;
            color: #29bebc;
            margin: 0 5px;
          }
        }
      }
      .table_Bottom {
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding: 0 20px;
        .bottom_num {
          font-size: 22px;
          font-weight: 700;
          color: #353535;
          margin: 0 5px;
        }
        .bottom_div {
          font-size: 15px;
          color: #353535;
          font-weight: 600;
          margin-bottom: 5px;
        }
        .bottom_size {
          font-size: 15px;
          color: #cacad2;
        }
        .bottom_line {
          border-right: 2px solid #cacad2;
          height: 80%;
        }
      }
    }
  }
  .upAnswer {
    position: absolute;
    font-size: 14px;
    text-align: center;
    color: #353535;
    cursor: pointer;
    bottom: -25px;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .table {
    width: 90%;
    margin: 10px auto;
    font-size: 16px;
    text-align: center;
    tr {
      &:first-child {
        height: 130px;
      }
      &:last-child {
        height: 50px;
      }
    }
  }
  .answerTitle {
    // margin-top: 40px;
    display: flex;
    justify-content: space-between;
    padding: 10px 25px;
    cursor: pointer;
    font-size: 18px;
    font-weight: 600;
  }

  .answerBtn {
    width: 80%;
    margin: 10px 10%;
  }
  .answerUpBtn {
    width: 40%;
    margin: 10px 5%;
    position: absolute;
    z-index: 1;
  }
  .question-content {
    background-color: #f0f0f4;
    // padding-top: 35px;
    margin-bottom: 50px;
  }

  .preview-container {
    background-color: #fff;
    border-bottom: 1px solid #d8dee7;
    margin: 1px 0px;
    padding: 0 10px;
  }
  /deep/ .van-tab {
    font-size: 17px;
  }
  /deep/ .van-tab--active {
    font-size: 17px;
    /* font-weight: 600; */
    color: rgb(41, 190, 188);
  }
  /deep/ .van-tabs__line {
    background-color: rgb(41, 190, 188);
    width: 50%;
    height: 2px;
  }
  // /deep/ .van-tabs__wrap {
  //   margin-left: 50%;
  //   width: 50%;
  // }
  /deep/ .van-tabs__content {
    // padding: 15px 20px;
  }
}
.nav {
  // height:215px;
  height: 60px;
  margin-top: 10px;
  display: flex;
  justify-content: space-around;
  text-align: center;
  > div {
    flex: 1;
      color: #86909C;
      font-size: 14px;
  }
}
.sty{
  font-weight: 700;
  font-size: 25px;
  color: #4670cd;
}
.sty1{
  font-weight: 700;
  font-size: 25px;
  color: #000;
}
.container {
  width: 100vw;
  background-color: #fff;
  padding-bottom: 5vh;
}
</style>
