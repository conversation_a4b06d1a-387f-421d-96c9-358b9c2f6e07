<template>
  <div class="container">
    <Header :title="title" @backFun="backAnswer"> </Header>
    <!--头部开始 -->
    <!-- <div class="top-box">
    <span class="back-box" @click="backAnswer">
      <van-icon name="arrow-left" style="min-width: 1em;margin-right: 0.106667rem;vertical-align: sub;font-size: 16px;" />
      <span class="return-text">返回</span>
    </span>

  </div> -->
    <div v-if="currentQuestionItem.type == '2'" class="titleData">
      <span v-if="questionPreviewList.answerId == '-1'"
        >剩余时间：
        <van-count-down style="display:inline-block" ref="countDown" :time="remainingTime" :auto-start="false" format="mm:ss" @finish="timeFinish" />
      </span>
      <span v-else
        >答题时间：
        <van-count-down style="display:inline-block" ref="countDown" :time="remainingTime" :auto-start="false" format="mm:ss" />
      </span>
      <span>总分：{{ scoreTotle }}</span>
    </div>
    <!-- 头部结束 -->
    <div v-show="!test" class="my-content ques-content-box">
      <!-- 答卷时的进度条 -->
      <div :class="['main-container', questionPreviewList.type == '2' ? 'main-content-exam' : 'main-content']">
        <el-progress
          class="progress"
          v-if="questionPreviewList.isProgress == 1 && questionPreviewList.answerId == -1"
          color="#2cc7c5"
          :text-inside="true"
          :stroke-width="20"
          :percentage="answerPercentage"
          status="success"
        ></el-progress>
        <!-- 问卷前置说明 -->
        <div
          v-if="questionPreviewList.startText"
          :class="['question-preText', questionPreviewList.isProgress == 1 && questionPreviewList.answerId == -1 ? 'main-container-progress' : '']"
        >
          {{ questionPreviewList.startText }}
        </div>
        <!-- 问卷题目列表 -->
        <div :class="['question-content', IsProgress() ? 'main-container-progress' : '']">
          <div v-for="qSubject in questionPreviewList.questions" :key="qSubject.id" class="preview-container">
            <!-- questionPreviewList.answerId表示当前问卷是否已做答，-1表示未答 -->
            <!-- :isAnswered="false" -->
            <component
              :isAnswered="btnDisabled()"
              :previewOption="qSubject"
              :list="questionPreviewList"
              :index="qSubject.numIndex"
              ref="child"
              :isQuestionNum="questionPreviewList.isQuestionNum == 1"
              :is="questionPreview[qSubject.type]"
              :isChecks="isCheck"
            />
          </div>
          <div class="my_flex" v-if="forceSign == 1">
            <div class="sign_title">签名</div>
            <div v-if="signatureImg" class="resign">
              <img class="sign-img" :src="signatureImg" />
              <span @click="showPopup" class="resign-txt" v-if="questionPreviewList.answerId == -1">重新签名</span>
            </div>
            <div v-else class="signature_edit" @click="showPopup">
              <div>
                <img src="../../assets/images/edit.png" alt />
                <span class="no_singature">签名</span>
              </div>
            </div>
          </div>
          <!-- 签名展示 结束-->
        </div>
      </div>

      <div class="submit-container" v-show="questionPreviewList.questions && questionPreviewList.questions.length !== 0">
        <el-button
          v-if="questionPreviewList.answerId == -1 || questionPreviewList.type == '1'"
          type="primary"
          style="width: 80%"
          :class="flag ? '' : 'buttons'"
          @click="submitQuestinAnswer"
          :disabled="!flag"
          >提交</el-button
        >
        <el-button v-else type="primary" style="width: 80%" @click="answerDetail">查看答案解析</el-button>
      </div>
    </div>

    <!-- 签名弹出框 -->
    <div v-show="test" class="sign" style="position:fixed;top:0;z-index:9999;bottom:0">
      <signature @closePop="closePop" @saveImg="complete"></signature>
    </div>
    <!-- 签名弹出框结束 -->
  </div>
</template>
<script>
import axios from "axios";

import Vue from "vue";
import { CountDown, Toast } from "vant";
Vue.use(CountDown);
Vue.use(Toast);
import utils from "./utils/utils";
// import { questionPvqQuestionDetail, saveQuestionAnswer, uploadImg } from "../../common/api/studyOnline"
import PreviewMatrix from "./components/previewMatrix";
import PreviewFillBlank from "./components/PreviewFillBlank";
import PreviewRadio from "./components/PreviewRadio";
import PreviewRadioExam from "./components/PreviewRadioExam";
import PreviewJudge from "./components/PreviewJudge";
import PreviewCheckBox from "./components/PreviewCheckBox";
import PreviewCheckBoxExam from "./components/PreviewCheckBoxExam";
import PreviewSelect from "./components/PreviewSelect";
import PreviewMulSelect from "./components/PreviewMulSelect";
import PreviewSort from "./components/PreviewSort";
import PreviewParagraph from "./components/PreviewParagraph";
import moment from "moment";
import signature from "./components/signature.vue";
import notice from "./notice/notice.js";
export default {
  components: {
    PreviewMatrix,
    PreviewFillBlank,
    PreviewRadio,
    PreviewRadioExam,
    PreviewJudge,
    PreviewCheckBox,
    PreviewCheckBoxExam,
    PreviewSelect,
    PreviewMulSelect,
    PreviewSort,
    PreviewParagraph,
    signature
  },
  data() {
    return {
      title: JSON.parse(localStorage.getItem("questRow")).name,
      flag: false,
      remainingTime: 0, //剩余时间
      scoreTotle: 0, //总分
      questionPreview: {},
      questionPreviewNaire: {
        radio: "PreviewRadio",
        checkbox: "PreviewCheckBox",
        input: "PreviewFillBlank",
        array: "PreviewMatrix",
        paragraph: "PreviewParagraph",
        sort: "PreviewSort",
        select: "PreviewSelect",
        nd_select: "PreviewMulSelect"
      },
      questionPreviewExam: {
        radio: "PreviewRadioExam",
        checkbox: "PreviewCheckBoxExam",
        determine: "PreviewJudge"
      },
      questionPreviewList: {}, //当前问卷的题目列表
      openTime: "",
      answerPercentageStep: 0, //进度条的步长
      answerPercentage: 0, //进度条的进度
      questionSubjectPercentage: {},
      questionId: "",
      test: "", //签名组件
      currentParams: {}, //提交参数
      signatureImg: "", //签名图片
      signatureSrc: "", //签名无baseUrl地址
      forceSign: "", //是否需要签名
      minQuestionList: "",
      maxQuestionList: "",
      answerRecordsList: "",
      checkData: "",
      isCheck: false,
      flags: false,
      currentQuestionItem: {}, //当前题目的 item
      goBack: false, //默认不可已直接退出
      quest: JSON.parse(localStorage.getItem("questRow"))
    };
  },
  //销毁观察者
  destroyed() {
    notice.offBrother("questionnaireObserver");
  },
  created() {
    // 注册观察者
    notice.onBrother("questionnaireObserver", data => {
      //提交按钮校验触发
      this.submitCheck();
    });
    //观察者注册结束
    // const currentQuestionItem = JSON.parse(
    //   localStorage.getItem("currentQuestionItem")
    // );
    const currentQuestionItem = JSON.parse(localStorage.getItem("questRow"));
    this.currentQuestionItem = currentQuestionItem;
    document.title = currentQuestionItem.name;
    if (currentQuestionItem.type == "2") {
      this.questionPreview = this.questionPreviewExam;
    } else {
      this.questionPreview = this.questionPreviewNaire;
    }
  },
  mounted() {
    // const questionId = this.getQueryString("id");
    // this.questionId = questionId
    //   ? questionId
    //   : localStorage.getItem("currentQuestionId");
    // localStorage.getItem("currentQuestionId", this.questionId);
    this.questionId = this.quest.id;
    // this.getCurrentQuestionAllSubject(this.questionId);
    this.getCurrentQuestionAllSubject(this.questionId);

    this.openTime = moment(utils.obtainEast8Date()).format("YYYY-MM-DD HH:mm:ss");
  },
  methods: {
    //返回退出
    backAnswer() {
      if (this.questionPreviewList.answerId == "-1" && this.questionPreviewList.type == "2" && !this.goBack) {
        // this.$dialog({
        //   type: 'confirm',
        //   title: '是否交卷？',
        //   message: '退出页面会直接交卷，请确认是否交卷',
        //   confirmBtn: {
        //     text: '确定交卷',
        //     active: true,
        //     disabled: false,
        //     href: 'javascript:;'
        //   },
        //   cancelBtn: {
        //     text: '继续答题',
        //     active: false,
        //     disabled: false,
        //     href: 'javascript:;'
        //   },
        //   onConfirm: () => {
        //     this.autoSubmitQuestin()
        //   },
        // }).show()
        this.$dialog
          .confirm({
            type: "confirm",
            title: "是否交卷？",
            message: "退出页面会直接交卷，请确认是否交卷"
          })
          .then(() => {
            this.autoSubmitQuestin();
          })
          .catch(() => {});
      } else {
        this.$router.go(-1);
      }
    },
    //倒计时结束
    timeFinish() {
      Toast.loading({
        message: "答题时间已结束，正在自动交卷中...",
        forbidClick: true
      });
      this.autoSubmitQuestin();
    },
    //未答且有进度条才显示
    IsProgress() {
      if (this.questionPreviewList.isProgress == 1 && this.questionPreviewList.answerId == -1) {
        if (this.questionPreviewList.startText) return false;
        return true;
      } else return false;
    },
    //按钮校验
    submitCheck() {
      const childList = this.$refs.child;
      let answerRecords = [];
      let isBreak = [];
      for (let index = 0; index < childList.length; index++) {
        const item = childList[index];
        // 调用子组件的doValided方法进行题目验证，子组件必须有doValided和checkValided方法，详见子组件
        const isValided = item.doValidedFlag();
        if (item.$vnode.tag.indexOf("Paragraph") < 0) {
          if (isValided) {
            isBreak.push(index);
          } else {
            answerRecords = answerRecords.concat(item.answerVal);
          }
        }
      }
      this.checkData = answerRecords;
      var flagCount = false;
      if (isBreak.length === 0) {
        //当前问卷题目均为非必答题时，阻止未做答任何题目点击提交
        if (answerRecords.length !== 0) {
          flagCount = true;
        }
      }
      this.flag = flagCount;
    },
    // 是否不可点击
    btnDisabled() {
      const { answerId, statusRun, status } = this.questionPreviewList;
      // statusRun==0 暂停 status=='recovery'完成
      return answerId != -1 || statusRun == 0 || status == "recovery";
    },
    getQueryString(name) {
      var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
      var r = window.location.search.substr(1).match(reg);
      if (r != null) {
        return unescape(r[2]);
      }
      return null;
    },

    // 获取当前问卷的必填题目，使用必填题目个数获取单个题目的进度条步长
    getAnswerPercentageStep() {
      let questionSubjectLength = 0;
      this.questionPreviewList.questions.forEach(qSubject => {
        if (this.questionPreviewList.type != "2") {
          qSubject.isMust == 1 && questionSubjectLength++;
        } else {
          questionSubjectLength++;
        }
      });
      //无必答题时问卷等同于在在线考试按填写数量
      if (questionSubjectLength == 0) {
        questionSubjectLength = this.questionPreviewList.questions.length;
      }
      this.answerPercentageStep = Math.ceil(100 / questionSubjectLength);
    },
    /**
     * 设置进度条的总进度
     * @questionSubjectId: 题目id
     * @isAnswer: true:题目已做答添加到对象 false：题目未做答从对象remove
     */
    setAnswerPercentage(questionSubjectId, isAnswer) {
      if (this.questionPreviewList.isProgress != 1) {
        return;
      }
      this.answerPercentage = 0;
      if (isAnswer) {
        this.questionSubjectPercentage[questionSubjectId] = this.answerPercentageStep;
      } else {
        this.questionSubjectPercentage.hasOwnProperty(questionSubjectId) && delete this.questionSubjectPercentage[questionSubjectId];
      }
      for (const key in this.questionSubjectPercentage) {
        if (this.questionSubjectPercentage.hasOwnProperty(key)) {
          const element = this.questionSubjectPercentage[key];
          this.answerPercentage = this.answerPercentage + element > 100 ? 100 : this.answerPercentage + element;
        }
      }
    },
    getCurrentQuestionAllSubject(id) {
      let that = this;
      const params = { id, isWx: 1 };
      this.axios.postContralHostBase("questionPvqQuestionDetail", params, res => {
        if (res.code == 200) {
          const questionList = res.data.questions;
          this.maxQuestionList = res.data.questions.maxSelect; //最多可选择的选项
          this.minQuestionList = res.data.questions.minSelect; //最少可选择的选项
          const previewData = [];
          let numIndex = 0;
          this.forceSign = res.data.forceSign;
          //根据是否 已答判断 签名
          if (res.data.answerId != "-1") {
            this.signatureImg = res.data.signature;
          } else {
            this.signatureImg = res.data.signaturePicUrl || res.data.signature;
            this.signatureSrc = res.data.signaturePic;
          }
          for (let index = 0; index < questionList.length; index++) {
            const item = questionList[index];
            if (item.type === "paragraph") {
              previewData.push({ ...item, numIndex: null });
            } else {
              numIndex++;
              previewData.push({ ...item, numIndex });
            }
          }
          this.questionPreviewList = { ...res.data, questions: previewData };
          // 问卷暂停 未答人员answerId=-1   问卷已暂停statusRun 0
          // answerId != -1  -1没答  不是-1就是答了
          // 问卷结束 未答人员answerId=-1   问卷已结束status=recovery
          const { answerId, statusRun, status } = this.questionPreviewList;
          if (answerId == -1 && statusRun == 0) return this.$toast.fail(`问卷已暂停`);
          if (answerId == -1 && status == "recovery") return this.$toast.fail(`问卷已结束`);
          if (res.data.type == "2") {
            this.remainingTime = Number(res.data.limitTime * 60 * 1000); //分钟转毫秒
            console.log(this.remainingTime, "that.remainingTime");
            that.scoreTotle = Number(res.data.score); //总分
            if (res.data.answerId == "-1") {
              this.$nextTick(() => {
                this.$refs.countDown.start();
              });
            }
          }
          this.getAnswerPercentageStep();
        } else {
          this.$message.error(res.msg);
        }
      });

      return;
      this.centralControl.axios.centralControl(
        "getCurrentQuestionAllSubject",
        data => {
          // alert("data.code"+data.code);
          // alert("data.message"+data.message);
          if (data.code == 200) {
            const questionList = data.data.questions;
            this.maxQuestionList = data.data.questions.maxSelect; //最多可选择的选项
            this.minQuestionList = data.data.questions.minSelect; //最少可选择的选项
            const previewData = [];
            let numIndex = 0;
            this.forceSign = data.data.forceSign;
            this.signatureImg = data.data.signature;
            for (let index = 0; index < questionList.length; index++) {
              const item = questionList[index];
              if (item.type === "paragraph") {
                previewData.push({ ...item, numIndex: null });
              } else {
                numIndex++;
                previewData.push({ ...item, numIndex });
              }
            }
            this.questionPreviewList = { ...data.data, questions: previewData };
            // 问卷暂停 未答人员answerId=-1   问卷已暂停statusRun 0
            // answerId != -1  -1没答  不是-1就是答了
            // 问卷结束 未答人员answerId=-1   问卷已结束status=recovery
            const { answerId, statusRun, status } = this.questionPreviewList;
            if (answerId == -1 && statusRun == 0) return toast(this, `问卷已暂停`, "error");
            if (answerId == -1 && status == "recovery") return toast(this, `问卷已结束`, "error");
            this.getAnswerPercentageStep();
          } else {
            this.$message.error(data.message);
          }
        },
        params
      );
    },
    //提交答题结果
    submitQuestinAnswer() {
      const childList = this.$refs.child;
      let answerRecords = [];
      let isBreak = [];
      for (let index = 0; index < childList.length; index++) {
        const item = childList[index];
        // 调用子组件的doValided方法进行题目验证，子组件必须有doValided和checkValided方法，详见子组件
        const isValided = item.doValided();
        if (item.$vnode.tag.indexOf("Paragraph") < 0) {
          if (isValided) {
            isBreak.push(index);
          } else {
            answerRecords = answerRecords.concat(item.answerVal);
          }
        }
      }
      //校验校验多选项答题规范
      let queList = this.questionPreviewList.questions;
      this.flags = false;
      if (queList && queList.length) {
        queList.every((item, index) => {
          if (item.lengthArr && item.lengthArr.length) {
            if ((item.maxSelect !== item.minSelect && item.lengthArr.length > item.maxSelect) || item.lengthArr.length < item.minSelect) {
              console.log("不规范呀呀呀");
              this.isCheck = true;
              this.$set(item, "checksed", true);
              this.flags = true;
              // return true;
            } else {
              this.isCheck = false;
              this.$set(item, "checksed", false);
              console.log("规范");
              this.flags = false;
              return true;
            }
          }
        });
        // this.flags = listfalseNum.length>0?true:false
        // console.log(listfalseNum);
      }
      if (isBreak.length === 0) {
        //当前问卷题目均为非必答题时，阻止未做答任何题目点击提交
        if (answerRecords.length === 0) {
          this.$message.error("您未作答！请您作答任意一个题均可");
          return;
        }
        if (this.flags) return this.$toast.fail(`题目作答不符合答题规范！`); //多选题作答不符合规范提示

        const loginInfo = localStorage.getItem("loginInfo");
        const currentLoginInfo = loginInfo ? JSON.parse(loginInfo) : { userName: "ihbs_15809216556" };
        const params = {
          pvqId: this.questionId, //当前问卷ID
          openTime: this.openTime, //问卷开始做答的时间
          subTime: moment(utils.obtainEast8Date()).format("YYYY-MM-DD HH:mm:ss"), //问卷做答结束的时间
          subName: currentLoginInfo.userName, //当前答卷人
          deviceType: "phone",
          // userId: "", //当前答卷人的ID
          answerRecords: JSON.stringify(answerRecords)
        };
        if (this.signatureImg) params.signature = this.signatureSrc || this.signatureImg;
        if (this.forceSign == 1 && !this.signatureImg) return this.$toast.fail(`请先签名`);
        this.currentParams = params;
        this.saveApi();
      }
    },
    //不校验直接提交
    autoSubmitQuestin() {
      const childList = this.$refs.child;
      let answerRecords = [];
      for (let index = 0; index < childList.length; index++) {
        const item = childList[index];
        // 调用子组件的doValided方法进行题目验证，子组件必须有doValided和checkValided方法，详见子组件
        answerRecords = answerRecords.concat(item.answerVal);
      }
      const loginInfo = localStorage.getItem("loginInfo");
      const currentLoginInfo = loginInfo ? JSON.parse(loginInfo) : { userName: "ihbs_15809216556" };
      const params = {
        pvqId: this.questionId, //当前问卷ID
        openTime: this.openTime, //问卷开始做答的时间
        subTime: moment(utils.obtainEast8Date()).format("YYYY-MM-DD HH:mm:ss"), //问卷做答结束的时间
        subName: currentLoginInfo.userName, //当前答卷人
        deviceType: "phone",
        // userId: "", //当前答卷人的ID
        answerRecords: JSON.stringify(answerRecords)
      };
      if (this.signatureImg) params.signature = this.signatureSrc || this.signatureImg;
      this.currentParams = params;
      this.saveApi();
    },
    // 问卷保存接口 signatureImg
    saveApi() {
      let params = this.currentParams;

      this.axios.postContralHostBase("saveQuestionAnswer", params, res => {
        this.test = false;
        if (res.code == 200) {
          // this.showQuestionEndText();
          this.showAlert();
        } else {
          this.$router.go(-1);
          this.goBack = true; //报错可以退出
          this.$message.error(res.message);
        }
      });

      // saveQuestionAnswer(params).then(data=> {
      //   this.test = false;
      //     if (data.code == 200) {
      //       this.showQuestionEndText();
      //     } else {
      //       this.goBack = true//报错可以退出
      //       this.$message.error(data.msg);
      //     }
      // })
      return;
      this.centralControl.axios.centralControl(
        "saveQuestionAnswer",
        data => {
          this.test = false;
          if (data.code == 200) {
            this.showQuestionEndText();
          } else {
            this.$message.error(data.message);
          }
        },
        params
      );
    },
    showAlert() {
      const _this = this;
      this.$dialog
        .alert({
          type: "alert",
          title: "答题成功"
        })
        .then(() => {
          this.$router.go(-1);
        });

      // this.$dialog(
      //   {
      //     type: "alert",
      //     title: "答题成功",
      //     confirm: (e, promptValue) => {
      //       _this.$router.go(-1);
      //     },
      //   },
      //   (createElement) => {
      //     return [
      //       createElement(
      //         "div",
      //         {
      //           class: {
      //             "endText-title": true,
      //           },
      //           slot: "title",
      //         },
      //         [createElement("p", _this.questionPreviewList.name)]
      //       ),
      //     ];
      //   }
      // ).show();
    },
    //答题完毕时的弹出框，用户显示后置说明
    showQuestionEndText() {
      const _this = this;
      //没有后置说明时弹框
      if (!_this.questionPreviewList.endText || _this.questionPreviewList.endText.length === 0) {
        _this.showAlert();
        return;
      }
      // this.$dialog(
      //   {
      //     type: "alert",
      //     confirm: (e, promptValue) => {
      //       _this.$router.go(-1);
      //     },
      //   },
      //   (createElement) => {
      //     return [
      //       createElement(
      //         "div",
      //         {
      //           class: {
      //             "endText-title": true,
      //           },
      //           slot: "title",
      //         },
      //         [createElement("p", _this.questionPreviewList.name)]
      //       ),
      //       createElement(
      //         "div",
      //         {
      //           class: {
      //             "endText-content": true,
      //           },
      //           slot: "content",
      //         },
      //         _this.questionPreviewList.endText
      //       ),
      //     ];
      //   }
      // ).show();
    },
    showPopup() {
      this.test = true;
    },
    closePop() {
      this.test = false;
    },
    complete(data) {
      if (!data) return this.$message.error("保存签名失败");
      this.closePop();
      // let file = utils.dataURLtoFile(data,'签名.png')
      let params = {
        file: data
      };
      let formData = new FormData();
      formData.append("file", data);
      formData.append("hospitalCode", JSON.parse(localStorage.getItem("loginInfo")).hospitalCode);
      formData.append("unitCode", JSON.parse(localStorage.getItem("loginInfo")).unitCode);
      axios.post(__PATH.BASEURL + "file/upload", formData).then(res => {
        console.log(res, "resssss");
        if (res.data.code == 200) {
          this.signatureImg = res.data.data.temporaryUrl;
          this.signatureSrc = res.data.data.fileKey;
          this.$toast.success(`签名成功`);
        }
      });

      //   this.axios.postContralHostUrlOne(__PATH.BASEURL +"uploadImg", params, (res) => {
      // const { code, data, message } = res;
      // if (code == 200) {

      //   this.signatureImg=data.temporaryUrl;
      //   this.signatureSrc=data.fileKey;
      //   this.$toast.success(`签名成功`)
      // }else {
      //   this.$toast.fail(message)
      // }

      // });
      // uploadImg({file: data}).then(res=> {
      //   if(res.code == '200') {
      //     this.signatureImg=res.data.temporaryUrl;
      //     this.signatureSrc=res.data.fileKey;
      //     this.$toast.success(`签名成功`)
      //   }else {
      //     this.$toast.fail(res.message)
      //   }
      // })
    },
    //在线考试 查看答题解析
    answerDetail() {
      this.$router.push({ path: "/examCompleteDetail" });
    }
  }
};
</script>

<style scoped lang="scss">
.return-text {
  display: inline-block;
  // vertical-align: text-top;
  height: 20px;
  font-size: 14px;
  font-family: PingFang-SC-Medium, PingFang-SC;
  font-weight: 500;
  // color: $text-black;
  line-height: 7px;
}

.top-box {
  display: flex;
  background: #fff;
  height: 44px;
  line-height: 44px;
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 99;
  box-shadow: 0 2px 3px #d4daeb;

  i {
    font-size: 16px;
    vertical-align: initial;
  }

  .back-box {
    // margin: auto 0;
    // padding-left: 10px;
    //     -webkit-box-align: center;
    // -webkit-align-items: center;
    align-items: center;
    padding: 0 16px;
    font-size: 0.373333rem;
    cursor: pointer;
  }

  .van-dropdown-menu {
    flex: 1;
  }
}
.titleData {
  position: fixed;
  display: flex;
  justify-content: space-between;
  width: 100%;
  top: 60px;
  padding: 2px 15px;
  background: rgb(237, 246, 255);
  font-size: 14px;
  z-index: 9;
  border-bottom: 1px solid #eee;
  color: rgb(55, 157, 255);
  box-sizing: border-box;
}
.buttons {
  background-color: #cccccc !important;
  border-color: #cccccc !important;
}
.ques-content-box {
  bottom: 50px;
  background: #fff;

  .resign-txt {
    font-style: normal;
    margin: auto 0;
  }

  .resign {
    flex: 1;
    text-align: center;
    display: flex;
  }

  .sign-img {
    width: 60px;
    height: 60px;
    margin: auto;
    transform: rotate(90deg);
  }
  .my_flex {
    display: flex;
    align-items: center;
    background: #fff;
    padding: 16px;
    justify-content: space-between;

    .sign_title {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 500;
      color: rgba(53, 53, 53, 1);
    }
  }
  .signature_edit {
    width: 252px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid rgba(41, 190, 188, 1);

    .no_singature {
      display: inline-block;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(41, 190, 188, 1);
      line-height: 22px;
    }

    img {
      width: 16px;
      margin-right: 4px;
    }
  }
}

.my_signature {
  background: #fff;
  padding: 0px 20px;
  text-align: center;

  img {
    // padding:20px;
    width: 100px;
    transform: rotate(90deg);
  }
}

// 后置说明的标题
.endText-title {
  padding: 10px 20px;
  font-size: 18px;
  text-align: justify;
  text-justify: unset;
  line-height: normal;
}

.endText-content {
  padding: 0 20px;
  font-size: 14px;
  text-align: justify;
  text-justify: unset;
  line-height: normal;
  height: 180px;
  overflow-y: auto;
}

.main-container-progress {
  padding-top: 20px !important;
}

.main-container-Progress-exam {
  padding-top: 89px !important;
}
.main-content {
  padding-top: 45px;
}
.main-content-exam {
  padding-top: 20px;
}
.main-container {
  background-color: #fff;
  overflow-y: auto;
  .progress {
    position: fixed;
    width: 100%;
    z-index: 2;
    // top: 45px;
  }
  .question-content {
    background-color: #f0f0f4;
    margin-bottom: 50px;
  }
  .question-content-exam {
    margin-bottom: 50px;
    background-color: #f0f0f4;
  }
  .preview-container {
    background-color: #fff;
    border-bottom: 1px solid #d8dee7;
    margin: 1px 0px;
    padding: 0 10px;
  }

  .question-preText {
    // padding: 20px 0;
    border-bottom: 1px solid #f0f0f4;
    text-align: justify;
    text-justify: unset;
    padding: 10px;
    line-height: normal;
  }

  .question-endText {
    padding-bottom: 20px;
    text-align: center;
  }
}

.submit-container {
  // padding: 10px;
  height: 50px;
  position: fixed;
  bottom: 0px;
  width: 100%;
  text-align: center;
  align-items: center;
  justify-content: center;
  display: flex;
  background: #fff;
  z-index: 1;
  .el-button--primary {
    color: #fff;
    background-color: #2cc7c5;
    border-color: #2cc7c5;
  }

  .el-button--primary.is-active,
  .el-button--primary:active {
    background: #2cc7c5;
    border-color: #2cc7c5;
    color: #fff;
  }

  .el-button--primary:focus,
  .el-button--primary:hover {
    background: #2cc7c5;
    border-color: #2cc7c5;
    color: #fff;
  }

  .el-button--primary.is-disabled,
  .el-button--primary.is-disabled:active,
  .el-button--primary.is-disabled:focus,
  .el-button--primary.is-disabled:hover {
    background-color: #69cecc;
    border-color: #69cecc;
  }
}

.el-progress-bar__outer,
.el-progress-bar__inner {
  border-radius: 0px !important;
}
.container {
  width: 100vw;
  background-color: #fff;
  padding-bottom: 5vh;
}
</style>
