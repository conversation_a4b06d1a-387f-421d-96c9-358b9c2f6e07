import router from  '@/router/index'
import UAParser from "ua-parser-js";
import Vue from 'vue';
const uaParer = new UAParser();
class Utils {
  validateMobile(val){
    if(!Boolean(val)){
      return true
    } else {
      const reg=/(^$)|^[1][3,4,5,6,7,8,9][0-9]{9}$/; 
      return reg.test(val)
    }
  }
  getUniqueName(){
    return new Date().valueOf();
  }
  idHide(val){
    return val&&val.replace(/(.{4}).*(.{4})/, '$1********$2')　
  }
  // 是否是微信端。
  isWechat() {
    return uaParer.getBrowser().name === "WeChat";
  }
  // 是否是微信端。
  isIOS() {
    return uaParer.getOS().name === "iOS";
  }
  // 安卓
  isAndroid(){
    return uaParer.getOS().name === 'Android'
  }
  labelCodeDecode(str){
    let strNew=''
    try{
      strNew= window.atob(str)
    }catch(e){
    }
    return strNew;
  }
  // 获取微信位置
  getLocationFn(){
    return new Promise((resolve,reject)=>{
      wx.getLocation({
        type: 'wgs84', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
        success: function (res) {
          console.log(res)
          let latitude = res.latitude; // 纬度，浮点数，范围为90 ~ -90
          let longitude = res.longitude; // 经度，浮点数，范围为180 ~ -180。
          let location=latitude+','+longitude;
          let key = 'NRHBZ-5N6C5-LGVIW-QNDW5-JDX3O-F2BML'
          let url = `https://apis.map.qq.com/ws/geocoder/v1/`
          let data = {
            location,
            key,
            output:'jsonp'
          }
          Vue.prototype.$jsonp(url, data)
          .then(result => {
            resolve(result)
          })
          .catch(error => {
            reject(error)
          });
        },
        fail(error){
          reject(error)
        },
      });
    })
  }
  // 扫码后的跳转
  scanJump(res,item){
    const {status,result,message}=res;
    let resultNew = result?result:{}; 
    const {bindTime='',location='',createTime='', locationAddress="", labelCode=''}=resultNew;
    const typeObj={
      1:'1',
      0:'3'
    }
    const {firstType,bindType} = item;//初始绑定状态
    let urlType=(firstType==1&&bindType==1)?1:0;//1扫码 0绑定
    let query={};
    if(status==200){
        // 绑定 bindTime labelCode location
        // 扫码 createTime labelCode locationAddress

        //扫码成功-绑定成功
        query = {
            type:typeObj[urlType],//3绑定成功  1扫码成功
            location:location||locationAddress,
            createTime:createTime||bindTime,
            labelCode
        }
    } else {
        if(status=='10004'){//无效
          query={type:2};
        }else if(status=='10005'){//绑定失败 --已被绑定
          query = {
            type:4,
            location:location,
            createTime:bindTime,
            labelCode
          }
        } else if(status==0){
          query = {
            type:urlType==1?7:5,//5绑定失败 7扫码失败  网络   
          }
        }else {
          query = {
            type:urlType==1?7:5,//5绑定失败 7扫码失败  网络   
          }
        }
    }
    if(status!=10011&&status!=10026&&status!=10046){//扫码绑定>10个时不跳转。  未绑定时不跳转  10046, "此标签不属于当前备件,扫码失败"
      router.push({
        path:'/scanResult',
        query
      })
    }
  }
  uniqueArr(arr){
    console.log(arr,arr.length)
    var arr1 = [];       // 新建一个数组来存放arr中的值
    for(var i=0,len=arr.length;i<len;i++){
        if(arr1.indexOf(arr[i]) === -1){
            arr1.push(arr[i]);
        }
    }
    return arr1;
  }
  // 获取东八时区
  obtainEast8Date() {
    var timezone = 8; //目标时区时间，东八区   东时区正数 西市区负数
    var offset_GMT = new Date().getTimezoneOffset(); // 本地时间和格林威治的时间差，单位为分钟
    var nowDate = new Date().getTime(); // 本地时间距 1970 年 1 月 1 日午夜（GMT 时间）之间的毫秒数
    var targetDate = new Date(
      nowDate + offset_GMT * 60 * 1000 + timezone * 60 * 60 * 1000
    );
    return targetDate;
  }
  //base64 - File
  dataURLtoFile(dataurl, filename) {
    var arr = dataurl.split(','), mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]), n = bstr.length, u8arr = new Uint8Array(n);
    while(n--){
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, {type:mime})
  }
}
let utils = new Utils();
export default utils;
