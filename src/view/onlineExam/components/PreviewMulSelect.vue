<template>
  <div class="mulselect-container">
    <div :class="[isValided? 'error-border' : 'common-container']">
      <div class="quesName">
        <span v-show="isQuestionNum">{{index}}.</span>
        <span class="question-name-text">{{mulSelectOption.name}}</span>
        <span class="starRed" v-if="mulSelectOption.isMust == 1">*</span>
      </div>
      <div class="option-container">
        <div @click="showCascadePicker" :class="['multi-select',isAnswered? 'multi-select_disabled' : '']">
          <span :class="[isAnswer ? 'multi-select-placeholder':'multi-select-container']">{{selectedValue}}</span>
          <i class="multi-select-icon"></i>
        </div>
        <!-- <div class="block">
          <el-cascader style="width:100%;"
            v-model="selectvalue"
            :options="mulSelectOption.optionsData"
          >
          </el-cascader>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
	import notice from "../notice/notice.js"
  export default {
    props: {
      //当前题目的所有相关信息
      previewOption: {
        type: Object
      },
      //该题目在当前问卷中的序号
      index: {
        type: Number
      },
        //该题目是否已做答，true为已答过，false为未答
      isAnswered: {
        type: Boolean
      },
      //是否显示题目序号，true为显示，false为不显示
      isQuestionNum: {
        type: Boolean
      }
    },
    created() {
      this.previewOption.optionsData.forEach((item, index) => {
        const childOption = JSON.parse(item.childOptionArr);
        childOption.forEach((option) => {
          option.value = option.key;
          option.text = option.name
        });
        item.value = item.id;
        item.text = item.name;
        item.children = childOption
      });
      this.mulSelectOption = this.previewOption;
      if(this.previewOption.answers && this.previewOption.answers.length > 0){
        let selectedValue = "";
        this.previewOption.answers.forEach(item => {
          if(item.opetionLevel == 1){
            this.selectedValue = item.optionName+"/";
          }
          if(item.opetionLevel == 2){
            this.selectedValue = this.selectedValue + item.optionName;
          }
        });
      }
    },
    mounted() {
      this.cascadePicker = this.$createCascadePicker({
        // title: 'Cascade Picker',
        data: this.previewOption.optionsData,
        selectedIndex: [0, 0],
        onSelect: this.handleSelect,
        // onCancel: this.handleCancel
      })
    },
    data(){
      return{
        isAnswer: false, //标志当前题目是否已做答
        isValided: false,
        answerVal: [],
        selectedValue: "请选择",
        mulSelectOption: {},
        mulSelectOption1: {
          name:"①二级下拉菜单",
          optionsData: [
            {
            value: 'ziyuan',
            label: '资源',
            children: [{
                value: 'axure0',
                label: 'Axure Components0'
                }, {
                value: 'sketch0',
                label: 'Sketch Templates0'
                }, {
                value: 'jiaohu0',
                label: '组件交互文档0'
              }]
          },
          {
            value: 'zujian',
            label: '组件',
            children: [{
                value: 'axure1',
                label: 'Axure Components1'
                }, {
                value: 'sketch1',
                label: 'Sketch Templates1'
                }, {
                value: 'jiaohu1',
                label: '组件交互文档1'
              }]
          },
          {
            value: 'yuanxing',
            label: '原型',
            children: [{
                value: 'axure2',
                label: 'Axure Components2'
                }, {
                value: 'sketch2',
                label: 'Sketch Templates2'
                }, {
                value: 'jiaohu2',
                label: '组件交互文档2'
              }]
          }],
          isMust:1 //是否必填，必填时显示红色*
        },
      }
    },
    methods:{
      showCascadePicker() {
        if(!this.isAnswered){
          this.cascadePicker.show()
        }
      },
      handleSelect(selectedVal, selectedIndex, selectedText) {
        this.selectedValue = selectedText.join("/");
        this.answerVal = selectedVal.map((item,index) => {
          return {
            pvqId:localStorage.getItem('currentQuestionId'), //问卷id
            questionId: this.mulSelectOption.id, //题目id
            questionType:this.mulSelectOption.type, //题目类型
            optionId: selectedVal[0],
            optionName: selectedText[index],
            opetionLevel: index + 1,
          }
        })
        this.isValided = false;
        this.isAnswer = true;
        this.mulSelectOption.isMust == 1 && this.$parent.setAnswerPercentage(this.mulSelectOption.id,this.isAnswer);
        	 //观察者触发
        notice.emitBrother("questionnaireObserver");
      },
      handleCancel() {
        this.selectedValue = "请选择";
        this.isAnswer = false;
        this.mulSelectOption.isMust == 1 && this.$parent.setAnswerPercentage(this.mulSelectOption.id,this.isAnswer);
      },
      checkValided(){
        if(this.mulSelectOption.isMust == 0){
          return false;
        }
        //判断当前题目是否必填，必填时才需要判断是否已答
        if(this.mulSelectOption.isMust != 1){
          return true;
        }
        //判断当前题目是否已答
        if(!this.isAnswer){
          return true;
        }
        return false;
      },
      	doValidedFlag(){
      return this.checkValided();
    },
      doValided() {
        this.isValided = this.checkValided();
        return this.isValided;
      }
    }
  }
</script>

<style scoped  lang="scss">
.mulselect-container {
  padding: 10px;

  .quesName {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 4px;
    word-wrap: break-word;
  }

  .question-name-text{
    font-weight: 600;
    font-size: 16px;
    line-height : 20px;
    color : #353535;
  }

  .starRed{
    color: red;
    font-weight: 600;
  }
  .common-container {
    border: 1px solid transparent;
    padding: 9px;
  }
  .error-border {
    border: 1px solid red;
    padding: 9px;
  }
  .option-container {
    padding: 10px 0;
  }
  .multi-select {
    box-sizing: border-box;
    padding: 0.266667rem 0.533333rem 0.266667rem 0.266667rem;
    border-radius: 0.053333rem;
    font-size: 0.373333rem;
    line-height: 1.429;
    color: #000;
    background-color: #fff;
    position: relative;
    width : 100%;
    padding :10px 20px 10px 10px;
  }
  .multi-select::after {
    content: "";
    pointer-events: none;
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    border: 1px solid rgb(229, 229, 229);;
    border-radius: 0.053333rem;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
  }
  .multi-select > span {
    display: inline-block;
  }
  .multi-select-container {
    color: #ccc;
    font-size: 14px;
  }
  .multi-select-placeholder {
    color: #606266;
    font-size: 14px;
  }
  .multi-select-icon {
    position: absolute;
    right: 8px;
    top: 50%;
    border-width: 4px 4px 0!important;
    -webkit-transform: translate(0, -50%);
    transform: translate(0, -50%);
    border-style: solid;
    border-color: #999 transparent transparent transparent;
    border-width: 0.106667rem 0.106667rem 0 0.106667rem;
    -webkit-transition: -webkit-transform 0.3s ease-in-out;
    transition: -webkit-transform 0.3s ease-in-out;
    transition: transform 0.3s ease-in-out;
    transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
  }
  .multi-select_disabled {
    color: #606266;
    background-color: rgba(0,0,0,0.04);
    cursor: not-allowed;
  }
}
</style>
