<template>
  <div class="matrix-container">
    <div :class="[isValided? 'error-border' : 'common-container']">
      <div class="quesName">
        <span v-show="isQuestionNum">{{index}}.</span>
        <span class="question-name-text">{{MatrixQuestionOption.name}}</span>
        <span
          class="starRed"
          v-if="MatrixQuestionOption.isMust == 1"
        >*</span>
        <span class="chexMinMax">
          <span v-if="MatrixQuestionOption.minValue">最小值{{Number(MatrixQuestionOption.minValue)}}</span>
          <span v-if="MatrixQuestionOption.maxValue">最大值{{Number(MatrixQuestionOption.maxValue)}}</span>
        </span>
      </div>
      <div class="questionContainer">
        <el-table :data="MatrixQuestionOption.rowType" style="width: 100%">
          <el-table-column>
            <template slot-scope="scope">{{scope.row.name}}</template>
          </el-table-column>
          <el-table-column
            :key="opt.id"
            v-for="opt in MatrixQuestionOption.columnType"
            :label="opt.name"
          >
            <template slot-scope="scope">
              <el-form :model="opt.rowType[scope.$index]" :rules="rules" ref="opt">
                <el-form-item prop="value">
                  <!-- <span>{{opt.rowType[scope.$index]}}</span> -->
                  <el-input
                    v-model="opt.rowType[scope.$index].value"
                    v-if="MatrixQuestionOption.inputType === 'number'"
                    @mousewheel.native.prevent
                    :disabled="isAnswered"
                    @input="handleInputChange(opt,scope.$index)"
                  ></el-input>

                <!-- <el-input-number
                  class="inputNumber"
                  controls-position="right"
                  :min="Number(MatrixQuestionOption.minValue)"
                  :max="Number(MatrixQuestionOption.maxValue)"
                  v-model="opt.rowType[scope.$index].value"
                  v-if="MatrixQuestionOption.inputType === 'number'"
                  type="number"
                  @mousewheel.native.prevent
                  :disabled="isAnswered"
                  @input="handleInputChange(opt,scope.$index)"
                ></el-input-number> -->
                  <el-input
                    v-model="opt.rowType[scope.$index].value"
                    v-if="MatrixQuestionOption.inputType === 'row'"
                    @input="handleInputChange(opt,scope.$index)"
                    :disabled="isAnswered"
                  ></el-input>
                </el-form-item>
              </el-form>
              <el-checkbox
                v-model="opt.checkedValue"
                v-if="MatrixQuestionOption.inputType === 'checkbox'"
                :label="scope.row.id"
                @change="handleCheckboxChange(scope,opt)"
                :disabled="isAnswered"
              >
                <span></span>
              </el-checkbox>
              <el-radio
                v-model="opt.value"
                @change="handleRadioChange(scope,opt)"
                v-if="MatrixQuestionOption.inputType === 'radio'"
                :label="scope.row.id"
                :disabled="isAnswered"
              >
                <span></span>
              </el-radio>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import utils from "./utils";
import notice from "../notice/notice.js"
export default {
  props: {
    //当前题目的所有相关信息
    previewOption: {
      type: Object
    },
    //该题目在当前问卷中的序号
    index: {
      type: Number
    },
      //该题目是否已做答，true为已答过，false为未答
    isAnswered: {
      type: Boolean
    },
    //是否显示题目序号，true为显示，false为不显示
    isQuestionNum: {
      type: Boolean
    }
  },
  created() {
    this.initData();
  },
  data() {
    //表单自定义校验规则
    let validedFormItem = (rule, val, callback) => {
      let value = Number(val)
      let regOption = /^(\-|\+)?\d+(\.\d+)?$/
      if (val === "") {
        if (this.MatrixQuestionOption.isMust == 1) {
          return callback(new Error('内容不能为空'));
        } else {
          callback();
        }
      } else {
        //文本
        if(this.MatrixQuestionOption.inputType == 'row') {
          if(val.length > 200) {
            callback(new Error("长度小于200"));
          }else {
            callback();
          }
        }else {
          //number 数值
          if (!regOption.test(value)) {
            callback(new Error('请输入正确的数字格式'));
          }else if (value > Number(this.MatrixQuestionOption.maxValue)) {
            callback(new Error("超过最大值"));
          }else if (value < Number(this.MatrixQuestionOption.minValue)) {
            callback(new Error("小于最小值"));
          }else {
            callback();
          }
        }
      }
    };
    return {
      rules: {
        value: [{ validator: validedFormItem, trigger: 'blur' }]
      },
      validedFormItem: validedFormItem,
      isAnswer: false, //标志当前题目是否已做答
      isValided: false,
      answerVal: [],
      radioChecked: {}, //在类型为radio时保存选中的radio选项
      checkoxChecked: {},//在类型为checkbox时保存选中的checkbox选项
      inputValue: {},
      MatrixQuestionOption: {},
      MatrixQuestionOption1: {
        name: "请对喜欢的水果评价",
        rowType: ["不喜欢", "一般", "喜欢", "非常喜欢"],
        inputType: "row",
        columnType: [
          {
            id: utils.guid(),
            name: "苹果",
            value: "", //radio的value，值为rowType中的某个
            checkedValue: ["不喜欢","喜欢"],//checkbox的value，值为rowType中的值
            rowType: [
              {
                name: "不喜欢",
                value: "hkjhj"
              },
              {
                name: "一般",
                value: ""
              },
              {
                name: "喜欢",
                value: ""
              },
              {
                name: "非常喜欢",
                value: ""
              }
            ]
          },
          {
            id: utils.guid(),
            name: "香蕉",
            value: "不喜欢",
            checkedValue: ["喜欢", "非常喜欢"],
            rowType: [
              {
                name: "不喜欢",
                value: ""
              },
              {
                name: "一般",
                value: ""
              },
              {
                name: "喜欢",
                value: ""
              },
              {
                name: "非常喜欢",
                value: ""
              }
            ]
          },
          {
            id: utils.guid(),
            name: "芒果",
            value: "",
            checkedValue: ["不喜欢", "喜欢"],
            rowType: [
              {
                name: "不喜欢",
                value: ""
              },
              {
                name: "一般",
                value: ""
              },
              {
                name: "喜欢",
                value: ""
              },
              {
                name: "非常喜欢",
                value: ""
              }
            ]
          }
        ],
        isMust: 1
      }
    };
  },
  methods: {
    validateNum(rule, value, callback) {
      console.log(value);
        if (value > 100 || value < 1 ) {
          callback(new Error('请输入1-100之间的数字'));
        } else {
          callback();
        }
    },
    initData() {
      const columnOption = JSON.parse(this.previewOption.columnType);
      const rowOption = JSON.parse(this.previewOption.rowType);
      const columnType = columnOption.map(item => {
        const row = rowOption.map((rowItem, index) => {
          //当矩阵类型是input时，判断是否有答题记录，有则将input的答案textValue保存
          if (
            this.previewOption.inputType === "number" ||
            this.previewOption.inputType === "row"
          ) {
            //判断当前问卷是否有答案列表
            let obj = { name: rowItem, value: "" };
            const answers = this.previewOption.answers;
            if (this.previewOption.answers && this.previewOption.answers.length > 0) {
              this.previewOption.answers.forEach(answerItem => {
                if (
                  JSON.parse(answerItem.rowValue).id === rowItem.id &&
                  answerItem.columnValue === item
                ) {
                  obj = { name: rowItem.name, value: answerItem.textValue };
                }
              });
            }
            return obj;
          } else {
            //当矩阵类型是radio或checkbox时，则设置默认值
            return { name: rowItem, value: "" };
          }
        });
        const columnObj = this.previewOption.inputType === "radio" ? {} : [];
        if (this.previewOption.answers && this.previewOption.answers.length > 0) {
          this.previewOption.answers.forEach(answerItem => {
            if (answerItem.columnValue === item) {
              this.previewOption.inputType === "radio"
                ? (columnObj[item] = JSON.parse(answerItem.rowValue).id)
                : columnObj.push(JSON.parse(answerItem.rowValue).id);
            }
          });
        }
        return {
          id: utils.guid(),
          name: item,
          // value: "",
          // checkedValue: [],
          value:
            this.previewOption.inputType === "radio" &&
            this.previewOption.answers &&
            this.previewOption.answers.length > 0
              ? columnObj[item]
              : "", //保存radio的值
          checkedValue:
            this.previewOption.inputType === "checkbox" &&
            this.previewOption.answers &&
            this.previewOption.answers.length > 0
              ? columnObj
              : [],
          rowType: row
        };
      });
      this.MatrixQuestionOption = {
        ...this.previewOption,
        rowType: rowOption,
        columnType
      };
      const MatrixQuestionOption = this.MatrixQuestionOption;
    },
    handleRadioChange(scope,column) {
      this.radioChecked[column.id] = {
        rowValue: scope.row,
        columnValue: column.name
      };
      // console.log("radioChecked",this.radioChecked)
      this.answerVal = [];
      for (const key in this.radioChecked) {
        if (this.radioChecked.hasOwnProperty(key)) {
          const radioElement = this.radioChecked[key];
          this.answerVal = this.answerVal.concat({
            pvqId:localStorage.getItem('currentQuestionId'), //问卷id
            questionId:this.MatrixQuestionOption.id, //题目id
            questionType:this.MatrixQuestionOption.type, //题目类型
            ...radioElement
          });
        }
      }
      const radioCheckedArr = Object.keys(this.radioChecked);
      this.isAnswer = radioCheckedArr.length>0?true:false;
      // this.isAnswer = this.MatrixQuestionOption.columnType.length === radioCheckedArr.length;
      this.MatrixQuestionOption.isMust == 1 && this.$parent.setAnswerPercentage(this.MatrixQuestionOption.id,this.isAnswer);
      this.isValided = false;
       //观察者触发
        notice.emitBrother("questionnaireObserver");
    },
    handleCheckboxChange(scope,column) {
      this.checkoxChecked[column.id] = column.checkedValue.map((item) => {
        const row = column.rowType.filter((element) => {
          return element.name.id === item
        });
        return {
          rowValue: row[0].name,
          columnValue: column.name
        }
      });
      this.answerVal = [];
      for (const key in this.checkoxChecked) {
        let checkboxValueItem = [];
        if (this.checkoxChecked.hasOwnProperty(key)) {
          const checkboxElement = this.checkoxChecked[key];
          checkboxValueItem = checkboxElement.map(item => {
            return{
              pvqId:localStorage.getItem('currentQuestionId'), //问卷id
              questionId:this.MatrixQuestionOption.id, //题目id
              questionType:this.MatrixQuestionOption.type, //题目类型
              ...item
            };
          });
        }
        this.answerVal = this.answerVal.concat(checkboxValueItem);
      }
      //保存多选时有效值（将选中再取消时，对象里的空值过滤掉）
      let checkedRow = {};
      for (const key in this.checkoxChecked) {
        if (this.checkoxChecked.hasOwnProperty(key)) {
          const item = this.checkoxChecked[key];
          if(item.length !== 0){
            checkedRow[key] = item;
          }
        }
      }
      const checkoxCheckedArr = Object.keys(checkedRow);
      this.isAnswer = checkoxCheckedArr.length>0?true:false;
      // this.isAnswer = this.MatrixQuestionOption.columnType.length === checkoxCheckedArr.length;;
      this.MatrixQuestionOption.isMust == 1 && this.$parent.setAnswerPercentage(this.MatrixQuestionOption.id,this.isAnswer);
      this.isValided = false;
       //观察者触发
        notice.emitBrother("questionnaireObserver");
    },
    handleInputChange(column,rowIndex) {
      // console.log(column.rowType[rowIndex].value);
      const rowItems = column.rowType.filter((rowItem) => { return rowItem.value});
      this.inputValue[column.id] = rowItems.map((rowItem) => {
        return {
          rowValue: rowItem.name,
          columnValue: column.name,
          textValue: rowItem.value
        }
      });
      this.answerVal = [];
      for (const key in this.inputValue) {
        let inpputValueItem = [];
        if (this.inputValue.hasOwnProperty(key)) {
          const inutElement = this.inputValue[key];
          inpputValueItem = inutElement.map(item => {
            return{
              pvqId:localStorage.getItem('currentQuestionId'), //问卷id
              questionId:this.MatrixQuestionOption.id, //题目id
              questionType:this.MatrixQuestionOption.type, //题目类型
              ...item
            };
          });
        }
        this.answerVal = this.answerVal.concat(inpputValueItem);
      }
      let inputValueLength = 0; //矩阵中所有input的个数
      for (const key in this.inputValue) {
        if (this.inputValue.hasOwnProperty(key)) {
          const element = this.inputValue[key];
          inputValueLength += element.length;
        }
      }
      //当矩阵红input的个数等于行和列的乘积，即所有input框都有输入值，则标志位该题目已答
      this.isAnswer = inputValueLength === (this.MatrixQuestionOption.columnType.length * this.MatrixQuestionOption.rowType.length) ? true : false;
      this.MatrixQuestionOption.isMust == 1 && this.$parent.setAnswerPercentage(this.MatrixQuestionOption.id,this.isAnswer);
      this.isValided = false;
       //观察者触发
        notice.emitBrother("questionnaireObserver");
    },
    checkValided(flag = false){
      if(this.MatrixQuestionOption.isMust == 0){
        return false;
      }
      //判断当前题目是否必填，必填时才需要判断是否已答
      if(this.MatrixQuestionOption.isMust != 1){
        return true;
      }
      //判断当前题目是否已答
      if(!this.isAnswer){
        return true;
      }
      //当矩阵题目类型为文本或者数字时，判断矩阵中的输入框每项是否均已填写
      if(this.MatrixQuestionOption.inputType === 'row' || this.MatrixQuestionOption.inputType === 'number'){
        let inputValueLength = 0;
        for (const key in this.inputValue) {
          if (this.inputValue.hasOwnProperty(key)) {
            const element = this.inputValue[key];
            inputValueLength += element.length;
          }
        }
        if(inputValueLength < (this.MatrixQuestionOption.columnType.length * this.MatrixQuestionOption.rowType.length)){
          return true;
        }
      }
      if(this.MatrixQuestionOption.inputType === 'number'){
        let validResult = false;
        for (const key in this.inputValue) {
          if (!validResult && this.inputValue.hasOwnProperty(key)) {
            const element = this.inputValue[key];
            for (let index = 0; index < element.length; index++) {
              const item = element[index];
              if(parseInt(item.textValue) <  this.MatrixQuestionOption.minValue || parseInt(item.textValue) >  this.MatrixQuestionOption.maxValue){
                validResult = true;
                break;
              }
            }
          }
        }
        return validResult;
      }
      if(flag) {
        //当矩阵题目类型为单选类型时，判断矩阵中每列数据是否有选中（以列为分组）
        if(this.MatrixQuestionOption.inputType === 'radio'
            && Object.keys(this.radioChecked).length < this.MatrixQuestionOption.columnType.length){
          return true;
        }
        //当矩阵题目类型为多选类型时，判断矩阵中每列数据是否有选中（以列为分组）
        if(this.MatrixQuestionOption.inputType === 'checkbox'
            && Object.keys(this.checkoxChecked).length < this.MatrixQuestionOption.columnType.length){
          return true;
        }
      }
      return false;
    },
    	doValidedFlag(){
      return this.checkValided(false);
    },
    doValided() {
      this.isValided = this.checkValided(true);
      return this.isValided;
    }
  }
};
</script>

<style scoped  lang="scss">
.matrix-container{
  padding: 10px;

  .quesName {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 4px;
    word-wrap: break-word;
  }

  .question-name-text{
    font-weight: 600;
    font-size: 16px;
    line-height : 20px;
    color : #353535;
  }

  .starRed {
    color: red;
    font-weight: 600;
  }

  .chexMinMax{
    color: blue;
    font-size : 14px;
    margin-left: 3px;
  }

  .questionContainer {
    padding: 20px 0px 20px 0px;
    .inputNumber {
      width: 100%;
      .el-input-number__decrease, .el-input-number__increase {
        display: none
      }
      .el-input__inner {
        padding-right: 15px;
      }
    }
    .el-table__header-wrapper {
      overflow: visible;
    }
    .el-radio__input.is-checked .el-radio__inner{
      border-color: #2cc7c5;
      background: #2cc7c5;
    }
    //radio在disable时的样式
    .el-radio__input.is-disabled .el-radio__inner, .el-radio__input.is-disabled.is-checked .el-radio__inner{
      border-color: #f5f7fa;
      background: #f5f7fa;
    }
    .el-radio__input.is-checked+.el-radio__label {
      color: #2cc7c5;
    }
    .el-input__inner:focus{
      border:1px solid #2CC7C5;
      outline:none;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: #2cc7c5;
      border-color: #2cc7c5;
    }
    //chebox在disable时的样式
    .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
      border-color: #f5f7fa;
      background: #f5f7fa;
    }
    .cell {
      // width: 100px;
      padding-left: 0px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow:ellipsis
    }

  }
  .common-container {
    border: 1px solid transparent;
    padding: 9px;
  }
  .error-border {
    border: 1px solid red;
    padding: 9px;
  }

  div >>> .el-input__inner {
    padding: 0 0 0 10px;
  }

  .el-table th>.cell, .el-table .cell {
    white-space: nowrap;
  }
}
</style>
