<template>
  <div class="input-container">
    <div :class="[isValided? 'error-border' : 'common-container']">
      <div class="quesName">
        <span v-show="isQuestionNum">{{index}}.</span>
        <span class="question-name-text">{{inputOption.name}}</span>
        <span class="starRed" v-if="inputOption.isMust == 1">*</span>
        <span class="chexMinMax">
          <span v-if="inputOption.inputType === 'number' && inputOption.minValue">最小值{{Number(inputOption.minValue)}}</span>
          <span v-if="inputOption.inputType === 'number' && inputOption.maxValue">最大值{{Number(inputOption.maxValue)}}</span>
        </span>
      </div>
      <div class="questionContainer">
        <el-form :model="inputForm" ref="inputForm" :rules="rules">
          <el-form-item prop="id_input">
            <el-input
              type="textarea"
              :rows="4"
              auto-complete="off"
              @input="handleInputChange"
              v-model="inputForm.id_input"
              v-if="inputOption.inputType === 'rows'"
              :disabled="isAnswered"
            ></el-input>
            <div
              v-else-if="inputOption.inputType === 'date'"
              @click="showDatePicker"
              :class="['multi-select',isAnswered? 'date_disabled' : '']"
            >
              <i class="el-input__icon el-icon-date"></i>
              <span :class="[isAnswer ? 'date-placeholder':'date-container']" >{{dateValue}}</span>
            </div>
            <div
              v-else-if="inputOption.inputType === 'time'"
              @click="showTimePicker"
              :class="['multi-select',isAnswered? 'time_disabled' : '']"
            >
              <i class="el-input__icon el-icon-time"></i>
              <span :class="[isAnswer ? 'time-placeholder':'time-container']">{{dateValue}}</span>
            </div>
            <!-- <el-date-picker v-else-if="inputOption.inputType === 'date'" type="date" placeholder="选择日期" v-model="inputForm.id_input" :disabled="isAnswered"></el-date-picker>
            <el-date-picker v-else-if="inputOption.inputType === 'time'" type="datetime"  placeholder="选择时间" v-model="inputForm.id_input" :disabled="isAnswered"></el-date-picker>-->
            <el-input
              v-else-if="inputOption.inputType === 'number'"
              v-model="inputForm.id_input"
              @input="handleInputChange"
              :disabled="isAnswered"
              @mousewheel.native.prevent>
            ></el-input>
            <el-input
              v-else-if="inputOption.inputType === 'url'"
              v-model="inputForm.id_input"
              auto-complete="off"
              onkeyup="value=value.replace(/\s+/g,'')"
              @input="handleInputChange"
              :disabled="isAnswered"
            ></el-input>
            <el-input
              v-else
              v-model="inputForm.id_input"
              auto-complete="off"
              @input="handleInputChange"
              :disabled="isAnswered"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
	import notice from "../notice/notice.js"
export default {
  props: {
    //当前题目的所有相关信息
    previewOption: {
      type: Object
    },
    //该题目在当前问卷中的序号
    index: {
      type: Number
    },
      //该题目是否已做答，true为已答过，false为未答
    isAnswered: {
      type: Boolean
    },
    //是否显示题目序号，true为显示，false为不显示
    isQuestionNum: {
      type: Boolean
    }
  },
  created() {
    this.inputOption = this.previewOption;
    if (this.previewOption.answers && this.previewOption.answers.length > 0) {
      if (
        this.inputOption.inputType === "date" ||
        this.inputOption.inputType === "time"
      ) {
        this.dateValue = this.previewOption.answers[0].questionValue;
      } else {
        this.inputForm.id_input = this.previewOption.answers[0].questionValue;
      }
    }
  },
  data() {
    //表单自定义校验规则
    let validedFormItem = (rule, val, callback) => {
      let value = Number(val)
      const regOption = this.typeReg[this.inputOption.inputType];
      const type = this.inputOption.inputType;
      if (val === "" || val == undefined) {
        if (this.inputOption.isMust == 1) {
          return callback(new Error(regOption.requireMessage));
        } else {
          callback();
        }
      } else {
        if(this.inputOption.inputType === "row" || this.inputOption.inputType === "rows"){
          if (val.length > 200) {
            callback(new Error("长度小于200个字符"));
          }
        }else if(this.inputOption.inputType === "number" ){
          if (regOption.reg && !regOption.reg.test(value)) {
            callback(new Error(regOption.formatMessage));
          }else if (value > Number(this.inputOption.maxValue)) {
            callback(new Error("超过最大值"));
          }else if (value < Number(this.inputOption.minValue)) {
            callback(new Error("小于最小值"));
          }else {
            callback();
          }
        }else{
          if (regOption.reg && !regOption.reg.test(val)) {
            callback(new Error(regOption.formatMessage));
          }else{
            callback();
          }
        }
      }
    };
    return {
      inputOption: {},
      inputForm: {
        id_input: ""
      },
      answerVal: [],
      dateValue: "请选择", //日期组件显示日期的值
      isAnswer: false, //标志当前题目是否已做答
      isValided: false,
      //输入框的示例数据
      inputOption1: {
        name: "①填空题击一为ID哦iwejdooiadio",
        inputType: "phone", //row, rows, number,phone, email,incard,url,date,time
        isMust: 1 //是否必填，0表示否，1表示是
      },
      typeReg: {
        row: {
          reg: "",
          requireMessage: "请输入内容",
          formatMessage: ""
        },
        rows: {
          reg: "",
          requireMessage: "请输入内容",
          formatMessage: ""
        },
        number: {
          // reg: /^\d+$|^\d+[.]?\d+$/,
          reg: /^(\-|\+)?\d+(\.\d+)?$/,
          requireMessage: "数字内容不能为空",
          formatMessage: "请输入正确的数字格式"
        },
        phone: {
          reg: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
          requireMessage: "电话号码不能为空",
          formatMessage: "请输入正确的电话号码格式"
        },
        email: {
          reg: /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,4}$/,
          requireMessage: "邮箱地址不能为空",
          formatMessage: "请输入正确的邮箱地址格式"
        },
        incard: {
          reg: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)/,
          requireMessage: "身份证号码不能为空",
          formatMessage: "请输入正确的身份证号码格式"
        },
        url: {
          reg: /^http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/,
          // reg: /(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/,
          requireMessage: "URL地址不能为空",
          formatMessage: "请输入正确的URL地址格式"
        },
        date: {
          reg: "",
          requireMessage: "请选择日期",
          formatMessage: ""
        },
        time: {
          reg: "",
          requireMessage: "请选择日期",
          formatMessage: ""
        }
      },
      rules: {
        id_input: [{ validator: validedFormItem, trigger: "blur" }],
        dateValue: [{ validator: validedFormItem, trigger: "blur" }]
      }
    };
  },

  methods: {
    showDatePicker() {
      if(this.isAnswered){
        return;
      }
      if (!this.datePicker) {
        this.datePicker = this.$createDatePicker({
          // min: new Date(2008, 7, 8),
          max: new Date(2030, 12, 31),
          value: new Date(),
          onSelect: this.selectHandle,
          // onCancel: this.cancelHandle
        });
      }

      this.datePicker.show();
    },
    selectHandle(date, selectedVal, selectedText) {
      if (this.isAnswered) {
        return;
      }
      this.dateValue = selectedVal.join("/");
      this.answerVal = [
        {
          pvqId: localStorage.getItem("currentQuestionId"), //问卷id
          questionId: this.inputOption.id, //题目id
          questionType: this.inputOption.type, //题目类型
          questionValue: this.dateValue //输入的值
        }
      ];
      this.isValided = false;
      this.isAnswer = true;
      this.inputOption.isMust == 1 && this.$parent.setAnswerPercentage(this.inputOption.id, this.isAnswer);
       //观察者触发
        notice.emitBrother("questionnaireObserver");
      // this.$createDialog({
      //   type: 'warn',
      //   content: `Selected Item: <br/> - date: ${date} <br/> - value: ${selectedVal.join(', ')} <br/> - text: ${selectedText.join(' ')}`,
      //   icon: 'cubeic-alert'
      // }).show()
    },
    cancelHandle() {
      this.dateValue = "请选择";
      this.isAnswer = false;
      this.inputOption.isMust == 1 && this.$parent.setAnswerPercentage(this.inputOption.id, this.isAnswer);
       //观察者触发
        notice.emitBrother("questionnaireObserver");
      // this.$createToast({
      //   type: 'correct',
      //   txt: 'Picker canceled',
      //   time: 1000
      // }).show()
    },
    showTimePicker() {
      if (this.isAnswered) {
        return;
      }
      this.$createTimePicker({
        showNow: true,
        minuteStep: 5,
        delay: 15,
        day : {
          len: 10,
        },
        onSelect: (selectedTime, selectedText, formatedTime) => {
          if (this.isAnswered) {
            return;
          }
          // console.log(selectedTime, selectedText, formatedTime, this.dateValue);
          this.dateValue = formatedTime;
          this.answerVal = [
            {
              pvqId: localStorage.getItem("currentQuestionId"), //问卷id
              questionId: this.inputOption.id, //题目id
              questionType: this.inputOption.type, //题目类型
              questionValue: this.dateValue //输入的值
            }
          ];
          this.isValided = false;
          this.isAnswer = true;
          this.isAnswer &&
            this.inputOption.isMust == 1 &&
            this.$parent.setAnswerPercentage(
              this.inputOption.id,
              this.isAnswer
            );
       //观察者触发
        notice.emitBrother("questionnaireObserver");
        },
        // onCancel: this.cancelHandle
      }).show();
    },
    handleInputChange(val) {
      this.answerVal = [
        {
          pvqId: localStorage.getItem("currentQuestionId"), //问卷id
          questionId: this.inputOption.id, //题目id
          questionType: this.inputOption.type, //题目类型
          questionValue: val //输入的值
        }
      ];
      this.isAnswer = val ? true : false;
      this.inputOption.isMust == 1 && this.$parent.setAnswerPercentage(this.inputOption.id, this.isAnswer);
      this.isValided = false;
       //观察者触发
        notice.emitBrother("questionnaireObserver");
    },
    validedForm () {
      let validedResult = false;
      this.$refs['inputForm'].validate((result) => {
        if (result) {
          validedResult = false;
        } else {
          validedResult = true;
        }
      })
      return validedResult;
    },
    checkValided() {
      if(this.inputOption.isMust == 0){
        return false;
      }
      //判断当前题目是否必填，必填时才需要判断是否已答
      if (this.inputOption.isMust != 1) {
        return true;
      }
      //判断当前题目是否已答
      if (!this.isAnswer) {
        return true;
      }
      if (
        (this.inputOption.inputType === "date" ||
          this.inputOption.inputType === "time") &&
        this.dateValue.length === 0
      ) {
        return true;
      }
      //判断是否输入值
      // if (
      //   this.inputOption.inputType !== "date" &&
      //   this.inputOption.inputType !== "time" &&
      //   this.inputForm.id_input.length === 0
      // ) {
      //   return true;
      // }
      //当类型为number时判断输入的值是否满足设置的最大值和最小值
      if (
        this.inputOption.inputType === "number" && this.inputOption.maxValue !== this.inputOption.minValue &&
        (this.inputForm.id_input > this.inputOption.maxValue + 1 || this.inputForm.id_input < this.inputOption.minValue - 1)
      ) {
        return true;
      }
      if (this.inputOption.inputType !== "date" && this.inputOption.inputType !== "time"){
        return this.validedForm();
      }
      return false;
    },
    	doValidedFlag(){
      return this.checkValided();
    },
    doValided() {
      this.isValided = this.checkValided();
      return this.isValided;
    }
  }
};
</script>

<style scoped  lang="scss">
.input-container{
  padding: 10px;

  .quesName {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 4px;
    word-wrap: break-word;
  }

  .question-name-text{
    font-weight: 600;
    font-size: 16px;
    line-height : 20px;
    color : #353535;
  }

  .starRed {
    color: red;
    font-weight: 600;
  }
  .chexMinMax{
    color: blue;
    font-size : 14px;
    margin-left: 3px;
  }

  .questionContainer {
    padding: 20px 0px 0px 0px;

    .el-input.is-active .el-input__inner, .el-input__inner:focus {
      border-color: #2cc7c5;
      outline: 0;
    }

    .el-form-item.is-success .el-input__inner, .el-form-item.is-success .el-input__inner:focus, .el-form-item.is-success .el-textarea__inner, .el-form-item.is-success .el-textarea__inner:focus {
      // border-color: #67C23A;
      border-color: #2cc7c5;
    }

    .multi-select {
      box-sizing: border-box;
      padding: 0rem 0.266667rem;
      border-radius: 0.053333rem;
      font-size: 0.373333rem;
      line-height: 1.429;
      color: #000;
      background-color: #fff;
      position: relative;
      width: 100%;
    }

    .multi-select::after {
      content: '';
      pointer-events: none;
      display: block;
      position: absolute;
      left: 0;
      top: 0;
      -webkit-transform-origin: 0 0;
      transform-origin: 0 0;
      border: 1px solid rgb(229, 229, 229);
      border-radius: 0.053333rem;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
    }

    .multi-select > span {
      display: inline-block;
    }

    .multi-select-placeholder {
      color: #ccc;
    }
    .date-container, .time-container {
      color: #ccc;
      font-size: 14px;
    }
    .date-placeholder, .time-placeholder{
      color: #606266;
      font-size: 14px;
    }

    .multi-select-icon {
      position: absolute;
      right: 0.213333rem;
      top: 50%;
      -webkit-transform: translate(0, -50%);
      transform: translate(0, -50%);
      border-style: solid;
      border-color: #999 transparent transparent transparent;
      border-width: 0.106667rem 0.106667rem 0 0.106667rem;
      -webkit-transition: -webkit-transform 0.3s ease-in-out;
      transition: -webkit-transform 0.3s ease-in-out;
      transition: transform 0.3s ease-in-out;
      transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
    }
  }
  .common-container {
    border: 1px solid transparent;
    padding: 9px;
  }
  .error-border {
    border: 1px solid red;
    padding: 9px;
  }
  .date_disabled, .time_disabled {
    color: #606266;
    background-color: rgba(0,0,0,0.04) !important;
    cursor: not-allowed;
  }
  .time_disabled span {
    color: red;
  }
  .el-textarea__inner:focus {
    border-color : #2cc7c5;
  }
}
</style>
