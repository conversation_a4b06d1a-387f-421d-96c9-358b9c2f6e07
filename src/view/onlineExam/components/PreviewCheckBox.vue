<template>
  <div class="checkbox-container">
    <div :class="[isValided? 'error-border' : 'common-container']">
      <div class="quesName">
        <span v-show="isQuestionNum">{{index}}.</span>
        <span class="question-name-text">{{checkboxOption.name}}</span>
        <span class="starRed" v-if="checkboxOption.isMust == 1">*</span>
        <span class="chexMinMax" :style="{'color':isChecks&&checkboxOption.checksed?'#f56c6c':'blue'}">
          <span v-if="checkboxOption.minSelect && checkboxOption.minSelect > 0">最少选{{checkboxOption.minSelect}}个</span>
          <span v-if="checkboxOption.maxSelect && checkboxOption.maxSelect > 0">最多选{{checkboxOption.maxSelect}}个</span>
        </span>
      </div>
      <el-form :rules="rules" :model="checkboxOption" ref="checkboxForm">
        <div
          class="option-container"
          v-for="(childitem, childindex) in checkboxOption.optionsData"
          :key="childindex"
        >
          <el-checkbox
            :disabled="isAnswered"
            v-model="checkedOption"
            :label="childitem.id"
            @change="handleCheckboxChange(childitem,childindex,$event)"
            :style="{'color':isChecks&&checkboxOption.checksed?'#f56c6c':'#000'}"
            :class="[isChecks&&checkboxOption.checksed?'errorCheck':'rightCheck']"
          >
            <div class="checkbox-text">{{childitem.name}}</div>
          </el-checkbox>
          <el-form-item
            label-width="0"
            :prop="'optionsData.' + childindex + '.textValue'"
            :rules="rules.textValue"
          >
            <el-input
              :disabled="isAnswered"
              v-model="childitem.textValue"
              v-if="childitem.isInput == 1"
              @focus="handleCheckboxChange(childitem,childindex)"
              @input="handleInput(childitem)"
              class="checkbox-input"
            ></el-input>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import notice from "../notice/notice.js"
export default {
  props: {
    //当前题目的所有相关信息
    previewOption: {
      type: Object
    },
    //该题目在当前问卷中的序号
    index: {
      type: Number
    },
    //该题目是否已做答，true为已答过，false为未答
    isAnswered: {
      type: Boolean
    },
    //是否显示题目序号，true为显示，false为不显示
    isQuestionNum: {
      type: Boolean
    },
    list:{
      type:Object
    },
    isChecks:{
      type: Boolean
    }
  },
  mounted() {
    this.checkboxOption = this.previewOption;
    // console.log(12,this.list)
    this.previewOption.optionsData = this.previewOption.optionsData.map(element => {
      return {
        ...element,
        optionId: element.id, //选项id
        optionName: "", //选项label
        textValue: "" //保存选项后的输入框的值
      };
    });
    if (this.previewOption.answers && this.previewOption.answers.length > 0) {
      this.checkedOption = this.previewOption.answers.map(
        item => item.optionId
      );
      this.previewOption.optionsData.forEach(item => {
        this.previewOption.answers.forEach(answerOption => {
          if (item.optionId === answerOption.optionId) {
            item.textValue = answerOption.textValue;
          }
        });
      });
    }
  },
  data() {
    return {
      arr:[],
      checkboxOption: {},
      answerVal: [],
      isAnswer: false, //标志当前题目是否已做答
      isValided: false,
      checkedOption: [], //保存checkbox的选中项
      rules: {
        textValue: [
          { min: 1, max: 200, message: '长度小于200个字符' }
        ]
      },
      arr:[]
    };
  },

  methods: {
    handleCheckboxChange(optionItem, optionIndex, val) {
      // console.log(optionItem,optionIndex,val)
      //val为undefined表示输入框获取到焦点，此时默认选中当前行的checkbox
      let obj = {}
      if(val==true){
        obj.optionItem = optionItem
        obj.optionIndex = optionIndex
        obj.val = val
        this.arr = this.arr.concat(obj)
         this.list.questions.forEach((item,val)=>{
           if(item.id == optionItem.questionId){
              item.lengthArr = this.arr
           }
         })
        // console.log('arr',this.list.questions)
      }
      else{
        obj.optionItem = optionItem
        obj.optionIndex = optionIndex
        obj.val = val
        if(this.arr&&this.arr.length){
          this.list.questions.forEach((item,val)=>{
           if(item.id == optionItem.questionId&&item.lengthArr.length){
              item.lengthArr.forEach((v,i)=>{
                if(optionIndex==i){
                  // console.log('index')
                  v.val=false
                  item.lengthArr.splice(i,1)
                }
              })
           }
         })
        }
        // console.log('arr',this.list.questions)
      }
      if (val === undefined) {
        const indexValue = this.checkedOption.indexOf(optionItem.id);
        indexValue === -1 && this.checkedOption.push(optionItem.id);
        this.previewOption.optionsData[optionIndex].optionName = optionItem.name;
      } else {
        this.previewOption.optionsData[optionIndex].optionName = val ? optionItem.name : "";
      }
      //获取checkbox选中的值，当optionId和optionName同事存在当前的checkbox值才有效
      const answerArray = this.previewOption.optionsData.filter(item => {
        return item.optionId && item.optionName;
      });
      this.answerVal = answerArray.map(item => {
        return {
          pvqId: localStorage.getItem("currentQuestionId"), //问卷id
          questionId: this.checkboxOption.id, //题目id
          questionType: this.checkboxOption.type, //题目类型
          optionId: item.id, //选项id
          optionName:item.optionName, //选项label
          textValue: item.textValue, //保存选项后的输入框的值
        };
      });
      this.isAnswer = answerArray.length > 0 ? true : false;
      this.checkboxOption.isMust == 1 &&
      this.$parent.setAnswerPercentage(this.checkboxOption.id, this.isAnswer);
      this.isValided = false;
      	 //观察者触发
        notice.emitBrother("questionnaireObserver");
    },

    handleInput(optionItem){
      this.isValided = false;
      this.answerVal.forEach(item => {
        if(optionItem.optionId === item.optionId){
          item.textValue = optionItem.textValue;
        }
      });
    },
    validedForm () {
      let validedResult = true;
      this.$refs['checkboxForm'].validate((result) => {
        if (result) {
          validedResult = false;
        } else {
          validedResult = true;
        }
      })
      return validedResult;
    },
    checkValided() {
      if (this.checkboxOption.isMust == 0) {
        return false;
      }
      //判断当前题目是否必填，必填时才需要判断是否已答
      if (this.checkboxOption.isMust != 1) {
        return true;
      }
      //判断当前题目是否已答,true为已做答，false为未做答
      if (!this.isAnswer) {
        return true;
      }
        return this.validedForm();
      //判断选中的选项格式是否满足设置的最多选择和最少选择
      if (
        (this.checkboxOption.maxSelect !== this.checkboxOption.minSelect &&this.checkedOption.length > this.checkboxOption.maxSelect) ||
        this.checkedOption.length < this.checkboxOption.minSelect
      ) {
        return true;
      }
      return false;
    },
    doValidedFlag(){
      return this.checkValided();
    },
    doValided() {
      this.isValided = this.checkValided();
      return this.isValided;
    }
  }
};
</script>

<style  scoped  lang="scss">
.checkbox-container {
  padding: 10px;

  .errorCheck{
    .el-checkbox__inner{
      border-color:#f56c6c;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner{
      border-color:#f56c6c !important;
      background-color:#f56c6c !important;
    }
    .el-checkbox__label .checkbox-text{
      color:#f56c6c;
    }
  }
  .quesName {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 4px;
    word-wrap: break-word;
  }

  .question-name-text{
    font-weight: 600;
    font-size: 16px;
    line-height : 20px;
    color : #353535;
  }

  .starRed {
    color: red;
    font-weight: 600;
  }
  .chexMinMax{
    color: blue;
    font-size : 14px;
    margin-left: 3px;
  }

  .option-container {
    display: flex;
    padding: 10px 0;
    align-items: flex-start;
    flex-direction: column;
    //checkbox的label颜色
    .el-checkbox__input.is-checked+.el-checkbox__label {
        color: #2cc7c5;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: #2cc7c5;
      border-color: #2cc7c5;
    }

    .el-input__inner:focus{
      border:1px solid #2CC7C5;
      outline:none;
    }

    .el-form-item {
      margin-bottom: 0;
      // width : calc( 100% -34px) !important;
      // width : 100%
    }
    // 多选框容器
    .el-checkbox {
      margin-right: 0;
      display : flex;
      margin-bottom : 10px;
    }
    // checkbox文字
    .checkbox-text {
      word-break: normal;
      width: auto;
      display: block;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-all;
      overflow: hidden;
      line-height: 20px;
    }
     // 输入框
    .el-input__inner {
      width : calc( 100% + 50px);
      margin-left : 24px
    }
    // 多选按钮位置
    .el-checkbox__inner{
      position: relative;
      top: calc( 50% - 7px)
    }
    //校验提示文字
    .el-form-item__error {
      left: 24px;
    }

  }

  .common-container {
    border: 1px solid transparent;
    padding: 9px;
  }

  .error-border {
    border: 1px solid red;
    padding: 9px;
  }

  // chebox在disable时的样式
  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    border-color: #f5f7fa !important ;
    background: #f5f7fa;
  }
  // chebox在disable时label的样式
  .el-checkbox__input.is-disabled+span.el-checkbox__label {
    color: #C0C4CC !important;
    cursor: not-allowed;
  }
  .el-form-item.is-success .el-input__inner {
    border-color: #DCDFE6;
  }
  .el-form-item.is-success .el-input__inner:focus {
    border-color: #2CC7C5;
  }
}
</style>
