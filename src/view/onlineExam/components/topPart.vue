<template>
  <!-- 公共头部开始 -->
  <div class="top-box">
    <span class="back-box" @click="back">
      <van-icon name="arrow-left" style="min-width: 1em;margin-right: 0.106667rem;vertical-align: sub;font-size: 16px;" />
      <span class="return-text">{{title}}</span>
    </span>
    <slot v-if="rightPart" name="difinePort"></slot>
  </div>
  <!-- 公共头部结束 -->
</template>
<script type="text/ecmascript-6">
export default {
  name: "topPart",
  props: {
    rightPart: {
      type: Boolean,
      default: false
    }, //控制头部右边组件是否显示
    //控制自己是否有指定的返回路径
    routerObj: {
      type: Object,
      required: false
    }
  },
  data() {
    return {
      title: "返回"
    };
  },
  mounted() {},
  methods: {
    back() {
      if (this.routerObj && this.routerObj.router) {
        this.$router.push(this.routerObj.router);
      } else {
        this.$router.go(-1);
        // this.$router.back();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
// @import '../assets/stylus/theme';
.return-text {
  display: inline-block;
  // vertical-align: text-top;
  height: 20px;
  font-size: 14px;
  font-family: PingFang-SC-Medium, PingFang-SC;
  font-weight: 500;
  // color: $text-black;
  line-height: 7px;
}

.top-box {
  display: flex;
  background: #fff;
  height: 44px;
  line-height: 44px;
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 99;
  box-shadow: 0 2px 3px #D4DAEB;

  i {
    font-size: 16px;
    vertical-align: initial;
  }

  .back-box {
    // margin: auto 0;
    // padding-left: 10px;
    //     -webkit-box-align: center;
    // -webkit-align-items: center;
    align-items: center;
    padding: 0 16px;
    font-size: 0.373333rem;
    cursor: pointer;
  }

  .van-dropdown-menu {
    flex: 1;
  }
}
</style>
