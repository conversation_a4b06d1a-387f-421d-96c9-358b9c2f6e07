<template>
  <div class="radio-container">
    <!-- 设置此题目必答时点击提交问卷需要判断此题是否已做答 -->
    <div class="common-container">
      <div class="quesName">
        <span v-show="isQuestionNum">{{index}}、</span>
        <span class="question-name-text">{{radioOption.name}}</span>
        <span class="question-name-text">( 单选 )</span>
        <span class="chexMinMax">
          <span>分值 {{Number(radioOption.score)}}分</span>
        </span>
      </div>
      <el-form :model="radioOption" ref="radioForm">
        <div
          class="option-container"
          v-for="(childitem, childindex) in radioOption.optionsData"
          :key="childindex"
        >
          <div v-if="childitem.isSelected=='1'"><span style="color:#4E5969">您的回答&nbsp;&nbsp;&nbsp;</span><span :style="!IsAnswerTrue?'color:#F53F3F':''">{{childitem.name}}</span></div>
            <!-- <el-radio
              :disabled="true"
              v-model="childitem.isSelected"
              label="1"
            >
              <div class="radio-text">{{childitem.name}}</div>
            </el-radio> -->
        </div>
         <div v-if="!IsAnswerTrue" class="option-container"><span  style="color:#4E5969">正确答案&nbsp;&nbsp;&nbsp;</span><span>{{trueAnswer}}</span></div>
        <!-- <div  v-if="IsAnswerTrue" class="option-container"><span  style="color:#4E5969">正确答案&nbsp;&nbsp;&nbsp;</span><span>{{radioOption.optionsData[0].name}}</span></div> -->
        <div v-if="IsAnswerTrue" class="answerJudge colorBlue">
          <div> <span> 回答正确 </span><span style="margin-left: 10px">+{{Number(radioOption.score)}}分</span></div>
        </div>
        <div v-else class="answerJudge colorRed">
          <div>
            <!-- <van-icon name="clear" />  -->
          <span> 回答错误 </span><span style="margin-left: 10px"> +0分</span></div>
        </div>
        <!-- <div class="analysisDiv"> -->

          <!-- <div><div style="font-weight:500;color:rgb(20, 20, 19);margin-bottom: 8px">答案解析</div><span style="font-size:14px;">{{radioOption.questionAnalysis}}</span></div> -->
        <!-- </div> -->
      </el-form>
    </div>
  </div>
</template>

<script>
import notice from "../notice/notice.js"
  export default {
    props: {
      //当前题目的所有相关信息
      previewOption: {
        type: Object
      },
      //该题目在当前问卷中的序号
      index: {
        type: Number
      },
      //是否显示题目序号，true为显示，false为不显示
      isQuestionNum: {
        type: Boolean
      }
    },
    mounted() {
      this.radioOption = this.previewOption;
      let optionData = JSON.parse(JSON.stringify(this.radioOption.optionsData))
      //判断题目是否正确
      this.IsAnswerTrue = !optionData.some(item=> item.isAnswer != item.isSelected)
      //拿到正确答案集合
      if(!this.IsAnswerTrue) {
        let trueAnswer = []
        optionData.map(item=> {
          if(item.isAnswer == '1') trueAnswer.push(item.name)
        })
        this.trueAnswer = trueAnswer.length>0?trueAnswer.toString():""
      }
    },
    data () {
      return{
        radioOption: {},
        IsAnswerTrue: true,//默认 正确
        trueAnswer: "",//正确答案
      }
    },

    methods: {

    }
  }
</script>

<style lang='scss' scoped>
.radio-container{
  padding: 10px;

  .quesName {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 4px;
    word-wrap: break-word;
  }
  .question-name-text{
    // font-weight: 600;
    font-size: 17px;
    line-height : 20px;
    color : #1D2129;
  }
  .answerJudge {
    font-size: 15px;
    // display: flex;
    // justify-content: space-between;
    padding: 5px 1px;
  }
  .colorBlue {
    color: rgb(35, 188, 186);
  }
  .colorRed {
    color: rgb(248, 35, 16);
  }
  .analysisDiv {
    font-size: 15px;
    background: rgb(244, 245, 249);
    color:rgb(74, 73, 73);
    border: #797979;
    padding: 5px;
  }
  .chexMinMax{
    color: rgb(202, 202, 210);
    font-size : 14px;
    margin-left: 3px;
  }
  .common-container {
    border: 1px solid transparent;
    padding: 9px;
  }
  //radio在disable时的样式
  // .el-radio__input.is-disabled .el-radio__inner, .el-radio__input.is-disabled.is-checked .el-radio__inner{
  //   border-color: #f5f7fa;
  //   background: #f5f7fa;
  // }
  .el-radio__input.is-disabled+span.el-radio__label {
    color: #606266 !important;
    cursor: not-allowed;
  }
  .el-form-item.is-success .el-input__inner {
    border-color: #DCDFE6;
  }
  .el-form-item.is-success .el-input__inner:focus {
    border-color: #2CC7C5;
  }
}
.option-container{
  font-size: 16px;
  line-height: 30px;
}
</style>
