<template>
  <div class="checkbox-container">
    <div class="common-container">
      <div class="quesName">
        <span v-show="isQuestionNum">{{ index }}、</span>
        <span class="question-name-text">{{ checkboxOption.name }}</span>
        <span class="question-name-text">( 多选 )</span>
        <span class="chexMinMax">
          <span>分值 {{ Number(checkboxOption.score) }}分</span>
        </span>
      </div>
      <el-form :model="checkboxOption" ref="checkboxForm">
        <div class="option-container" v-for="(childitem, childindex) in checkboxOption.optionsData" :key="childindex">

          <!-- <el-checkbox
            :disabled="true"
            v-model="childitem.isSelected"
            :true-label="isInputTrue"
            :false-label="isInputFalse"
          >
            <div class="checkbox-text">{{childitem.name}}</div>
          </el-checkbox> -->
        </div>
        <!-- <div v-if="IsAnswerTrue" class="option-container">
          <span style="color:#4E5969">正确答案&nbsp;&nbsp;&nbsp;</span><span>{{ checkboxOption.optionsData[0].name }}</span>
        </div> -->
         <div v-if="checkAnswerArr.length > 0" class="option-container">
            <span style="color:#4E5969">您的回答&nbsp;&nbsp;&nbsp;</span><span :style="!IsAnswerTrue ? 'color:#F53F3F' : ''">{{ checkAnswer }}</span>
          </div>
        <div v-if="!IsAnswerTrue" class="option-container">
          <span style="color:#4E5969">正确答案&nbsp;&nbsp;&nbsp;</span><span>{{ trueAnswer }}</span>
        </div>
        <!-- <div v-else class="option-container"><span  style="color:#4E5969">正确答案&nbsp;&nbsp;&nbsp;</span><span>{{childitem.name}}</span></div> -->
        <div v-if="IsAnswerTrue" class="answerJudge colorBlue">
          <div>
            <!-- <van-icon name="checked" /> -->
             <span> 回答正确 </span><span style="margin-left: 10px">+{{ Number(checkboxOption.score) }}分</span>
          </div>
        </div>
        <div v-else class="answerJudge colorRed">
          <div> <span> 回答错误 </span><span style="margin-left: 10px"> +0分</span></div>
        </div>
        <!-- <div class="analysisDiv">
          <div v-if="!IsAnswerTrue" style="margin-bottom: 8px"><div style="margin-bottom: 5px" class="colorBlue">正确答案</div><span style="font-size:14px">{{trueAnswer}}</span></div>
          <div><div style="font-weight:500;color:rgb(20, 20, 19);margin-bottom: 8px;">答案解析</div><span style="font-size:14px;">{{checkboxOption.questionAnalysis}}</span></div>
        </div> -->
      </el-form>
    </div>
  </div>
</template>
<script>
import notice from "../notice/notice.js";
export default {
  props: {
    //当前题目的所有相关信息
    previewOption: {
      type: Object
    },
    //该题目在当前问卷中的序号
    index: {
      type: Number
    },
    //是否显示题目序号，true为显示，false为不显示
    isQuestionNum: {
      type: Boolean
    }
  },
  mounted() {
    this.checkboxOption = this.previewOption;
    let optionData = JSON.parse(JSON.stringify(this.checkboxOption.optionsData));
    this.IsAnswerTrue = !optionData.some(item => item.isAnswer != item.isSelected);
    if (!this.IsAnswerTrue) {
      let trueAnswer = [];
      optionData.map(item => {
        if (item.isAnswer == "1") trueAnswer.push(item.name);
      });
      this.trueAnswer = trueAnswer.length > 0 ? trueAnswer.toString() : "";
    }

    optionData.map(i => {
        if (i.isSelected == "1") this.checkAnswerArr.push(i.name);
      });
      this.checkAnswer = this.checkAnswerArr.length > 0 ? this.checkAnswerArr.toString() : "";
  },
  data() {
    return {
      IsAnswerTrue: true, //默认 正确
      checkboxOption: {},
      isInputTrue: "1", //默认选中
      isInputFalse: "0", //默认选中
      trueAnswer: "", //正确答案
      checkAnswer:'',
      checkAnswerArr:[],
    };
  },

  methods: {}
};
</script>

<style lang="scss" scoped>
.checkbox-container {
  padding: 10px;

  .quesName {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 4px;
    word-wrap: break-word;
  }

  .question-name-text {
    // font-weight: 600;
    font-size: 17px;
    line-height: 20px;
    color: #1d2129;
  }
  .answerJudge {
    font-size: 15px;
    // display: flex;
    // justify-content: space-between;
    padding: 5px 1px;
  }
  .colorBlue {
    color: rgb(35, 188, 186);
  }
  .colorRed {
    color: rgb(248, 35, 16);
  }
  .analysisDiv {
    font-size: 15px;
    background: rgb(244, 245, 249);
    color: rgb(74, 73, 73);
    border: #797979;
    padding: 5px;
  }
  .chexMinMax {
    color: rgb(202, 202, 210);
    font-size: 14px;
    margin-left: 3px;
  }

  // .option-container {
  //   display: flex;
  //   padding: 10px 0;
  //   align-items: flex-start;
  //   flex-direction: column;
  //   //checkbox的label颜色
  //   .el-checkbox__input.is-checked + .el-checkbox__label {
  //     color: #2cc7c5;
  //   }
  //   .el-checkbox__input.is-checked .el-checkbox__inner,
  //   .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  //     background-color: #2cc7c5;
  //     border-color: #2cc7c5;
  //   }

  //   .el-input__inner:focus {
  //     border: 1px solid #2cc7c5;
  //     outline: none;
  //   }

  //   .el-form-item {
  //     margin-bottom: 0;
  //   }
  //   // 多选框容器
  //   .el-checkbox {
  //     margin-right: 0;
  //     display: flex;
  //     margin-bottom: 10px;
  //   }
  //   // checkbox文字
  //   .checkbox-text {
  //     word-break: normal;
  //     width: auto;
  //     display: block;
  //     white-space: pre-wrap;
  //     word-wrap: break-word;
  //     overflow: hidden;
  //     line-height: 20px;
  //     word-break: break-all;
  //   }
  //   // 多选按钮位置
  //   .el-checkbox__inner {
  //     position: relative;
  //     top: calc(50% - 7px);
  //   }
  // }

  .common-container {
    border: 1px solid transparent;
    padding: 9px;
  }

  .error-border {
    border: 1px solid red;
    padding: 9px;
  }

  // chebox在disable时的样式
  // .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  //   border-color: #f5f7fa !important ;
  //   background: #f5f7fa;
  // }
  // chebox在disable时label的样式
  .el-checkbox__input.is-disabled + span.el-checkbox__label {
    color: #606266 !important;
    cursor: not-allowed;
  }
  .el-form-item.is-success .el-input__inner {
    border-color: #dcdfe6;
  }
  .el-form-item.is-success .el-input__inner:focus {
    border-color: #2cc7c5;
  }
}
.option-container {
  font-size: 16px;
  line-height: 30px;
}
</style>
