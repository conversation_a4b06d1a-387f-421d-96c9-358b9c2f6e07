<template>
  <div class="radio-container">
    <!-- 设置此题目必答时点击提交问卷需要判断此题是否已做答 -->
    <div class="common-container">
      <div class="quesName">
        <span v-show="isQuestionNum">{{index}}.</span>
        <span class="question-name-text">{{judgeOption.name}}</span>
        <span class="question-name-text" style="font-size:15px">(判断)</span>
        <span style="font-size: 14px;color: rgb(202, 202, 210);margin-left:5px">分值 {{Number(judgeOption.score)}}分</span>
      </div>
      <el-form :model="judgeOption" ref="radioForm">
        <div
          class="option-container"
          v-for="(childitem, childindex) in judgeOption.optionsData"
          :key="childindex"
        >
          <el-radio
            :disabled="isAnswered"
            v-model="judgeValue"
            :label="childitem.id"
            @change="handleRadioChange(childitem,childindex,$event)"
          >
            <div class="radio-text">{{childitem.name}}</div>
          </el-radio>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import notice from "../notice/notice.js"
  export default {
    props: {
      //当前题目的所有相关信息
      previewOption: {
        type: Object
      },
      //该题目在当前问卷中的序号
      index: {
        type: Number
      },
       //该题目是否已做答，true为已答过，false为未答
      isAnswered: {
        type: Boolean
      },
      //是否显示题目序号，true为显示，false为不显示
      isQuestionNum: {
        type: Boolean
      }
    },
    created() {
      this.judgeOption = this.previewOption;
      this.previewOption.optionsData = this.previewOption.optionsData.map(element => {
        return {
          ...element,
          optionId: element.id, //选项id
          optionName:"", //选项label
        }
      });
      if(this.previewOption.answers && this.previewOption.answers.length > 0){
        this.judgeValue = this.previewOption.answers[0].optionId;
      }
    },
    data () {
      return{
        judgeOption: {},
        answerVal: [], //保存当前题目的答卷信息，父页面（答题页面）获取后用于构造答卷的请求参数
        isAnswer: false, //标志当前题目是否已做答
        judgeValue: "",
      }
    },

    methods: {
      handleRadioChange(optionItem,optionIndex,val){
        //清空其他选项输入框的值
        this.previewOption.optionsData.forEach((item,index) => {
          if(optionIndex !== index){
            item.optionName = "";
          }
        });
        //val为undefined表示输入框获取到焦点，此时默认选中当前行的radio
        if(val === undefined) {
          this.judgeValue = optionItem.id;
        }
        this.previewOption.optionsData[optionIndex].optionName = optionItem.name ;
        //获取radio选中的值，当optionId和optionName同事存在当前的radio值才有效
        const answerArray = this.previewOption.optionsData.filter((item) => {
          return item.optionId && item.optionName
        });
        this.answerVal = answerArray.map((item) => {
          return {
            pvqId:localStorage.getItem('currentQuestionId'), //问卷id
            questionId:this.judgeOption.id, //题目id
            questionType:this.judgeOption.type, //题目类型
            optionId: item.id, //选项id
            optionName:item.optionName, //选项label
          }
        });
        this.isAnswer = true;
        this.$parent.setAnswerPercentage(this.judgeOption.id, this.isAnswer);
        this.doValided();
        //观察者触发
        notice.emitBrother("questionnaireObserver");
      },
      doValidedFlag(){
        return false;
      },
      doValided() {
        return false;
      }
    }
  }
</script>

<style scoped  lang="scss">
.radio-container{
  padding: 10px;

  .quesName {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 4px;
    word-wrap: break-word;
  }
  .starRed {
    color: red;
    font-weight: 600;
  }
  .question-name-text{
    font-weight: 600;
    font-size: 16px;
    line-height : 20px;
    color : #353535;
  }
  .option-container {
    display: flex;
    padding: 10px 0;
    align-items: flex-start;
    flex-direction: column;

    .el-radio__input.is-checked .el-radio__inner{
      border-color: #2cc7c5;
      background: #2cc7c5;
    }
    .el-radio__input.is-checked+.el-radio__label {
      color: #2cc7c5;
    }
    .el-input__inner:focus{
      border:1px solid #2CC7C5;
      outline:none;
    }
    //单选按钮容器
    .el-radio {
      margin-right: 0;
      display: inherit;
      margin-bottom : 10px;
    }
    // 单选按钮的文字
    .el-radio__label{
      color: #000;
    }
    .radio-text {
      word-break: normal;
      width: auto;
      display: block;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-all;
      overflow: hidden;
      line-height: 20px;
    }
    // 输入框
    .el-input__inner {
      width : calc( 100% - 34px);
      margin-left : 24px
    }
    // 单选按钮框
    .el-radio__inner {
      position: relative;
      top: calc( 50% - 7px)
    }
    //校验提示文字
    .el-form-item__error {
      left: 24px;
    }
    // .el-form-item.is-success .el-input__inner,
    // .el-form-item.is-success .el-input__inner:focus,
    // .el-form-item.is-success .el-textarea__inner,
    // .el-form-item.is-success .el-textarea__inner:focus{
    //   border-color: #2cc7c5;
    // }
    .el-form-item {
      margin-bottom : 0px;
    }
    //修改被全局样式覆盖的
    .el-radio__inner,
    .el-radio__input.is-checked .el-radio__inner {
      width: 14px;
      height: 14px;
    }
    .el-radio__inner::after {
      width: 7px;
      height: 7px;
    }
  }
  .common-container {
    border: 1px solid transparent;
    padding: 9px;
  }
  .error-border {
    border: 1px solid red;
    padding: 9px;
  }
  //radio在disable时的样式
  .el-radio__input.is-disabled .el-radio__inner, .el-radio__input.is-disabled.is-checked .el-radio__inner{
    border-color: #f5f7fa;
    background: #f5f7fa;
  }
  .el-radio__input.is-disabled+span.el-radio__label {
    color: #C0C4CC !important;
    cursor: not-allowed;
  }
  .el-form-item.is-success .el-input__inner {
    border-color: #DCDFE6;
  }
  .el-form-item.is-success .el-input__inner:focus {
    border-color: #2CC7C5;
  }
}

</style>
