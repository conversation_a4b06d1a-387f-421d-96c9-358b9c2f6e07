<template>
  <div class="radio-container">
    <!-- 设置此题目必答时点击提交问卷需要判断此题是否已做答 -->
    <div class="common-container">
      <div class="quesName">
        <span v-show="isQuestionNum">{{index}}、</span>
        <span class="question-name-text">{{judgeOption.name}}</span>
        <span class="question-name-text">( 判断 )</span>
        <span class="chexMinMax">分值 {{Number(judgeOption.score)}}分</span>
      </div>
      <el-form :model="judgeOption" ref="radioForm">
        <div
          class="option-container"
          v-for="(childitem, childindex) in judgeOption.optionsData"
          :key="childindex"
        >
          <!-- <el-radio
            :disabled="true"
              v-model="childitem.isSelected"
              label="1"
          >
            <div class="radio-text">{{childitem.name}}</div>
          </el-radio> -->
           <div v-if="childitem.isSelected=='1'"><span style="color:#4E5969">您的回答&nbsp;&nbsp;&nbsp;</span><span :style=" !IsAnswerTrue?'color:#F53F3F':''">{{childitem.name}}</span></div>
        </div>
         <div v-if="!IsAnswerTrue" class="option-container"><span  style="color:#4E5969">正确答案&nbsp;&nbsp;&nbsp;</span><span>{{trueAnswer}}</span></div>
        <!-- <div  v-if="IsAnswerTrue" class="option-container"><span  style="color:#4E5969">正确答案&nbsp;&nbsp;&nbsp;</span><span>{{judgeOption.optionsData[0].name}}</span></div> -->
        <div v-if="IsAnswerTrue" class="answerJudge colorBlue">
          <div><van-icon name="checked" /> <span> 回答正确 </span><span style="margin-left: 10px">+{{Number(judgeOption.score)}}分</span></div>
        </div>
        <div v-else class="answerJudge colorRed">
          <div><van-icon name="clear" /> <span> 回答错误 </span><span style="margin-left: 10px"> +0分</span></div>
        </div>
        <!-- <div class="analysisDiv">
          <div v-if="!IsAnswerTrue" style="margin-bottom: 8px"><div style="margin-bottom: 5px" class="colorBlue">正确答案</div><span style="font-size:14px">{{trueAnswer}}</span></div>
          <div><div style="font-weight:500;color:rgb(20, 20, 19);margin-bottom: 8px">答案解析</div><span style="font-size:14px;">{{judgeOption.questionAnalysis}}</span></div>
        </div> -->
      </el-form>
    </div>
  </div>
</template>

<script>
import notice from "../notice/notice.js"
  export default {
    props: {
      //当前题目的所有相关信息
      previewOption: {
        type: Object
      },
      //该题目在当前问卷中的序号
      index: {
        type: Number
      },
      //是否显示题目序号，true为显示，false为不显示
      isQuestionNum: {
        type: Boolean
      }
    },
    created() {
      this.judgeOption = this.previewOption;
      let optionData = JSON.parse(JSON.stringify(this.judgeOption.optionsData))
      this.IsAnswerTrue = !optionData.some(item=> item.isAnswer != item.isSelected)
      if(!this.IsAnswerTrue) {
        let trueAnswer = []
        optionData.map(item=> {
          if(item.isAnswer == '1') trueAnswer.push(item.name)
        })
        this.trueAnswer = trueAnswer.length>0?trueAnswer.toString():""
      }
    },
    data () {
      return{
        judgeOption: {},
        IsAnswerTrue: true,//默认 正确
        trueAnswer: "",//正确答案
      }
    },

    methods: {

    }
  }
</script>

<style lang='scss' scoped>
.radio-container{
  padding: 10px;

  .quesName {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 4px;
    word-wrap: break-word;
  }
  .starRed {
    color: red;
    font-weight: 600;
  }
  .question-name-text{
    // font-weight: 600;
    font-size: 17px;
    line-height : 20px;
    color : #1D2129;
  }
  .answerJudge {
    font-size: 15px;
    // display: flex;
    // justify-content: space-between;
    padding: 5px 1px;
  }
  .colorBlue {
    color: rgb(35, 188, 186);
  }
  .colorRed {
    color: rgb(248, 35, 16);
  }
  .analysisDiv {
    font-size: 15px;
    background: rgb(244, 245, 249);
    color:rgb(74, 73, 73);
    border: #797979;
    padding: 5px;
  }
  .chexMinMax{
    color: rgb(202, 202, 210);
    font-size : 14px;
    margin-left: 3px;
  }
  // .option-container {
  //   display: flex;
  //   padding: 10px 0;
  //   align-items: flex-start;
  //   flex-direction: column;

  //   .el-radio__input.is-checked .el-radio__inner{
  //     border-color: #2cc7c5;
  //     background: #2cc7c5;
  //   }
  //   .el-radio__input.is-checked+.el-radio__label {
  //     color: #2cc7c5;
  //   }
  //   .el-input__inner:focus{
  //     border:1px solid #2CC7C5;
  //     outline:none;
  //   }
  //   //单选按钮容器
  //   .el-radio {
  //     margin-right: 0;
  //     display: inherit;
  //     margin-bottom : 10px;
  //   }
  //   // 单选按钮的文字
  //   .el-radio__label{
  //     color: #000;
  //   }
  //   .radio-text {
  //     word-break: normal;
  //     width: auto;
  //     display: block;
  //     white-space: pre-wrap;
  //     word-wrap: break-word;
  //     overflow: hidden;
  //     line-height: 20px;
  //     word-break: break-all;
  //   }
  //   // 输入框
  //   .el-input__inner {
  //     width : calc( 100% - 34px);
  //     margin-left : 24px
  //   }
  //   // 单选按钮框
  //   .el-radio__inner {
  //     position: relative;
  //     top: calc( 50% - 7px)
  //   }
  //   //校验提示文字
  //   .el-form-item__error {
  //     left: 24px;
  //   }
  //   // .el-form-item.is-success .el-input__inner,
  //   // .el-form-item.is-success .el-input__inner:focus,
  //   // .el-form-item.is-success .el-textarea__inner,
  //   // .el-form-item.is-success .el-textarea__inner:focus{
  //   //   border-color: #2cc7c5;
  //   // }
  //   .el-form-item {
  //     margin-bottom : 0px;
  //   }
  //   //修改被全局样式覆盖的
  //   .el-radio__inner,
  //   .el-radio__input.is-checked .el-radio__inner {
  //     width: 14px;
  //     height: 14px;
  //   }
  //   .el-radio__inner::after {
  //     width: 7px;
  //     height: 7px;
  //   }
  // }
  .common-container {
    border: 1px solid transparent;
    padding: 9px;
  }
  .error-border {
    border: 1px solid red;
    padding: 9px;
  }
  //radio在disable时的样式
  // .el-radio__input.is-disabled .el-radio__inner, .el-radio__input.is-disabled.is-checked .el-radio__inner{
  //   border-color: #f5f7fa;
  //   background: #f5f7fa;
  // }
  .el-radio__input.is-disabled+span.el-radio__label {
    color: #606266 !important;
    cursor: not-allowed;
  }
  .el-form-item.is-success .el-input__inner {
    border-color: #DCDFE6;
  }
  .el-form-item.is-success .el-input__inner:focus {
    border-color: #2CC7C5;
  }
}
.option-container{
  font-size: 16px;
  line-height: 30px;
}
</style>
