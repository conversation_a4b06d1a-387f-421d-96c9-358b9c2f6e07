class Observer {
    //构造函数初始化 消息中心
    constructor() {
        this.arrBrother = [];
    }
    //注册监听者
    onBrother(name, objs) {
        var obj = {
            key: name,
            value: objs
        }
        this.arrBrother.push(obj);
    }
    //通知改变
    emitBrother(name, ...args) {
        for (var a = 0; a < this.arrBrother.length; a++) {
            if (this.arrBrother[a]['key'] == name) {
                this.arrBrother[a]['value'](...args);
            }
        }
    }
    //删除监听者
    offBrother(name) {
        for (var a = 0; a < this.arrBrother.length; a++) {
                if(this.arrBrother[a]['key'] == name){
                    this.arrBrother.splice(a,1);
                    a--;
                }
        }
    }
}
let observer = new Observer();
export default observer;
