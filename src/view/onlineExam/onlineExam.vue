<template>
  <div class="inner">
    <Header title="在线考试" @backFun="goBack"> </Header>
    <van-tabs @click="tabclick" class="tabs">
      <van-tab title="未答考试" name="0"> </van-tab>
      <van-tab title="已答考试" name="1"></van-tab>
      <van-tab title="已超期" name="2"></van-tab>
    </van-tabs>
    <div v-if="list.length > 0" class="hei">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div v-for="item in list" :key="item.id" class="item" @click="questionnaireDetails(item)">
            <div>
              <div class="accoutName">
                <div class="accout-title">{{ item.name || "-" }}</div>
                <div>
                  <span :class="answerType == '0' ? 'unnormal' : answerType == '1' ? 'normal' : 'otherStatus'"
                    >{{ answerType == "0" ? "未答" : answerType == "1" ? "已答" : "已超期" }}
                  </span>
                  <!-- <van-icon name="arrow" color="#C9CDD4" /> -->
                </div>
              </div>
              <div class="child">
                <span class="child-text">总分</span>
                <span>{{ item.score || "-" }}</span>
              </div>
              <div class="child">
                <span class="child-text">限制时间</span>
                <span>{{ item.limit_time || "-" }}分钟</span>
              </div>
              <div class="child">
                <span class="child-text">开始日期</span>
                <span>{{ formatDateTime(item.start_time) || "-" }}</span>
              </div>
              <div class="child">
                <span class="child-text">结束日期</span>
                <span>{{ formatDateTime(item.end_time) || "-" }}</span>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <van-empty class="custom-image" :image="require('../../assets/images/noData.png')" description="" />
    </div>
  </div>
</template>

<script>
import moment from "moment";
import Vue from "vue";
import { Toast } from "vant";

Vue.use(Toast);
export default {
  data() {
    return {
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      questionStatus: "publish",
      current: 1,
      size: 15,
      total: 0,
      list: [
        // {
        //   questionName:'考试',
        //   answerTag:'0',
        //   officeName:'',
        //   createTime:'',
        //   startTime:'',
        //   endTime:'',
        // }
      ],
      answerType: "0"
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    getcurrentTime() {
      let date = new Date();
      let Y = date.getFullYear(); // 获取系统的年；
      let M = date.getMonth() + 1; // 获取系统月份，由于月份是从0开始计算，所以要加1
      let D = date.getDate(); // 获取系统日
      let H = date.getHours(); // 获取系统时间
      let m = date.getMinutes() > 10 ? date.getMinutes() : "0" + date.getMinutes(); // 分
      let s = date.getSeconds(); // 秒
      M = M < 10 ? "0" + M : M;
      D = D < 10 ? "0" + D : D;
      H = H < 10 ? "0" + H : H;
      s = s < 10 ? "0" + s : s;
      return Y + "-" + M + "-" + D + " " + H + ":" + m + ":" + s;
      // return Y + '年' + M + '月' + D + '日' + h + '时' + m + '分'
    },
    formatDateTime(val) {
      return moment(val).format("YYYY-MM-DD HH:mm:ss");
    }, //时间转换
    questionnaireDetails(row) {
      console.log(row, "row");
      if (this.answerType == "0" && this.getcurrentTime() < this.formatDateTime(row.start_time)) return Toast.fail("未开始，不可答题");
      if (this.answerType == "2") return Toast.fail("已结束，不可答题");
      if (this.answerType == "0" && this.getcurrentTime() > this.formatDateTime(row.start_time)) {
        this.$dialog
          .confirm({
            type: "confirm",
            title: "是否开始考试",
            message: "开始考试后将不能随意退出，请确认是否现在开始考试"
          })
          .then(() => {
            localStorage.setItem("questRow", JSON.stringify(row));
            this.$router.push({
              path: "/replyQuestion"
            });
          })
          .catch(() => {});
      }
      if (this.answerType == "1") {
        localStorage.setItem("questRow", JSON.stringify(row));
        this.$router.push({
          path: "/replyQuestion"
        });
      }
    },

    onRefresh() {
      this.current = 1;
      this.finished = false;
      this.loading = true;
      this.list = [];
      this.getList();
    },
    onLoad() {
      console.log("执行了");
      this.finished = false;
      this.loading = true;
      this.current++;
      this.getList();
    },
    getList() {
      let params = {
        type: 2,
        currentPage: this.current,
        pageSize: this.size,
        answerType: this.answerType
      };
      this.axios.postContralHostBase("questionFindList", params, res => {
        this.loading = false;
        if (res.code == 200) {
          res.data.forEach(item => {
            this.list.push(item);
          });
          if (this.list.length >= res.total) {
            this.finished = true;
            this.loading = false;
            return;
          }
        }
        console.log(res, "ssssssss");
      });
      // let params = {
      //   questionStatus: this.questionStatus,
      //   unitCode: JSON.parse(localStorage.getItem("loginInfo")).unitCode,
      //   hospitalCode: JSON.parse(localStorage.getItem("loginInfo"))
      //     .hospitalCode,
      //    userId: JSON.parse(localStorage.getItem("loginInfo"))
      //     .staffId,
      //   userName:JSON.parse(localStorage.getItem("loginInfo"))
      //     .staffName,
      //   pageNum: this.current,
      //   pageSize: this.size
      // };
      // this.$api.selfQuestionListToWx(params).then(res => {
      //   this.loading = false;

      //   if (res.code == 200) {
      //   console.log(res, "saaaasa");

      //     res.data.forEach(item => {
      //       this.list.push(item);

      //     });
      //       if (this.list.length >= res.total) {
      //     this.finished = true;
      //     this.loading = false;
      //     return;
      //   }
      //   }

      // });
    },
    tabclick(val) {
      this.answerType = val;
      this.loading = true;
      this.finished = false;
      this.current = 1;
      this.list = [];
      this.getList();
    },
    sysClickBack() {
      api.addEventListener(
        {
          name: "keyback666"
        },
        (ret, err) => {
          this.goBack();
        }
      );
    },
    goBack() {
      api.sendEvent({
        name: "refreshList"
      });
      api.closeFrame({});
    }
  }
};
</script>

<style scoped lang="scss">
.inner {
  height: 100vh;
  background-color: #f2f4f9 overflow hidden;
  .item {
    overflow: auto;
    background-color: #fff;
    margin: 15px 0.1875rem 0.1875rem;
    padding: 0.3125rem;
    border-radius: 0.125rem;
    font-size: 16px;
    color: #1d2129;
    font-family: PingFang SC-Medium;
    line-height: 20px;
    border-bottom: 1px solid #eee;
    .accoutName {
      display: flex;
      justify-content: space-between;
      > div:nth-child(1) {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .child {
      margin-top: 0.8rem;
    }
  }
}
.normal {
  display: inline-block width 36px;
  height: 28px;
  background-color: #fff7e8;
  color: #ff7d00;
  border-radius: 5px;
  padding: 2px 5px;
  font-size: 12px;
  text-align: center;
  vertical-align: middle;
}
.unnormal {
  display: inline-block width 36px;
  height: 28px;
  background-color: #e8ffea;
  color: #00b42a;
  border-radius: 5px;
  padding: 2px 5px;
  font-size: 12px;
  text-align: center;
  vertical-align: middle;
}
.otherStatus {
  display: inline-block width 36px;
  height: 28px;
  background-color: #f2f3f5;
  color: #4e5969;
  border-radius: 5px;
  padding: 2px 5px;
  font-size: 12px;
  text-align: center;
  vertical-align: middle;
}
/deep/ .van-tabs__line {
  background-color: #00cac8;
}
.tabs {
  height: 40px;
}
.hei {
  height: calc(100% - 120px);
  margin-bottom: 40px;
  overflow: auto;
}
.notList {
  position: relative;
  height: calc(90% - 1.24rem);
  .emptyImg {
    position: absolute;
    height: 100%;
    width: 50%;
    left: 25%;
    // background: url('../../../assets/images/equipmentManagement/缺省@2x.png') 0 40% no-repeat;
    background-size: 100% auto;
    .emptyText {
      position: absolute;
      width: 100%;
      text-align: center;
      bottom: 40%;
    }
  }
}
</style>
