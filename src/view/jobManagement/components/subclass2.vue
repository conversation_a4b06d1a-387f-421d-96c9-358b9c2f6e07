<template>
  <div>
    <div v-show="test" class="sign" style="position: fixed; top: 0; z-index: 9999; bottom: 0">
      <signature @closePop="closePop" @saveImg="complete"></signature>
    </div>
    <div class="connent">
      <van-overlay :show="overlayShow">
        <van-loading type="spinner" vertical>加载中...</van-loading>
      </van-overlay>
      <div class="top">基本信息</div>
      <van-form>
        <van-field readonly v-model.trim="form.name" label="危险作业名称" placeholder="请输入危险作业名称" :rules="[{ required: true }]" />
        <van-field readonly v-model.trim="form.situs" label="作业任务编号" placeholder="请输入作业任务编号" :rules="[{ required: true }]" />
        <van-field readonly clickable v-model="form.assignmentTemplateName" label="选择作业票" placeholder="请选择作业票" right-icon="arrow" :rules="[{ required: true }]" />
        <div class="top">作业申请信息</div>
        <div v-for="item in attrList" :key="item.attrId">
          <van-field
            readonly
            v-if="item.attrType == 'TEXT' || item.attrType == 'TEXTAREA'"
            v-model.trim="item.attrValue"
            :label="item.attrName"
          />
            <!-- :placeholder="item.attrPlaceholder" -->

          <van-field v-if="item.attrType == 'DATE'" readonly clickable v-model="item.attrValue" :label="item.attrName"  right-icon="arrow" />
          <!-- :placeholder="item.attrPlaceholder" -->
        </div>
        <div class="top">审核信息</div>
        <el-table border :data="signatureList" stripe style="width: 100%; margin: 10px 0" v-if="taskType == '1'">
          <el-table-column prop="auditPersonName" label="审批人" min-width="70" align="center"> </el-table-column>
          <el-table-column prop="auditTime" label="审批日期" width="170" align="center"> </el-table-column>
          <el-table-column prop="auditStatus" label="审批结果" min-width="80" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.auditStatus == 1 ? "通过" : "驳回" }}</span>
            </template>
          </el-table-column>
        </el-table>
        <div v-else>
          <van-field readonly clickable v-model="form.auditDepartment" label="审核部门" placeholder="请选择审核部门" right-icon="arrow" />
          <van-popup v-model="showDepartment" position="bottom" v-if="this.rowList != undefined">
            <van-picker value-key="teamName" show-toolbar :columns="typeDepartment" @confirm="onDepartment" @cancel="showDepartment = false" />
          </van-popup>
          <van-field readonly clickable v-model="form.auditPersonnel" label="审核人员" placeholder="请选择审核人员" right-icon="arrow" />
        </div>
      </van-form>
      <div class="radio" v-if="flag">
        <van-radio-group v-model="radio" direction="horizontal" :disabled="approvedOrNot == 1">
          <span style="color: #646566; position: relative;"><i class="is_require">*</i>审核结果</span>&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <van-radio name="1" icon-size="16px">通过</van-radio>
          <van-radio name="2" icon-size="16px">驳回</van-radio>
        </van-radio-group>
      </div>
      <div class="remarks2" v-if="flag">
        <span>审核意见</span>
        <van-field
          :disabled="approvedOrNot == 1"
          v-model="opinion"
          rows="1"
          autosize
          type="textarea"
          maxlength="130"
          placeholder="请输入审核意见"
          show-word-limit
          class="message"
          :rules="[{ required: true }]"
        />
      </div>
      <div class="my_flex" v-if="approvedOrNot == '2'">
        <div class="sign_title">签名</div>
        <div v-if="imgData" class="resign">
          <img class="sign-img" :src="imgData" />
          <span @click="showPopup" class="resign-txt" v-if="imgData != ' '">重新签名</span>
        </div>
        <div v-else class="signature_edit" @click="showPopup">
          <div>
            <span class="no_singature">签名</span>
          </div>
        </div>
      </div>

      <div class="bottom">
        <van-button style="background-color: #00cac8; color: #fff; width: 30%" @click="confirm" v-if="approvedOrNot == '2'">提交审核</van-button>
        <van-button style="background-color: #00cac8; color: #fff; width: 25%" @click="back">返回</van-button>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import signature from "./signature.vue";

export default {
  components: {
    signature
  },
  props: {
    rowList: {
      type: Object,
      default: () => {}
    },
    id: {
      type: String,
      default: ""
    },
    approvalVersion: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      flag: true,
      signatureList: [],
      approvedOrNot: "",
      sign: "base64",
      taskType: "",
      signature: "",
      test: false,
      imgData: "",
      status: "1",
      form: {
        name: "", //事故名称
        situs: "", //事故地点
        time: "", //事故时间
        department: "", //责任部门
        departmentId: "", //责任部门ID
        accidentSfter: "", //简要经过
        accidentCasualty: "", //伤亡
        troubleRemoval: "", //处理
        takeSteps: "", //措施
        fileList: [],
        auditDepartment: "", //审核部门
        auditDepartmentId: "", //审核部门ID
        auditPersonnel: "", //审核人员
        auditPersonnelId: "", //审核人员ID
        attachmentUrl: [] //附件
      },
      showType: false,
      showTypeChildren: false,
      showDepartment: false,
      showPersonnel: false,
      typeDepartment: [], //审核部门数据
      radio: "",
      opinion: "", //审核意见
      overlayShow: false,
      attrList: []
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));

    this.detail();
    // if (this.id) {
    //   this.detail();
    // }
    this.obtainPersonnelSignature();
  },
  methods: {
    obtainPersonnelSignature() {
      this.axios.postContralHostBase("obtainPersonnelSignature", "", res => {
        if (res.code == 200) {
          this.imgData = res.data.ossSignature;
          this.signature = res.data.signature;
          this.sign = "png";
        }
      });
    },
    showPopup() {
      this.test = !this.test;
    },
    closePop() {
      this.test = false;
    },
    complete(data) {
      if (!data) return this.$message.error("保存签名失败");
      this.closePop();
      this.imgData = data;
      this.sign = "base64";
      // this.saveApi({ signature: data });
    },
    //审核部门
    onDepartment(val) {
      this.form.auditDepartment = val.teamName;
      this.form.auditDepartmentId = val.id;
      this.showDepartment = false;
      let params = {
        platformFlag: 2,
        roleCode: this.loginInfo.roleCode,
        departId: val.id
      };
      this.axios.postContralHostBase("getUseListByDeptId", params, res => {
        if (res.code == 200) {
          this.typePersonnel = res.data;
        }
      });
    },
    //返回
    back() {
      this.$emit("button", false);
    },
    //获取详情
    detail() {
      console.log(this.approvalVersion, "this.approvalVersion");
      this.overlayShow = true;
      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode
      };
      if (this.id) {
        params.id = this.id;
        params.approvalVersion = this.approvalVersion;
      } else {
        params.id = this.rowList.id;
      }
      this.axios.postContralHostBase("getAssignmentDetail", params, res => {
        console.log(res, "res");
        if (res.code == 200) {
          this.overlayShow = false;
          this.form.name = res.data.assignment.assignmentName;
          this.form.situs = res.data.assignment.assignmentCode;
          this.form.assignmentTemplateName = res.data.assignment.assignmentTemplateName;
          this.form.assignmentTemplateId = res.data.assignment.assignmentTemplateId;
          this.attrList = JSON.parse(res.data.assignment.assignmentAttribute);
          this.radio = res.data.assignment.auditStatus || "";
          this.opinion = res.data.assignment.auditOpinion;
          this.taskType = res.data.assignment.taskType;
          this.approvedOrNot = res.data.approvedOrNot;
          this.getFlag();
          if (this.taskType == 2) {
            this.onDepartment(res.data.assignment.auditDeptId);
            this.form.auditDepartment = res.data.assignment.auditDeptName;
            this.form.auditDepartmentId = res.data.assignment.auditDeptId;
            this.form.auditPersonnel = res.data.assignment.auditPersonName;
            this.form.auditPersonnelId = res.data.assignment.auditPersonId;
          }
          this.signatureList = res.data.signatureList || [];
        }
      });
    },
    getFlag() {
      if (this.taskType == 1 && this.approvedOrNot == 1) {
        this.flag = false;
      } else {
        this.flag = true;
      }
    },
    //点击提交
    confirm() {
      let inf0obj = this.loginInfo;

      let params = {
        // unitCode: this.loginInfo.unitCode,
        // hospitalCode: this.loginInfo.hospitalCode,
        id: this.id || this.rowList.id,
        auditStatus: this.radio,
        auditOpinion: this.opinion,
        taskType: this.taskType,
        signature: this.sign == "base64" ? this.imgData : this.signature
      };
      if (this.approvalVersion) {
        params.approvalVersion = this.approvalVersion;
      }
      if (params.auditStatus == "") {
        return this.$toast.fail("请勾选审批结果");
      }
      this.axios.postContralHostBase(
        "auditAssignment",
        params,
        res => {
          if (res.code == 200) {
            this.$toast.success(res.message);
            this.back();
          } else {
            this.$toast.fail(res.message);
          }
        },
        inf0obj
      );
    }
  }
};
</script>

<style scoped lang="scss">
@import "../../../assets/stylus/theme";
.connent {
  padding: 0 15px;
  font-size: 14px;
  .top {
    border-left: 5px solid $main-bgColor;
    padding-left: 3px;
    color: $main-bgColor;
  }
  .remarks {
    background-color: #fff;
    margin: 0.3125rem 0 0 0;
    padding: 0.3125rem;
    border-bottom: 1px solid #f6f7f8;
    .van-field {
      height: 70px;
      overflow-y: scroll;
    }
    span {
      color: #646566;
    }
    .message {
      background-color: #f4f5f9;
      margin-top: 0.3125rem;
    }
  }
  .instructions {
    border-bottom: 4px solid #ebedf0;
  }
  .instructions .explain {
    background-color: #fff;
    min-height: 48px;
    display: flex;
    align-items: center;
    padding: 0 10px;
  }
  .instructions .explain span {
    font-size: 14px;
  }
  .bottom {
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
    width: 100%;
    height: 60px;
    padding-bottom: 15px;
    border-top: 1px solid #f6f7f8;
    position: fixed;
    left: 0;
    bottom: 0;
    background-color: #fff;
  }
}
.radio {
  padding-left: 0.3125rem;
  line-height: 35px;
}
.remarks2 {
  background-color: #fff;
  padding: 0 0.3125rem 0 0.3125rem;
  border-bottom: 1px solid #f6f7f8;
  .van-field {
    height: 70px;
    overflow-y: scroll;
  }
  span {
    color: #646566;
  }
  .message {
    background-color: #f4f5f9;
    margin-top: 0.3125rem;
  }
}
.van-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
}
.van-button {
  border-radius: 5px;
}
.van-cell {
  padding: 10px 7px !important;
}
/deep/ .van-field__label {
  width: 9.2em;
}
.my_flex {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 10px;
  justify-content: space-between;
  margin-top: 0.1875rem;
  .sign_title {
    flex: 1;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(53, 53, 53, 1);
  }
}
.signature_edit {
  width: 252px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #00cac8;

  .no_singature {
    display: inline-block;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #00cac8;
    line-height: 22px;
  }

  img {
    width: 16px;
    margin-right: 4px;
  }
}
.resign-txt {
  color: #00cac8;
  font-style: normal;
  margin: auto 0;
  text-align: center;
}
.resign {
  flex: 3;
  text-align: center;
  display: flex;
}
.sign-img {
  // width: 60px;
  height: 60px;
  margin: auto;
  // transform: rotate(90deg);
}
.is_require {
  position: absolute;
  top: 0;
  left: -8px;
  color: red;
}
/deep/ .van-icon-arrow::before {
  content: "" !important;
}
</style>
