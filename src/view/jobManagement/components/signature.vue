<template>

  <div id="wrapper" v-loading="loading">
    <div class="title">
      <span>手写签字</span>
      <span class="close" @click="closeSig">
       <van-icon name="cross" />
      </span>
    </div>
    <div class="canvas-wrapper">
      <canvas id="canvas" @touchstart="touchstart"></canvas>
      <canvas id="re-canvas" style="display:none"></canvas>
    </div>
    <div class="rewrite">
      <div class="rewrite-msg" @click="clear">
        <van-button color="#00cac8" size="large">重新书写</van-button>
      </div>
      <div class="rewrite-sure" @click="save">
        <van-button color="#00cac8" size="large">确定</van-button>
      </div>
    </div>
  </div>
</template>

<script type=text/ecmascript-6>
import wx from "weixin-js-sdk";
// import { toast } from "@/common/api/api.js";
let flag = 0;
let draw;
let preHandler = function(e) {
  e.preventDefault();
};
// width = document.documentElement.clientWidth - 40;
// height = document.documentElement.clientHeight * 0.5;
class Draw {
  constructor(el) {
    this.el = el;
    this.canvas = document.getElementById(this.el);
    this.canvas.width = window.innerWidth;
    this.canvas.height = window.innerHeight;
    this.cxt = this.canvas.getContext("2d");
    this.stage_info = canvas.getBoundingClientRect();
    this.path = {
      beginX: 0,
      beginY: 0,
      endX: 0,
      endY: 0
    };
  }
  init(btn) {
    let that = this;

    this.canvas.addEventListener("touchstart", function(event) {
      document.addEventListener("touchstart", preHandler, true);
      that.drawBegin(event);
    });
    this.canvas.addEventListener("touchend", function(event) {
      document.addEventListener("touchend", preHandler, true);
      that.drawEnd();
    });
    this.clear(btn);
  }
  isDrawFlag() {
    return flag;
  }
  drawBegin(e) {
    flag = 1;
    let that = this;
    window.getSelection()
      ? window.getSelection().removeAllRanges()
      : document.selection.empty();
    this.cxt.strokeStyle = "#000";
    this.cxt.lineWidth = 2;//线条的宽度
    this.cxt.beginPath();
    this.cxt.moveTo(
      e.changedTouches[0].clientX - this.stage_info.left,
      e.changedTouches[0].clientY - this.stage_info.top
    );
    this.path.beginX = e.changedTouches[0].clientX - this.stage_info.left;
    this.path.beginY = e.changedTouches[0].clientY - this.stage_info.top;
    canvas.addEventListener("touchmove", function() {
      that.drawing(event);
    });
  }
  drawing(e) {
    this.cxt.lineTo(
      e.changedTouches[0].clientX - this.stage_info.left,
      e.changedTouches[0].clientY - this.stage_info.top
    );
    this.path.endX = e.changedTouches[0].clientX - this.stage_info.left;
    this.path.endY = e.changedTouches[0].clientY - this.stage_info.top;
    this.cxt.stroke();
  }
  drawEnd() {
    document.removeEventListener("touchstart", preHandler, true);
    document.removeEventListener("touchend", preHandler, true);
    document.removeEventListener("touchmove", preHandler, true);
  }
  clear(btn) {
    this.cxt.clearRect(0, 0, "414", "800");
    flag = 0;
  }

  //保存图片并添加背景色
  save(backgroundColor) {
    let w = canvas.width;
    let h = canvas.height;
    let data;
    if (backgroundColor) {
      let context = canvas.getContext("2d");
      data = context.getImageData(0, 0, w, h);
      let compositeOperation = context.globalCompositeOperation;
      context.globalCompositeOperation = "destination-over";
      context.fillStyle = backgroundColor;
      context.fillRect(0, 0, w, h);
    }

    // 获取canvas的边界
    let ctx = this.canvas.getContext("2d");
    let imgData = ctx.getImageData(0, 0, w, h).data;

    let lOffset = canvas.width, rOffset = 0,tOffset = canvas.height, bOffset = 0;

    for (let i = 0; i < canvas.width; i++) {
        for (let j = 0; j < canvas.height; j++) {
            let pos = (i + canvas.width * j) * 4
            if (imgData[pos] > 0 || imgData[pos + 1] > 0 || imgData[pos + 2] || imgData[pos + 3] > 0) {
                // 说第j行第i列的像素不是透明的
                // 楼主貌似底图是有背景色的,所以具体判断RGBA的值可以根据是否等于背景色的值来判断
                bOffset = Math.max(j, bOffset); // 找到有色彩的最底部的纵坐标
                rOffset = Math.max(i, rOffset); // 找到有色彩的最右端

                tOffset = Math.min(j, tOffset); // 找到有色彩的最上端
                lOffset = Math.min(i, lOffset); // 找到有色彩的最左端
            }
        }
    }
    // 由于循环是从0开始的,而我们认为的行列是从1开始的
    lOffset++;
    rOffset++;
    tOffset++;
    bOffset++;
    console.log('canvas边界',lOffset, rOffset, tOffset, bOffset);

    let sx=lOffset;
    let sy=tOffset;
    let sw=rOffset-lOffset;
    let sh=bOffset-tOffset;

    let actualImg=ctx.getImageData(sx, sy, sw, sh);

    let reCanvas = document.getElementById('re-canvas');
    reCanvas.width = rOffset-lOffset;
    reCanvas.height = bOffset-tOffset;
    let reCxt = reCanvas.getContext("2d");
    reCxt.putImageData(actualImg,0,0);

    let imageData = reCanvas.toDataURL("image/png");
    // let arr = imageData.split(","),
    //   mime = arr[0].match(/:(.*?);/)[1],
    //   bstr = atob(arr[1]),
    //   n = bstr.length,
    //   u8arr = new Uint8Array(n);
    // while (n--) {
    //   u8arr[n] = bstr.charCodeAt(n);
    // }
    return imageData
    //new File([u8arr], "img.png", { type: mime });
  }
}
export default {
  name: "signature",
  data(){
    return {
      loading:false
    }
  },
  props: {
    btnLabel: {
      type: String,
      default: "确认领取并签名"
    },
    status: {
      type: String,
      default: "0"
    }
  },
  mounted() {
    draw = new Draw("canvas");
    draw.init();
    //解决微信浏览器下拉露底的问题
    document.getElementById("wrapper").addEventListener(
      "touchmove",
      e => {
        e.preventDefault();
      },
      { passive: false }
    );
  },
  methods: {
    touchstart(e) {

    },


    /**
     * 清空画布
     */
    clear: function() {
      draw.clear();
    },
    closeSig() {
      this.$emit("closePop");
      // this.$router.go(-1);
    },
    /**
     * 保存图片
     */
    save: function() {
      if (!this.status) {
        // toast(this, "无法领取，请等待分会管理员确认");
        return;
      }
      this.loading = true;
      let drawFlag = draw.isDrawFlag();
      let data;
      let objloginInfo;
      let objstaffInfo;
      if (drawFlag == 1) {
        //在画布上绘画了
        data = draw.save();
        this.$emit("saveImg", data);
      }
      if (drawFlag == 0) {
        //没在画布上绘画
        this.$emit("saveImg", "");
      }
      setTimeout(() => {
        this.loading = false
      },10);
    }
  }
};
</script>
<style lang="scss" scoped>
#wrapper {
  width: 100%;
  background: rgba(255, 255, 255, 1);
  height: 100vh;
  .title {
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #efeff4;
    position: absolute;
    width: 100%;
    top: 0;
    // transform: rotate(270deg);
    // left: -44.5%;
    .close {
      float: right;
      font-size: 20px;
      padding-right: 5px;
    }
  }

  .canvas-wrapper {
    background: #fff;
  }

  .rewrite {
    width: 100%;
    background: #fff;
    display: flex;
    position: absolute;
    // transform: rotate(270deg);
    bottom: 0;
    // right: -43%;
    .rewrite-msg {
      flex: 1;
      padding: 10px;
    }
    .rewrite-sure {
      flex: 1;
      padding: 10px;
    }
  }
}
</style>
