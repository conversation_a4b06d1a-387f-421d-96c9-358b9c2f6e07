<template>
  <div class="connent">
    <div class="top">基本信息</div>
    <van-form>
      <van-field readonly v-model.trim="form.name" label="危险作业名称" placeholder="请输入危险作业名称" :rules="[{ required: true }]" />
      <van-field readonly v-model.trim="form.situs" label="作业任务编号" placeholder="请输入作业任务编号" :rules="[{ required: true }]" />
      <van-field readonly clickable v-model="form.assignmentTemplateName" label="选择作业票" placeholder="请选择作业票" right-icon="arrow" :rules="[{ required: true }]" />
      <div class="top">作业申请信息</div>
      <div v-for="item in attrList" :key="item.attrId">
        <van-field
          readonly
          v-if="item.attrType == 'TEXT' || item.attrType == 'TEXTAREA'"
          v-model.trim="item.attrValue"
          :label="item.attrName"
          :placeholder="item.attrPlaceholder"
        />
        <van-field v-if="item.attrType == 'DATE'" readonly clickable v-model="item.attrValue" :label="item.attrName" :placeholder="item.attrPlaceholder" right-icon="arrow" />
      </div>
      <div class="top">审核信息</div>
      <el-table border :data="signatureList" stripe style="width: 100%; margin: 10px 0" v-if="signatureList">
        <el-table-column prop="auditPersonName" label="审批人" min-width="70" align="center"> </el-table-column>
        <el-table-column prop="auditTime" label="审批日期" width="170" align="center"> </el-table-column>
        <el-table-column prop="auditStatus" label="审批结果" min-width="80" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.auditStatus == 1 ? "通过" : "驳回" }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div v-else>
        <van-field readonly clickable v-model="form.auditDepartment" label="审核部门" placeholder="请选择审核部门" right-icon="arrow" />
        <van-popup v-model="showDepartment" position="bottom" v-if="this.rowList != undefined">
          <van-picker value-key="teamName" show-toolbar :columns="typeDepartment" @confirm="onDepartment" @cancel="showDepartment = false" />
        </van-popup>
        <van-field readonly clickable v-model="form.auditPersonnel" label="审核人员" placeholder="请选择审核人员" right-icon="arrow" />
      </div>
    </van-form>
    <div v-if="!signatureList">
      <div class="radio">
        <van-radio-group v-model="radio" direction="horizontal">
          审核结果
          <van-radio name="1" disabled>通过</van-radio>
          <van-radio name="2" disabled>驳回</van-radio>
        </van-radio-group>
      </div>
      <div class="remarks2">
        <span>审核意见</span>
        <van-field readonly v-model="opinion" rows="1" autosize type="textarea" maxlength="130" placeholder="请输入审核意见" show-word-limit class="message" />
      </div>
    </div>
    <div class="top">作业记录</div>
    <van-field readonly clickable v-model="form.StartTime" label="实际作业开始时间" placeholder="" right-icon="arrow" />
    <van-field readonly clickable v-model="form.EndTime" label="实际作业结束时间" placeholder="" right-icon="arrow" />
    <van-field readonly v-model.trim="form.Status" label="作业状态" placeholder="" />
    <div v-if="rowList.accidentStatusName == '已调查处理'">
      <div class="top">事故调查处理信息</div>
      <div class="radio">
        <van-radio-group v-model="radio1" class="lh">
          事故调查处理类型：
          <van-radio name="1" disabled>单位内部调查处理</van-radio>
          <van-radio name="2" disabled c>单位外部调查</van-radio>
        </van-radio-group>
      </div>
      <div class="top">调查人员信息</div>
      <div class="number">
        <div v-for="(item, index) in form.information" :key="index" class="information">
          <div>
            姓名<span> {{ item.name }}</span>
          </div>
          <div>
            职务<span> {{ item.job }}</span>
          </div>
          <div>
            单位/部门<span> {{ item.department }}</span>
          </div>
          <div>
            联系方式<span> {{ item.phone }}</span>
          </div>
        </div>
      </div>
      <div class="top">调查处理结果</div>
    </div>
    <div class="bottom">
      <!-- <van-button
        style="background-color: #00cac8; color: #fff; width: 30%"
        @click="confirm"
        >提交审核</van-button
      > -->
      <van-button style="background-color: #00cac8; color: #fff; width: 25%" @click="back">返回</van-button>
    </div>
  </div>
</template>

<script>
import moment from "moment";
export default {
  props: {
    rowList: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      signatureList: [],
      form: {
        name: "", //事故名称
        situs: "", //事故地点
        time: "", //事故时间
        department: "", //责任部门
        departmentId: "", //责任部门ID
        accidentSfter: "", //简要经过
        accidentCasualty: "", //伤亡
        troubleRemoval: "", //处理
        takeSteps: "", //措施
        fileList: [],
        auditDepartment: "", //审核部门
        auditDepartmentId: "", //审核部门ID
        auditPersonnel: "", //审核人员
        auditPersonnelId: "", //审核人员ID
        attachmentUrl: [], //附件
        ///////////
        information: [], //调查人员信息
        type: "", //事故类型
        grade: "", //事故等级
        deathToll: "", //死亡人数
        seriousInjury: "", //重伤人数
        minorInjuries: "", //轻伤人数
        missingNumber: "", //失踪人数
        directDamage: "", //直接损失
        indirectloss: "", //间接损失
        through: "", //事故发生经过
        principal: "", //主要负责人
        secondary: "", //次要负责人
        dispositionOpinions: "", //处置意见
        preventiveAdvice: "", //防范建议
        suggest: "", //措施建议
        fileList2: [],
        StartTime: "", //作业开始时间
        EndTime: "", //作业结束时间
        Status: "" //作业状态
      },
      showType: false,
      showTypeChildren: false,
      showDepartment: false,
      showPersonnel: false,
      typeDepartment: [], //审核部门数据
      radio: "",
      opinion: "", //审核意见
      radio1: "",
      overlayShow: false,
      attrList: []
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.detail();
  },
  methods: {
    //审核部门
    onDepartment(val) {
      this.form.auditDepartment = val.teamName;
      this.form.auditDepartmentId = val.id;
      this.showDepartment = false;
      let params = {
        platformFlag: 2,
        roleCode: this.loginInfo.roleCode,
        departId: val.id
      };
      this.axios.postContralHostBase("getUseListByDeptId", params, res => {
        if (res.code == 200) {
          this.typePersonnel = res.data;
        }
      });
    },
    //返回
    back() {
      this.$emit("button", false);
    },
    //获取详情
    detail() {
      this.overlayShow = true;
      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        id: this.rowList.id,
      };
      this.loading = true;
      this.axios.postContralHostBase("getAssignmentDetail", params, res => {
        this.overlayShow = false;
        if (res.code == 200) {
          this.form.name = res.data.assignment.assignmentName;
          this.form.situs = res.data.assignment.assignmentCode;
          this.form.assignmentTemplateName = res.data.assignment.assignmentTemplateName;
          this.form.assignmentTemplateId = res.data.assignment.assignmentTemplateId;
          this.attrList = JSON.parse(res.data.assignment.assignmentAttribute);
          this.onDepartment(res.data.assignment.auditDeptId);
          this.form.auditDepartment = res.data.assignment.auditDeptName;
          this.form.auditDepartmentId = res.data.assignment.auditDeptId;
          this.form.auditPersonnel = res.data.assignment.auditPersonName;
          this.form.auditPersonnelId = res.data.assignment.auditPersonId;
          this.radio = res.data.assignment.auditStatus;
          this.opinion = res.data.assignment.auditOpinion;
          this.form.StartTime = res.data.assignment.assignmentStartTime;
          this.form.EndTime = res.data.assignment.assignmentEndTime;
          this.form.Status = res.data.assignment.assignmentStatusName;
          this.signatureList = res.data.signatureList;
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
@import "../../../assets/stylus/theme";
.connent {
  padding: 0 15px;
  font-size: 14px;

  .top {
    border-left: 5px solid $main-bgColor;
    padding-left: 3px;
    color: $main-bgColor;
  }
  .remarks {
    background-color: #fff;
    margin: 0.3125rem 0 0 0;
    padding: 0.3125rem;
    border-bottom: 1px solid #f6f7f8;
    .message {
      background-color: #f4f5f9;
      margin-top: 0.3125rem;
    }
  }
  .instructions {
    border-bottom: 4px solid #ebedf0;
  }
  .instructions .explain {
    background-color: #fff;
    min-height: 48px;
    display: flex;
    align-items: center;
    padding: 0 10px;
  }
  .instructions .explain span {
    font-size: 14px;
  }
  .bottom {
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
    width: 100%;
    height: 60px;
    padding-bottom: 15px;
    border-top: 1px solid #f6f7f8;
    position: fixed;
    left: 0;
    bottom: 0;
    background-color: #fff;
  }
}
.radio {
  line-height: 35px;
  color: #646566;
  div {
    padding-left: 7px;
  }
}
.radio1 {
  padding: 0.3125rem 0;
  color: #646566;
  div {
    padding-left: 8px;
  }
  .van-radio {
    padding: 0 !important;
  }
}
.remarks2 {
  background-color: #fff;
  padding: 0.3125rem;
  border-bottom: 1px solid #f6f7f8;
  .van-field {
    height: 70px;
    overflow-y: scroll;
  }
  span {
    color: #646566;
  }
  .message {
    background-color: #f4f5f9;
    margin-top: 0.3125rem;
  }
}
/deep/ .van-field__label {
  width: 9.2em;
}
.information {
  padding: 10px 10px 10px 17px;
  div {
    line-height: 40px;
    border-bottom: 1px solid #f6f7f8;
    span {
      color: #000;
    }
  }
}
.lh {
  line-height: 15px;
  padding-left: 8px;
  /deep/ .van-radio {
    margin-top: 10px;
  }
}
.van-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
}
.van-button {
  border-radius: 5px;
}
.van-cell {
  padding: 10px 7px !important;
}
.number {
  height: 185px;
  overflow-y: scroll;
}
/deep/ .van-field__label {
  width: 9.2em;
}
</style>
