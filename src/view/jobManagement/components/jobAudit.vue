<template>
  <div>
    <van-pull-refresh v-model="overlayShow" @refresh="onRefresh" v-if="!showHidden">
      <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoads" :immediate-check="false" :offset="5">
        <div>
          <div v-for="item in accidentList" :key="item.id" class="list" @click="accident(item)">
            <div class="apellation">
              <i style="color: #00cac8">{{ item.assignmentName }}</i
              ><i>{{ item.assignmentStatusName }}</i>
            </div>
            <div class="txt">
              <div>
                作业类型：<span>{{ item.assignmentTemplateName }}</span>
              </div>
              <div>
                申请部门：<span>{{ item.createDeptName }}</span>
              </div>

              <div>
                申请时间：<span>{{ item.createTime }}</span>
              </div>
              <div>
                作业区域：<span class="scene">{{ item.assignmentPlace }}</span>
              </div>
            </div>
          </div>
          <!-- <div class="txet" v-if="accidentList.length < total" @click="loadMore">
      点击加载更多
    </div>
    <div class="txet" v-if="noMore">没有更多了</div> -->
          <!-- <div class="blank" v-if="accidentList.length == '0'">
      <img src="../../../assets/images/empty.png" alt />
    </div> -->
        </div>
      </van-list>
    </van-pull-refresh>
    <subclass2 v-if="showHidden" :rowList="rowList" v-on:button="button($event)" class="top1" :id="id" :approvalVersion="approvalVersion"></subclass2>
  </div>
</template>

<script>
import subclass2 from "./subclass2.vue";
export default {
  components: {
    subclass2,
  },
  props: {
    id: {
      type: String,
      default: "",
    },
    approvalVersion: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      pageSize: 5,
      pageNo: 1,
      total: 0,
      accidentList: [],
      noMore: false,
      index: 0,
      showHidden: false,
      rowList: {},
      overlayShow: false,
      loading: true,
      finished: false,
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    console.log(this.approvalVersion, "approvalVersion");
    console.log(this.id, "id");

    this.onLoad();
    if (this.id) {
      this.showHidden = true;
    }
  },
  // watch: {
  //   index(o, l) {
  //     this.pageNo = 1;
  //     (this.list = []), this.onLoad();
  //   },
  // },
  methods: {
    onLoads() {
      setTimeout(() => {
        this.pageNo++;
        this.onLoad();
      }, 500);
    },
    onRefresh() {
      setTimeout(() => {
        this.pageNo = 1;
        this.accidentList = [];
        this.finished = false;
        this.loading = true;
        this.onLoad();
      }, 500);
    },
    button(val) {
      this.showHidden = val;
      this.pageNo = 1;
      this.accidentList = [];
      this.onLoad();
    },
    accident(row) {
      this.showHidden = true;
      this.rowList = row;
      this.id = row.id;
    },
    // loadMore() {
    //   this.pageNo++;
    //   this.onLoad();
    // },
    onLoad() {
      this.overlayShow = true;
      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        pageSize: this.pageSize,
        pageNo: this.pageNo,
        bussType: 2,
      };
      this.axios.postContralHostBase("getAuditTabulation", params, (res) => {
        this.loading = false;

        if (res.code == 200) {
          this.overlayShow = false;
          res.data.list.forEach((item) => {
            this.accidentList.push(item);
          });
          if (this.accidentList.length >= res.data.sum) {
            this.finished = true;
          }
        } else if (res.code == 500) {
          this.overlayShow = false;
          this.finished = true;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
@import "../../../assets/stylus/theme";
.list {
  font-size: 16px;
  // padding: 0 15px;

  margin-bottom: 5px;
  .txt {
    padding: 0 20px;
    border-bottom: 5px solid #e6eaf0;
  }
  .apellation {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #d7d1d1;
    padding: 0 10px;

    i:nth-child(1) {
      display: inline-block;
      width: 100px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    i:nth-child(2) {
      display: inline-block;
      color: $main-bgColor;
    }
  }
  div {
    line-height: 35px;
    font-size: 15px;
  }
  span {
    color: #a7a2a5;
  }
  i {
    font-style: normal;
  }
  .scene {
    display: inline-block;
    width: 200px;
    vertical-align: middle;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
.txet {
  text-align: center;
  font-size: 14px;
  color: #a7a2a5;
  padding-bottom: 15px;
}
.blank {
  text-align: center;
  img {
    width: 100%;
    height: 100%;
  }
}
.van-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
}
.top1 {
  // margin-top: 40px;
  margin-bottom: 70px;
}
/deep/ .van-list__finished-text {
  margin-top: 10px;
  line-height: 15px !important;
}
</style>
