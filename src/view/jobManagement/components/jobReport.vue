<template>
  <div>
    <van-tabs v-model="active" type="card"  :class="active==0?'top':'top1'" 
     offset-top="3rem"
      >
      <van-tab v-for="item in tasksList" :title="item.dictName" :key="item.id" >
      </van-tab>
    </van-tabs>
    <div>
        <jobReportLevel v-if="active == 0"></jobReportLevel>
        <myReport v-if="active == 1"></myReport>

    </div>
  </div>
</template>

<script>
import jobReportLevel from "./jobReportLevel.vue";
import myReport from "./myReport.vue";

export default {
  components: {
    jobReportLevel,
    myReport
  },
  data() {
    return {
      active: 0,
           tasksList:[
        {
          id:0,
          dictName:"作业申请"
        },
        {
          id:1,
          dictName:"我的申请"
        },
      ]
    };
  },
};
</script>

<style scoped lang="scss">
@import "../../../assets/stylus/theme";
/deep/ .van-tabs__nav--card {
  border: 1px solid $main-bgColor !important;
  margin: 0px 30.5%;
  font-size: 10px !important;
  height: 30px;

}
/deep/ .van-tabs__nav--card .van-tab.van-tab--active {
  background-color: $main-bgColor;
  color: #fff;
}
/deep/ .van-tabs__nav--card .van-tab {
  color: $main-bgColor;
  border-right: 1px solid $main-bgColor;
}
.top{
   position: fixed;
  width: 100%;
 z-index: 99;
  background-color: #fff;
  margin-top: -60px;

// margin-top: 5px;
}
.top1{
   position: fixed;
  width: 100%;
 z-index: 99;
  background-color: #fff;
  margin-top: -50px;


}
</style>