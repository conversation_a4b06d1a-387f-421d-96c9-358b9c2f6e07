<template>
  <div>
    <div class="risk_classify" v-if="!showHidden">
      <div
        class="risk_item bgc border"
        :class="num == item.id ? 'set_fong_weight' : ''"
        v-for="item in list"
        :key="item.id"
        @click="sty(item.id)"
      >
        <i class="risk_item">{{ item.num }}</i
        ><span>{{ item.name }}</span>
      </div>
    </div>
    <div class="top" v-if="!showHidden"></div>
    <van-pull-refresh
      v-model="overlayShow"
      @refresh="onRefresh"
      v-if="!showHidden"
    >
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoads"
        :immediate-check="false"
        :offset="5"
      >
        <div>
          <div
            v-for="item in accidentList"
            :key="item.id"
            class="list"
            @click="accident(item)"
          >
            <div class="apellation">
              <i style="color: #00cac8">{{ item.assignmentName }}</i
              ><i>{{ item.assignmentStatusName }}</i>
            </div>
            <div class="txt">
              <div>
                作业类型：<span>{{ item.assignmentTemplateName }}</span>
              </div>
              <div>
                申请部门：<span>{{ item.createDeptName }}</span>
              </div>

              <div>
                申请时间：<span>{{ item.createTime }}</span>
              </div>
              <div>
                作业区域：<span class="scene">{{ item.assignmentPlace }}</span>
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
    <subclass3
      v-if="showHidden"
      :rowList="rowList"
      v-on:button="button($event)"
      class="top1"
    ></subclass3>
  </div>
</template>

<script>
import subclass3 from "./subclass3.vue";
export default {
  components: {
    subclass3,
  },
  data() {
    return {
      pageSize: 5,
      pageNo: 1,
      total: 0,
      accidentList: [],
      noMore: false,
      index: 0,
      showHidden: false,
      rowList: {},
      overlayShow: false,
      loading: true,
      finished: false,
      list: [],
      num: "",
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.onLoad();
    this.getStatu();
  },

  methods: {
    sty(index) {
      this.num = index;
      console.log(index);
      this.pageNo = 1;
      this.finished = false;
      this.loading = true;
      this.accidentList = [];
      this.onLoad();
    },
    onLoads() {
      setTimeout(() => {
        this.pageNo++;
        this.onLoad();
      }, 500);
    },
    onRefresh() {
      setTimeout(() => {
        this.pageNo = 1;
        this.accidentList = [];
        this.finished = false;
        this.loading = true;
        this.getStatu()
        this.onLoad();
      }, 500);
    },
    button(val) {
      this.showHidden = val;
      this.pageNo = 1;
      this.accidentList = [];
      this.onLoad();
    },
    accident(row) {
      this.showHidden = true;
      this.rowList = row;
    },
    loadMore() {
      this.pageNo++;
      this.onLoad();
    },
    getStatu() {
      this.axios.postContralHostBase("getStatusNumber", {}, (res) => {
        if (res.code == 200) {
          (this.list = [
            {
              id: "",
              name: "全部",
            },
            {
              id: 4,
              name: "未开始",
            },
            {
              id: 5,
              name: "进行中",
            },
            {
              id: 6,
              name: "已结束",
            },
          ]),
            (this.list[0].num = res.data.totalNum);
          this.list[1].num = res.data.wksNum;
          this.list[2].num = res.data.jxzNum;
          this.list[3].num = res.data.yjsNum;
        }
      });
    },
    onLoad() {
      this.overlayShow = true;
      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        pageSize: this.pageSize,
        pageNo: this.pageNo,
        bussType: 3,
        status: this.num,
      };
      this.axios.postContralHostBase("assignmentListData", params, (res) => {
        this.loading = false;

        if (res.code == 200) {
          this.overlayShow = false;
          res.data.list.forEach((item) => {
            this.accidentList.push(item);
          });
          if (this.accidentList.length >= res.data.total) {
            this.finished = true;
          }
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
@import "../../../assets/stylus/theme";
.list {
  font-size: 16px;
  // padding: 0 15px;

  margin-bottom: 5px;
  .txt {
    padding: 0 20px;
    border-bottom: 5px solid #e6eaf0;
  }
  .apellation {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #d7d1d1;
    padding: 0 10px;

    i:nth-child(1) {
      display: inline-block;
      width: 100px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    i:nth-child(2) {
      display: inline-block;
      color: $main-bgColor;
    }
  }
  div {
    line-height: 35px;
    font-size: 15px;
  }
  span {
    color: #a7a2a5;
  }
  i {
    font-style: normal;
  }
  .scene {
    display: inline-block;
    width: 200px;
    vertical-align: middle;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
.txet {
  text-align: center;
  font-size: 14px;
  color: #a7a2a5;
  padding-bottom: 15px;
}
.blank {
  text-align: center;
  img {
    width: 100%;
    height: 100%;
  }
}
.van-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
}
.top1 {
  margin-top: 8px;
  margin-bottom: 70px;
}
.top {
  height: 80px;
}
.risk_classify {
  position: fixed;
  //  top: 109px;
  width: 100%;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /*padding: 5px;*/
  color: #fff;
  font-size: 12px;

  border-bottom: 4px solid #e6eaf0;
}
// .risk_classify div:nth-child(1) i{
// color: red;
// }
.risk_classify div:nth-child(2) i {
  color: #ff0000;
}
.risk_classify div:nth-child(3) i {
  color: #ff6100;
}
.risk_classify div:nth-child(4) i {
  color: #ecdf51;
}
.risk_item span {
  color: #353535;
  font-size: 13px;
  font-weight: 500;
}
.bgc {
  color: #353535;
  /*background-color: #2ac7c1*/
}

.set_fong_weight {
  background: #ebf9f9 !important;
  border: 1px solid #5bc7c6 !important;
}

.set_fong_weight::before {
  border-color: #5bc7c6 !important;
}

i {
  font-style: normal;
}

.risk_classify > div {
  height: 73px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  background-color: #fff;
  font-size: 13px;
}
.border {
  border: 1px solid #e6eaf0;
}
/deep/ .van-list__finished-text {
  line-height: 15px !important;
}
</style>