<template>
  <div class="connent">
    <div class="top">基本信息</div>
    <van-form ref="form">
      <van-field
        required
        :readonly="this.rowList.assignmentStatus == '2' || this.rowList.assignmentStatus == '4' || this.rowList.assignmentStatus == '5' || this.rowList.assignmentStatus == '6'"
        v-model.trim="form.name"
        label="危险作业名称"
        placeholder="请输入危险作业名称"
        :rules="[{ required: true }]"
      />
      <van-field
        :readonly="this.rowList.assignmentStatus == '2' || this.rowList.assignmentStatus == '4' || this.rowList.assignmentStatus == '5' || this.rowList.assignmentStatus == '6'"
        v-model.trim="form.situs"
        label="作业任务编号"
        placeholder="请输入作业任务编号"
      />
      <!-- <van-field
        readonly
        clickable
        v-model="form.time"
        label="事故发生时间"
        placeholder="请选择事故发生时间"
        right-icon="arrow"
        @click="claimClassification()"
        :rules="[{ required: true }]"
      />
      <van-popup
        v-model="showType"
        position="bottom"
        v-if="
          this.rowList != undefined &&
          (this.rowList.assignmentStatusName == '待提交' ||
            this.rowList.assignmentStatusName == '审核驳回')
        "
      >
        <van-datetime-picker
          type="datetime"
          title="选择完整时间"
          @confirm="onType"
          @cancel="showType = false"
        />
      </van-popup> -->
      <van-field
        required
        readonly
        clickable
        v-model="form.assignmentTemplateName"
        label="选择作业票"
        placeholder="请选择作业票"
        right-icon="arrow"
        @click="claimDepartment()"
        :rules="[{ required: true }]"
      />
      <van-popup v-model="showTypeChildren" position="bottom" v-if="this.rowList.assignmentStatus == '1' || this.rowList.assignmentStatus == '3'">
        <van-picker value-key="assignmentName" show-toolbar :columns="typeChildrenList" @confirm="onTypeChildren" @cancel="showTypeChildren = false" />
      </van-popup>
      <div class="top">作业申请信息</div>
      <div v-for="item in attrList" :key="item.attrId">
        <van-field
          :readonly="assignmentStatus == '2'"
          :required="item.attrField == 'assignmentRegion'"
          :rules="[
            item.attrField == 'assignmentRegion'
              ? { required: true }
              : '' || item.attrField == 'responsibleMobile'
              ? {
                  validator: validatorP,
                  message: val => {
                    return val.includes('-') ? '座机号码格式错误！' : '手机号码格式错误！';
                  }
                }
              : ''
          ]"
          v-if="item.attrType == 'TEXT' || item.attrType == 'TEXTAREA'"
          v-model.trim="item.attrValue"
          :label="item.attrName"
          :placeholder="item.attrPlaceholder"
        />
        <van-field
          required
          v-if="item.attrType == 'DATE'"
          readonly
          clickable
          v-model="item.attrValue"
          :label="item.attrName"
          :placeholder="item.attrPlaceholder"
          right-icon="arrow"
          @click="dateClick(item)"
        />
        <van-popup v-if="item.attrType == 'DATE' && assignmentStatus != '2'" v-model="item.showType" position="bottom">
          <van-datetime-picker type="datetime" title="选择完整时间" @confirm="onType2($event, item)" @cancel="cancelClick(item)" />
        </van-popup>
      </div>
      <div class="top">审核信息</div>
      <el-table border :data="signatureList" stripe style="width: 100%; margin: 10px 0" v-if="taskType == '1'">
        <el-table-column prop="auditPersonName" label="审批人" min-width="70" align="center"> </el-table-column>
        <el-table-column prop="auditTime" label="审批日期" width="170" align="center"> </el-table-column>
        <el-table-column prop="auditStatus" label="审批结果" min-width="80" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.auditStatus == 1 ? "通过" : "驳回" }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div v-else>
        <van-field
          required
          readonly
          clickable
          v-model="form.auditDepartment"
          label="审核部门"
          placeholder="请选择审核部门"
          right-icon="arrow"
          @click="claimauditDepartment()"
          :rules="[{ required: true }]"
        />
        <van-popup v-model="showDepartment" position="bottom" v-if="this.rowList.assignmentStatus == '1' || this.rowList.assignmentStatus == '3'">
          <van-picker value-key="teamName" show-toolbar :columns="typeDepartment" @confirm="onDepartment" @cancel="showDepartment = false" />
        </van-popup>
        <van-field
          required
          readonly
          clickable
          v-model="form.auditPersonnel"
          label="审核人员"
          placeholder="请选择审核人员"
          right-icon="arrow"
          @click="claimPersonnel()"
          :rules="[{ required: true }]"
        />
        <van-popup v-model="showPersonnel" position="bottom" v-if="this.rowList.assignmentStatus == '1' || this.rowList.assignmentStatus == '3'">
          <van-picker value-key="name" show-toolbar :columns="typePersonnel" @confirm="onPersonnel" @cancel="showPersonnel = false" />
        </van-popup>
      </div>
    </van-form>
    <div v-if="taskType == '2'">
      <div class="top">审核意见</div>
      <div class="radio">
        <van-radio-group v-model="radio" direction="horizontal">
          <span style="color: #646566">审核结果</span>&nbsp;&nbsp;
          <van-radio name="1" icon-size="16px" disabled>通过</van-radio>
          <van-radio name="2" icon-size="16px" disabled>驳回</van-radio>
        </van-radio-group>
      </div>
      <div class="remarks2">
        <span>审核意见</span>
        <van-field disabled v-model="opinion" rows="1" autosize type="textarea" maxlength="130" placeholder="请输入审核意见" show-word-limit class="message" />
      </div>
    </div>
    <div class="bottom">
      <van-button v-if="this.rowList.assignmentStatus == '1' || this.rowList.assignmentStatus == '3'" style="background-color: #00cac8; color: #fff; width: 30%" @click="confirm"
        >提交审核</van-button
      >
      <van-button
        v-if="this.rowList.assignmentStatus == '1' || this.rowList.assignmentStatus == '2' || this.rowList.assignmentStatus == '3' || this.rowList.assignmentStatus == '6'"
        style="background-color: #00cac8; color: #fff; width: 25%"
        @click="dele"
        >删除</van-button
      >
      <van-button style="background-color: #00cac8; color: #fff; width: 25%" @click="back">返回</van-button>
    </div>
  </div>
</template>

<script>
import moment from "moment";
export default {
  props: {
    rowList: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      flag: true,
      approvedOrNot: "",
      signatureList: [],
      form: {
        name: "", //事故名称
        situs: "", //事故地点
        time: "", //事故时间
        department: "", //责任部门
        departmentId: "", //责任部门ID
        accidentSfter: "", //简要经过
        accidentCasualty: "", //伤亡
        troubleRemoval: "", //处理
        takeSteps: "", //措施
        fileList: [],
        auditDepartment: "", //审核部门
        auditDepartmentId: "", //审核部门ID
        auditPersonnel: "", //审核人员
        auditPersonnelId: "", //审核人员ID
        attachmentUrl: [] //附件
      },
      taskType: "",
      showType: false,
      showTypeChildren: false,
      showDepartment: false,
      showPersonnel: false,
      currentDate: "",
      typeChildrenList: [], //责任部门数据
      typeDepartment: [], //审核部门数据
      typePersonnel: [], //审核人员数据
      maxSize: 10,
      overlayShow: false,
      attrList: [],
      radio: "",
      opinion: "", //审核意见
      assignmentStatus: ""
    };
  },
  created() {
    console.log(this.rowList, "this.rowList");
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.typeChildrenLists();
    this.gettingData();
    if (this.rowList != undefined) {
      this.detail();
    } else {
      return;
    }
  },
  methods: {
    validatorP(val) {
      if (val != "") {
        return /^[1][3,4,5,7,8,9][0-9]{9}$/.test(val) || (/^\d+-\d+$/.test(String(val)) && val.length <= 16); //前面手机号，后面座机号，输入框限定16位字符
      }
    },
    //删除
    dele() {
      this.$dialog
        .confirm({
          message: "确认删除该作业信息？"
        })
        .then(() => {
          let params = {
            unitCode: this.loginInfo.unitCode,
            hospitalCode: this.loginInfo.hospitalCode,
            id: this.rowList.id,
            taskType: this.taskType
          };
          this.axios.postContralHostBase("deleteAssignment", params, res => {
            if (res.code == 200) {
              this.$toast.success(res.message);
              this.$emit("button", false);
            }
          });
        })
        .catch(() => {
          this.$message({
            message: "已取消"
          });
        });
    },
    //返回
    back() {
      this.$emit("button", false);
    },
    //获取详情
    detail() {
      this.overlayShow = true;
      let params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        id: this.rowList.id
      };
      this.axios.postContralHostBase("getAssignmentDetail", params, res => {
        this.overlayShow = false;
        if (res.code == 200) {
          this.form.name = res.data.assignment.assignmentName;
          this.form.situs = res.data.assignment.assignmentCode;
          this.form.assignmentTemplateName = res.data.assignment.assignmentTemplateName;
          this.form.assignmentTemplateId = res.data.assignment.assignmentTemplateId;
          this.attrList = JSON.parse(res.data.assignment.assignmentAttribute);
          console.log(this.attrList, " this.attrList");
          this.radio = res.data.assignment.auditStatus;
          this.opinion = res.data.assignment.auditOpinion;
          this.taskType = res.data.assignment.taskType;
          this.assignmentStatus = res.data.assignment.assignmentStatus;
          this.approvedOrNot = res.data.approvedOrNot;
          // this.getFlag();
          if (this.taskType == 2) {
            this.onDepartment(res.data.assignment.auditDeptId);
            this.form.auditDepartment = res.data.assignment.auditDeptName;
            this.form.auditDepartmentId = res.data.assignment.auditDeptId;
            this.form.auditPersonnel = res.data.assignment.auditPersonName;
            this.form.auditPersonnelId = res.data.assignment.auditPersonId;
          }
          this.signatureList = res.data.signatureList || [];
        }
      });
    },
    //点击提交
    confirm() {
      this.$refs.form.validate().then(() => {
        let inf0obj = this.loginInfo;

        let params = {
          // hospitalCode: this.loginInfo.hospitalCode,
          // unitCode: this.loginInfo.unitCode,
          assignmentName: this.form.name, //事故名称
          assignmentCode: this.form.situs, //事故地点
          assignmentTemplateName: this.form.assignmentTemplateName, //事故时间
          assignmentTemplateId: this.form.assignmentTemplateId, //事故责任部门ID
          assignmentAttribute: JSON.stringify(this.attrList), //事故责任部门名称
          auditDeptId: this.form.auditDepartmentId, //审核部门ID
          auditDeptName: this.form.auditDepartment, //审核部门名称
          auditPersonId: this.form.auditPersonnelId, //审核人员ID
          auditPersonName: this.form.auditPersonnel, //审核人员名称
          assignmentStatus: 2, //事故状态（1.待提交 2.待审核）
          id: this.rowList.id,
          taskType: this.form.assignmentTemplateName == "动火作业票" ? "1" : "2"
        };
        this.axios.postContralHostBase(
          "saveAssignment",
          params,
          res => {
            if (res.code == 200) {
              this.back();
              this.$toast.success(res.message);
            } else {
              this.$toast.fail(res.message);
            }
          },
          inf0obj
        );
      });
    },
    // getFlag() {
    //   if (this.taskType == 1 && this.approvedOrNot == 1) {
    //     this.flag = false;
    //   } else {
    //     this.flag = true;
    //   }
    // },
    gettingData() {
      let params = {
        platformFlag: 2,
        roleCode: this.loginInfo.roleCode
      };
      this.axios.postContralHostBase("selectDepartList", params, res => {
        if (res.code == 200) {
          // this.typeChildrenList = res.data;
          this.typeDepartment = res.data;
        }
      });
      this.axios.postContralHostBase("getTemplateList", params, res => {
        if (res.code == 200) {
          this.typeChildrenList = res.data;
          // this.typeDepartment = res.data;
        }
      });
    },
    dateClick(item) {
      item.showType = true;
      this.$forceUpdate();
    },
    onType2(val, item) {
      item.attrValue = moment(val).format("YYYY-MM-DD HH:mm:ss");
      item.showType = false;
    },
    cancelClick(item) {
      item.showType = false;
      this.$forceUpdate();
    },
    claimClassification() {
      this.showType = true;
    },
    onType(val) {
      this.form.time = moment(val).format("YYYY-MM-DD HH:mm:ss");
      this.showType = false;
    },
    //责任部门
    onTypeChildren(val) {
      this.attrList = JSON.parse(val.assignmentAttribute);
      this.attrList.forEach(item => {
        item.showType = false;
      });
      this.form.assignmentTemplateName = val.assignmentName;
      this.form.assignmentTemplateId = val.id;
      this.showTypeChildren = false;
    },
    claimDepartment() {
      this.showTypeChildren = true;
    },
    //审核部门
    onDepartment(val) {
      this.form.auditDepartment = val.teamName;
      this.form.auditDepartmentId = val.id;
      this.showDepartment = false;
      let params = {
        platformFlag: 2,
        roleCode: this.loginInfo.roleCode,
        departId: val.id
      };
      this.axios.postContralHostBase("getUseListByDeptId", params, res => {
        if (res.code == 200) {
          this.typePersonnel = res.data;
        }
        console.log(res);
      });
    },
    claimauditDepartment() {
      this.showDepartment = true;
    },
    //审核人员
    onPersonnel(val) {
      this.form.auditPersonnel = val.name;
      this.form.auditPersonnelId = val.id;
      this.showPersonnel = false;
    },
    claimPersonnel() {
      if (!this.form.auditDepartment) {
        this.$toast.fail("请先选择审核部门");
        return;
      }
      this.showPersonnel = true;
    },
    typeChildrenLists() {},
    onOversize() {
      this.$toast.fail(`文件大小不能超过${this.maxSize}M`);
    },
    // 图片选择
    afterRead(files) {
      this.form.fileList.forEach(i => {
        return (i.status = "uploading");
      });
      const params = {
        file: ""
      };
      if (files.length) {
        let formData = new FormData();
        files.forEach(item => {
          formData.append("file", item.file);
        });
        formData.append("hospitalCode", this.loginInfo.hospitalCode);
        axios
          .post(__PATH.BASEURL + "file/upload", formData)
          .then(res => {
            if (res.data.code == 200) {
              this.form.fileList.forEach(i => {
                return (i.status = "done");
              });
              const imgUrl = res.data.data.fileKey.split(",");
              imgUrl.forEach((i, index) => {
                const item = {
                  name: files[index].file.name,
                  fileKey: i
                };
                this.form.attachmentUrl.push(item);
                // this.form.fileList.push(item);
              });
            }
          })
          .catch(() => {
            this.form.fileList.forEach(i => {
              return (i.status = "failed");
            });
            this.$toast.fail("上传失败");
          });
      } else {
        params.file = files.file;
        this.subImg(params);
      }
    },
    subImg(params) {
      console.log(params, "params");
      this.axios.postContralHostBase("uploadImg", params, res => {
        if ((res.code = "200")) {
          const item = {
            name: params.file.name,
            url: res.data.fileKey
          };
          // this.form.fileList.push(item);
          this.form.attachmentUrl.push(item);
          console.log(this.form.attachmentUrl, " this.attachmentUrl");
          this.form.fileList.forEach(i => {
            return (i.status = "done");
          });
        }
      });
    },
    //删除图片
    deleteImg(e) {
      this.form.attachmentUrl = this.form.attachmentUrl.filter(i => i.name != e.name);
    }
  }
};
</script>

<style scoped lang="scss">
@import "../../../assets/stylus/theme";
.connent {
  margin-bottom: 70px;
  padding: 0 15px;
  font-size: 14px;
  margin-top: 50px;
  .top {
    border-left: 5px solid $main-bgColor;
    padding-left: 3px;
    color: $main-bgColor;
  }
  .remarks {
    background-color: #fff;
    margin: 0.3125rem 0 0 0;
    padding: 0.3125rem;
    border-bottom: 1px solid #f6f7f8;
    .van-field {
      height: 70px;
      overflow-y: scroll;
    }
    span {
      color: #646566;
    }
    .message {
      background-color: #f4f5f9;
      margin-top: 0.3125rem;
    }
  }
  .instructions {
    border-bottom: 4px solid #ebedf0;
  }
  .instructions .explain {
    background-color: #fff;
    min-height: 48px;
    display: flex;
    align-items: center;
    padding: 0 10px;
  }
  .instructions .explain span {
    font-size: 14px;
  }
  .bottom {
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
    width: 100%;
    height: 60px;
    padding-bottom: 15px;
    border-top: 1px solid #f6f7f8;
    position: fixed;
    bottom: 0;
    left: 0;
    background-color: #fff;
  }
}
.van-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
}
.van-button {
  border-radius: 5px;
}
.van-cell {
  padding: 10px 7px !important;
}
/deep/ .van-field__label {
  width: 9.2em;
}
.remarks2 {
  background-color: #fff;
  padding: 0 0.3125rem 0 0.3125rem;
  border-bottom: 1px solid #f6f7f8;
  .van-field {
    height: 70px;
    overflow-y: scroll;
  }
  span {
    color: #646566;
  }
  .message {
    background-color: #f4f5f9;
    margin-top: 0.3125rem;
  }
}
.radio {
  // padding: 0.3125rem;
  padding-left: 0.3125rem;
  line-height: 35px;
}
/deep/ .van-cell--required::before {
  position: absolute;
  left: 0 !important;
}
</style>
