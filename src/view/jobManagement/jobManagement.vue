<template>
  <div>
    <Header title="作业管理" @backFun="backFn"></Header>
    <van-tabs v-model="active" title-active-color="#29BEBC" offset-top="3rem" sticky color="#29BEBC" swipe-threshold="4" line-width="50" ellipsis>
      <van-tab v-for="item in tasksList" :title="item.dictName" :key="item.id"> </van-tab>
      <jobReport v-if="active == 0"></jobReport>
      <jobAudit v-if="active == 1" :id="id" :approvalVersion="approvalVersion"></jobAudit>
      <jobLedger v-if="active == 2"></jobLedger>
    </van-tabs>

    <div></div>
  </div>
</template>

<script>
import jobAudit from "./components/jobAudit.vue";
import jobLedger from "./components/jobLedger.vue";
import jobReport from "./components/jobReport.vue";
import topNav from "../components/topNav.vue";

export default {
  components: {
    jobReport,
    jobAudit,
    jobLedger,
    topNav
  },
  data() {
    return {
      active: 0,
      id: "",
      approvalVersion:'',
      tasksList: [
        {
          id: 0,
          dictName: "作业申请"
        },
        {
          id: 1,
          dictName: "作业审核"
        },
        {
          id: 2,
          dictName: "作业台账"
        }
      ]
    };
  },
  mounted() {
    if (this.$route.query.id) {
      this.id = this.$route.query.id;
      this.approvalVersion=this.$route.query.approvalVersion
      this.active = 1;
    }
    this.sysClickBack();
  },
  created() {
    apiready = () => {
      var userInfo = api.getPrefs({
        sync: true,
        key: "userInfo"
      });
      userInfo = JSON.parse(userInfo);
      if (userInfo.id) {
        const virtualToken = encodeURIComponent(userInfo.hospitalName);
        localStorage.setItem("token", virtualToken);
        localStorage.setItem("loginInfo", JSON.stringify(userInfo));
      }
      this.sysClickBack();
    };
  },
  methods: {
    sysClickBack() {
      api.addEventListener(
        {
          name: "keyback666"
        },
        (ret, err) => {
          this.backFn();
        }
      );
    },
    backFn() {
      api.closeFrame({});
      api.sendEvent({
        name: "refreshList",
        extra: {}
      });
      api.sendEvent({
        name: "refreshMessageNum",
        extra: {}
      });
      // api.closeWin();
      // this.$router.go(-1);
    }
  }
};
</script>

<style scoped lang="scss">
@import "../../assets/stylus/theme";
/deep/ .van-tabs__nav--card {
  border: none !important;
  color: $main-bgColor;
}

/deep/ .van-tabs__nav--card .van-tab.van-tab--active {
  background: #fff;
  color: $main-bgColor;
}
/deep/ .van-tabs__nav--card .van-tab {
  color: $text-black-secondTitle;
  border: none !important;
}
.top {
  position: fixed;
  width: 100%;
  z-index: 99;
  background-color: #fff;
}
/deep/ .van-tabs__wrap {
  border-bottom: solid 5px #e6eaf0;
}
header {
  position: fixed;
}
</style>
