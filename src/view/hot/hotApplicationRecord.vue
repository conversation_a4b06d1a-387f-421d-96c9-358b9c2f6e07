<template>
  <div class="content">
    <Header title="我的申请" @backFun="backFn"></Header>
    <van-popup v-model="showStartTimePicker" position="bottom">
      <van-datetime-picker v-model="currentDate" type="datetime" @confirm="onStartTimeConfirm" @cancel="showStartTimePicker = false" />
    </van-popup>
    <van-popup v-model="showEndTimePicker" position="bottom">
      <van-datetime-picker v-model="currentDate2" type="datetime" :min-date="currentDate" @confirm="onEndTimeConfirm" @cancel="showEndTimePicker = false" />
    </van-popup>
    <div class="tool-box">
      <div class="time-box">
        <i class="line"></i>
        <div class="start-time" @click="showStartTimePicker = true">
          <span v-if="startTime">{{ startTime }}</span>
          <span v-else style="color: #86909c">请选择开始时间</span>
        </div>
        <div class="end-time" @click="showEndTimePicker = true">
          <span v-if="endTime">{{ endTime }}</span>
          <span v-else style="color: #86909c">请选择结束时间</span>
        </div>
      </div>
      <div class="search-box">
        <van-dropdown-menu active-color="#29BEBC">
          <van-dropdown-item title="工程属地" ref="dropDownRef">
            <van-cascader
              v-model="cascaderValue"
              title="请选择所在地区"
              :field-names="{ text: 'name', value: 'code', children: 'children' }"
              :options="areaList"
              @finish="cascaderFinish"
              active-color="#29BEBC"
              :closeable="false"
            />
          </van-dropdown-item>
          <van-dropdown-item v-model="hotState" :options="hotStateList" @change="search" />
        </van-dropdown-menu>
        <div class="reset" @click="handleReset">重置</div>
      </div>
    </div>
    <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" offset="100" :immediate-check="false">
      <div class="card" v-for="item in unitList" :key="item.id" @click="goDetails(item.id)">
        <div class="status">
          <div>
            <span>工程名称：</span>
            <span>{{ item.projectName }}</span>
          </div>
          <span v-if="item.hotState == '0' && item.approvalState == '0'" class="c1">动火前-待审批</span>
          <span v-if="item.hotState == '0' && item.approvalState == '1'" class="c1">动火前-审批通过</span>
          <span v-if="item.hotState == '0' && item.approvalState == '2'" class="c2">动火前-审批驳回</span>
          <span v-if="item.hotState == '1'">动火中</span>
          <span v-if="item.hotState == '2'" class="c3">动火后-待结束</span>
          <span v-if="item.hotState == '3'" class="c3">动火后-已结束</span>
        </div>
        <div>
          <span>建设单位：</span>
          <span>{{ item.constructionUnit }}</span>
        </div>
        <div>
          <span>施工单位：</span>
          <span>{{ item.constructionUnitName }}</span>
        </div>
        <div>
          <span>工程属地：</span>
          <span>{{ item.projectTerritory }}</span>
        </div>
        <div>
          <span>建设地址：</span>
          <span>{{ item.constructionAddress }}</span>
        </div>
        <div>
          <span>申请时间：</span>
          <span>{{ item.createTime }}</span>
        </div>
      </div>
    </van-list>
    <!-- <div class="btn-box">
      <van-button color="#29BEBC" @click="addUnit" block>新建单位申报</van-button>
    </div> -->
  </div>
</template>

<script>
export default {
  name: "constructionApplication",
  data() {
    return {
      loading: false,
      finished: false,
      unitList: [],
      pageNo: 1,
      pageSize: 10,
      showStartTimePicker: false,
      showEndTimePicker: false,
      startTime: "",
      endTime: "",
      constructionUnitName: "",
      hotStateList: [
        { text: "动火状态", value: "" },
        { text: "动火前", value: "0" },
        { text: "动火中", value: "1" },
        { text: "待结束", value: "2" },
        { text: "已结束", value: "3" },
      ],
      hotState: "",
      cascaderValue: "",
      areaList: [],
    };
  },
  created() {
    setTimeout(() => {
      api.addEventListener(
        {
          name: "keyback666",
        },
        (ret, err) => {
          this.backFn();
        }
      );
    }, 100);
  },
  methods: {
    async fetchRegions() {
      try {
        const response = await fetch("static/ipsm_sys_area.json");
        const data = await response.json();
        this.areaList = this.utils.transData(data.data, "code", "parent_code", "children");
      } catch (error) {
        console.error("Failed to fetch regions:", error);
      }
    },
    getList() {
      this.axios.postContralHostBase(
        "getHotApplicationRecord",
        {
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          hotStartTime: this.startTime,
          hotEndTime: this.endTime,
          hotState: this.hotState,
          streetId: this.cascaderValue,
        },
        (res) => {
          console.log(res);
          if (res.code == 200) {
            this.unitList = this.unitList.concat(res.data.list);
            this.finished = this.unitList.length >= res.data.total;
          }
        }
      );
    },
    backFn() {
      api.closeFrame({});
    },
    search() {
      this.pageNo = 1;
      this.unitList = [];
      this.getList();
    },
    onLoad() {
      this.finished = false;
      this.loading = true;
      this.pageNo++;
      this.getList();
    },
    goDetails(id) {
      console.log(id);
      this.$router.push({
        path: "/hotApplicationRecordDetails",
        query: {
          id,
        },
      });
    },
    addUnit() {
      this.$router.push({
        path: "/addConstructionUnits",
      });
    },
    onStartTimeConfirm(val) {
      console.log(val);
      // 将时间格式化成yyyy-MM-dd HH:mm格式，不足两位补0
      let year = val.getFullYear();
      let month = val.getMonth() + 1;
      let day = val.getDate();
      let hour = val.getHours();
      let minute = val.getMinutes();
      month = month < 10 ? "0" + month : month;
      day = day < 10 ? "0" + day : day;
      hour = hour < 10 ? "0" + hour : hour;
      minute = minute < 10 ? "0" + minute : minute;
      this.startTime = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":00";
      this.showStartTimePicker = false;
      this.search();
    },
    onEndTimeConfirm(val) {
      console.log(val);
      // 将时间格式化成yyyy-MM-dd HH:mm格式，不足两位补0
      let year = val.getFullYear();
      let month = val.getMonth() + 1;
      let day = val.getDate();
      let hour = val.getHours();
      let minute = val.getMinutes();
      month = month < 10 ? "0" + month : month;
      day = day < 10 ? "0" + day : day;
      hour = hour < 10 ? "0" + hour : hour;
      minute = minute < 10 ? "0" + minute : minute;
      this.endTime = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":59";
      this.showEndTimePicker = false;
      this.search();
    },
    handleReset() {
      this.startTime = "";
      this.endTime = "";
      this.constructionUnitName = "";
      this.hotState = "";
      this.cascaderValue = "";
      this.pageNo = 1;
      this.search();
    },
    cascaderFinish() {
      this.$refs.dropDownRef.toggle();
      this.search();
    },
  },
  mounted() {
    this.getList();
    this.fetchRegions();
  },
  computed: {
    currentDate() {
      return new Date();
    },
    currentDate2() {
      return new Date();
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  background-color: #f2f4f9;
  min-height: 100vh;
}
.van-list {
  padding: 0 10px;
  padding-top: 12px;
  padding-bottom: 60px;
}
.btn-box {
  display: flex;
  align-items: center;
  position: fixed;
  bottom: 0;
  height: 60px;
  width: 100%;
  display: flex;
  justify-content: center;
  background-color: #fff;
  .van-button--block {
    width: 80%;
  }
}
.card {
  background-color: #fff;
  border-radius: 8px;
  font-size: 16px;
  padding: 12px;
  margin-bottom: 12px;
  div > span:nth-child(1) {
    color: #4e5969;
  }
  div > span:nth-child(2) {
    color: #1d2129;
  }
}
.card > div {
  margin-bottom: 10px;
}
.status {
  border-bottom: 1px solid #f2f4f9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 3px;
  > div {
    max-width: 64%;
  }
  span {
    transform: translateY(-3px);
    color: #f53f3f;
  }
}
.status2 {
  border-bottom: 1px solid #f2f4f9;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .van-button {
    height: 28px;
    transform: translateY(-4px);
  }
}
.c1 {
  color: #3562db !important;
  background-color: #e6effc;
  padding: 4px 6px;
  font-size: 14px;
}
.c2 {
  color: #f53f3f !important;
  background-color: #ffece8;
  padding: 4px 6px;
  font-size: 14px;
}
.c3 {
  color: #4e5969 !important;
  background-color: #f2f3f5;
  padding: 4px 6px;
  font-size: 14px;
}
.tool-box {
  width: 100%;
  height: 100px;
  background-color: #fff;
  position: sticky;
  top: 60px;
  border-bottom: 1px solid #e5e6eb;
  padding: 0 16px;
  padding-top: 12px;
  box-sizing: border-box;
  z-index: 9;
}
.time-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
}
.time-box > div {
  width: 45%;
  height: 35px;
  background-color: #f2f3f5;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #1d2129;
  border-radius: 4px;
}
.line {
  position: absolute;
  width: 10px;
  height: 2px;
  top: 50%;
  left: 48%;
  background-color: #c9cdd4;
}
.search-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .van-cell::after {
    border: none;
  }
  > .van-dropdown-menu {
    width: 70%;
  }
}
.search-box .van-field {
  width: 60%;
  margin-top: 5px;
}
.reset {
  width: 30%;
  font-size: 15px;
  color: #1d2129;
  text-align: center;
}
/deep/ .van-dropdown-menu__bar {
  box-shadow: none !important;
}
/deep/ .van-cascader__header {
  display: none;
}
</style>
