<template>
  <div style="padding-bottom: 60px" class="container">
    <Header title="动火申请" @backFun="backFn"></Header>
    <van-form @submit="onSubmit" v-show="current == '1'" class="padding-12">
      <van-field name="projectType" label="工程类型" class="star proj-type">
        <template #input>
          <van-radio-group v-model="projectType" direction="horizontal">
            <van-radio name="1" checked-color="#29BEBC">改建（扩建/内装修）</van-radio>
            <van-radio name="2" checked-color="#29BEBC">新建</van-radio>
            <van-radio name="3" checked-color="#29BEBC">维修</van-radio>
          </van-radio-group>
        </template>
      </van-field>
      <van-field readonly clickable class="star" name="picker" :value="constructionPermitName" label="施工许可" placeholder="请选择施工许可" @click="showPermitPicker = true" />
      <van-popup v-model="showPermitPicker" position="bottom">
        <van-picker show-toolbar :columns="permitColumns" value-key="dictLabel" @confirm="onConfirmPermit" @cancel="showPermitPicker = false" />
      </van-popup>
      <van-uploader
        class="permit-upload"
        v-model="constructionPermit"
        :max-size="50 * 1024 * 1024"
        accept="image/*,application/msword,application/pdf"
        max-count="10"
        :after-read="afterImgRead"
        :preview-options="{
          images: filterImagesForPreview(constructionPermit),
        }"
      />
      <van-field readonly clickable class="star" name="picker" :value="constructionUnitName" label="施工单位" placeholder="请选择施工单位" @click="showBuildPicker = true" />
      <van-popup v-model="showBuildPicker" position="bottom">
        <van-picker show-toolbar :columns="buildColumns" value-key="constructionUnitName" @confirm="onConfirmBuild" @cancel="showBuildPicker = false" />
      </van-popup>
      <van-field v-model="creditCode" class="star" name="信用代码" label="信用代码" readonly />
      <van-field
        readonly
        clickable
        class="star"
        name="picker"
        :value="industryAuthoritiesName"
        label="行业主管部门"
        placeholder="请选择行业主管部门"
        @click="showPartPicker = true"
      />
      <van-popup v-model="showPartPicker" position="bottom">
        <van-picker show-toolbar :columns="partColumns" value-key="dictLabel" @confirm="onConfirmPart" @cancel="showPartPicker = false" />
      </van-popup>
      <van-field readonly clickable v-model="projectName" class="star" name="工程名称" label="工程名称" placeholder="请输入工程名称" maxlength="50" @click="clickProject" />
      <van-popup v-model="showProjectName" position="bottom">
        <van-picker value-key="hotUser" show-toolbar :columns="projectNameList.map((i) => i.dictName)" @confirm="confirmProjectName" @cancel="showProjectName = false" />
      </van-popup>
      <van-field v-model="constructionUnit" class="star" name="建设单位" label="建设单位" readonly />
      <van-field readonly clickable class="star" name="area" :value="projectTerritory" label="工程属地" placeholder="选择区/街道" @click="showArea = true" />
      <van-popup v-model="showArea" position="bottom">
        <van-cascader
          v-model="cascaderValue"
          title="请选择所在地区"
          :field-names="{ text: 'name', value: 'code', children: 'children' }"
          :options="areaList"
          @close="showArea = false"
          @finish="onConfirmArea"
          active-color="#29BEBC"
        />
      </van-popup>
      <van-field
        readonly
        clickable
        v-model="constructionAddress"
        class="star"
        name="建设地址"
        label="建设地址"
        placeholder="请选择建设地址"
        maxlength="200"
        @click="showAddress = true"
      />
      <van-popup v-model="showAddress" position="bottom">
        <van-picker value-key="address" show-toolbar :columns="addressList.map((i) => i.dictName)" @confirm="confirmAddress" @cancel="showAddress = false" />
      </van-popup>
      <van-field v-model="constructionScale" class="star" name="建设规模" label="建设规模" placeholder="请输入建设规模" maxlength="50" />
      <van-field readonly clickable name="picker" :value="licensingAuthorityName" label="许可发证机关" placeholder="请选择许可发证机关" @click="showOrganPicker = true" />
      <van-popup v-model="showOrganPicker" position="bottom">
        <van-picker show-toolbar :columns="organColumns" value-key="dictLabel" @confirm="onConfirmOrgan" @cancel="showOrganPicker = false" />
      </van-popup>
    </van-form>
    <div v-show="current == '2'" class="second-box padding-12">
      <div class="title">
        动火人信息
        <span>（请保持与《动火许可证》信息一致）</span>
      </div>
      <van-form ref="hotPerson">
        <van-field v-model="hotSpot" class="star" name="动火部位" label="动火部位" placeholder="请输入动火部位" maxlength="200" />
        <van-field
          readonly
          clickable
          v-model="hotPersonName"
          class="star"
          name="动火人姓名"
          label="动火人姓名"
          placeholder="请选择动火人姓名"
          maxlength="50"
          @click="showHotUser = true"
        />
        <van-popup v-model="showHotUser" position="bottom">
          <van-picker value-key="hotUser" show-toolbar :columns="hotUserList.map((i) => i.name)" @confirm="confirmUser" @cancel="showHotUser = false" />
        </van-popup>
        <van-field v-model="hotPhone" class="star" name="电话" label="电话" placeholder="请输入动火人电话" maxlength="11" type="tel" :formatter="telFormatter" />
        <van-field
          readonly
          clickable
          name="hotStartTime"
          :value="hotStartTime"
          label="动火开始时间"
          placeholder="请选择动火开始时间"
          @click="showStartTimePicker = true"
          :rules="[{ required: true, message: '' }]"
          class="star"
        />
        <van-popup v-model="showStartTimePicker" position="bottom">
          <van-datetime-picker type="datetime" v-model="currentDate" @confirm="onStartTimeConfirm" @cancel="showStartTimePicker = false" />
        </van-popup>
        <van-field
          readonly
          clickable
          name="hotEndTime"
          :value="hotEndTime"
          label="动火结束时间"
          placeholder="请选择动火结束时间"
          @click="showEndTimePicker = true"
          :rules="[{ required: true, message: '' }]"
          class="star"
        />
        <van-popup v-model="showEndTimePicker" position="bottom">
          <van-datetime-picker type="datetime" v-model="currentDate2" :min-date="currentDate" @confirm="onEndTimeConfirm" @cancel="showEndTimePicker = false" />
        </van-popup>
        <van-field v-model="hotContent" class="star star-pro" rows="2" autosize label="动火内容" type="textarea" maxlength="500" placeholder="请输入动火内容" show-word-limit />
        <van-field name="safetyMeasures" label="现场消防安全措施" class="star star-pro safe-box">
          <template #input>
            <div class="upload-box">
              <van-uploader
                v-model="safetyMeasures"
                accept="image/*,.doc,.docx,.pdf"
                max-count="10"
                :max-size="50 * 1024 * 1024"
                :after-read="afterImgRead5"
                :preview-image="false"
              />
              <span class="tips">支持上传word、pdf格式，文件大小不可超过50M</span>
            </div>
            <div class="file-item" v-for="(item, index) in safetyMeasures" :key="item.url" @click="preview(item)">
              <span>{{ item.fileName | spliceFileName }}</span>
              <van-icon name="cross" color="#f53f3f" @click="delFile(index)" />
            </div>
          </template>
        </van-field>
        <van-field
          v-model="fireExtinguishingEquipment"
          class="star star-pro"
          rows="2"
          autosize
          label="配备灭火器材"
          type="textarea"
          maxlength="500"
          placeholder="请输入灭火器材"
          show-word-limit
        />
      </van-form>
      <div class="title">动火证件信息</div>
      <van-form>
        <van-field name="hotModeId" label="动火方式" class="star star-pro">
          <template #input>
            <van-checkbox-group v-model="hotModeId" direction="horizontal">
              <van-checkbox :name="item.dictValue" checked-color="#29BEBC" shape="square" v-for="item in hotMethodList" :key="item.dictValue">{{ item.dictLabel }}</van-checkbox>
            </van-checkbox-group>
          </template>
        </van-field>
      </van-form>
      <van-form v-show="showSpecial">
        <van-field name="specialWorkCertificateUrl" label="特种作业证" class="star star-pro">
          <template #input>
            <van-uploader
              v-model="specialWorkCertificateUrl"
              accept=".jpg,.jpeg,.png,application/msword,application/pdf"
              max-count="10"
              :max-size="50 * 1024 * 1024"
              :after-read="afterImgRead4"
              :preview-options="{
                images: filterImagesForPreview(specialWorkCertificateUrl),
              }"
            />
          </template>
        </van-field>
        <van-field v-model="certificateNumber" name="证号" label="证号" placeholder="请输入证件号码" maxlength="50" />
        <van-field v-model="certificateName" name="姓名" label="姓名" placeholder="请输入姓名" maxlength="50" />
        <van-field name="radio" label="性别">
          <template #input>
            <van-radio-group v-model="certificateSex" direction="horizontal">
              <van-radio name="1">男</van-radio>
              <van-radio name="2">女</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field v-model="jobClass" name="作业类别" label="作业类别" placeholder="请输入作业类别" maxlength="50" />
        <van-field v-model="standardOperationItems" name="准操项目" label="准操项目" placeholder="请输入准操项目" maxlength="50" />
        <van-field name="personPhoto" label="人证合照" class="star star-pro">
          <template #input>
            <van-uploader
              v-model="personPhoto"
              accept=".jpg,.jpeg,.png,application/msword,application/pdf"
              max-count="10"
              :max-size="50 * 1024 * 1024"
              :after-read="afterImgRead2"
              :preview-options="{
                images: filterImagesForPreview(personPhoto),
              }"
            />
            <span class="tips" v-if="!personPhoto.length">请动火人手持《特种作业证》合照图片。</span>
          </template>
        </van-field>
      </van-form>
    </div>
    <div v-show="current == '3'" class="third-box padding-12">
      <div class="title">监护人信息</div>
      <van-form>
        <van-field readonly clickable v-model="tutelageName" class="star" name="姓名" label="姓名" placeholder="请输入监护人姓名" maxlength="50" @click="showTutelage = true" />
        <van-popup v-model="showTutelage" position="bottom">
          <van-picker value-key="tutelage" show-toolbar :columns="hotUserList.map((i) => i.name)" @confirm="confirmTutelage" @cancel="showTutelage = false" />
        </van-popup>
        <van-field v-model="tutelagePhone" class="star" name="电话" label="电话" placeholder="请输入监护人电话" maxlength="11" type="tel" :formatter="telFormatter" />
        <van-field v-model="tutelageIdentityCard" class="star" name="身份证号" label="身份证号" placeholder="请输入身份证号" maxlength="18" :formatter="telFormatter2" />
      </van-form>
      <div class="title">现场拍照</div>
      <van-field name="scenePhoto" label="现场照片">
        <template #input>
          <van-uploader
            v-model="scenePhoto"
            accept=".jpg,.jpeg,.png,application/msword,application/pdf"
            max-count="10"
            :max-size="50 * 1024 * 1024"
            :after-read="afterImgRead3"
            :preview-options="{
              images: filterImagesForPreview(scenePhoto),
            }"
          />
        </template>
      </van-field>
      <div class="title">审批流</div>
      <div class="sub-title text-star">是否有监理单位</div>
      <van-field name="radio">
        <template #input>
          <van-radio-group v-model="isSupervision" direction="horizontal">
            <van-radio checked-color="#29BEBC" name="1">否</van-radio>
            <van-radio checked-color="#29BEBC" name="0">是</van-radio>
          </van-radio-group>
        </template>
      </van-field>
      <van-field
        v-if="isSupervision == 0"
        readonly
        clickable
        name="picker"
        :value="supervisionUnit"
        label="监理单位"
        placeholder="请选择监理单位"
        @click="showSupervisionPicker = true"
        class="star"
      />
      <van-popup v-model="showSupervisionPicker" position="bottom">
        <van-search v-model="searchSupervisionUnit" placeholder="请输入搜索关键词" />
        <van-picker show-toolbar :columns="supervisionColumns" value-key="companyName" @confirm="onConfirmSupervision" @cancel="showSupervisionPicker = false" />
      </van-popup>
      <div class="sub-title">选择主管部门审批</div>
      <van-field readonly clickable class="star" name="picker" :value="approveName" label="选择审批流" placeholder="请选择审批流" @click="showApprovePicker = true" />
      <van-popup v-model="showApprovePicker" position="bottom">
        <van-picker show-toolbar :columns="ApproveColumns" value-key="approvalName" @confirm="onConfirmApprove" @cancel="showApprovePicker = false" />
      </van-popup>
    </div>
    <div v-show="current == '4'" class="success-box padding-12">
      <img src="@/assets/images/success.png" />
      <div style="margin: 24px 0">提交成功!</div>
      <div>在“我的申请”中可查看审批状态</div>
    </div>
    <div class="btn-box-1" v-if="current == '1'">
      <van-button block type="info" native-type="submit" color="#29BEBC" @click="nextStep">下一步</van-button>
    </div>
    <div class="btn-box" v-if="current == '2'">
      <van-button class="undertone" color="#29BEBC" @click="current = '1'">上一步</van-button>
      <van-button color="#29BEBC" @click="nextStep2">填报监护人</van-button>
    </div>
    <div class="btn-box" v-if="current == '3'">
      <van-button class="undertone" color="#29BEBC" @click="current = '2'">上一步</van-button>
      <van-button color="#29BEBC" @click="finalSubmit">提交申请</van-button>
    </div>
  </div>
</template>
<script>
import YBS from "@/centralControl/utils/utils.js";
export default {
  data() {
    return {
      projectType: "1",
      constructionPermit: [],
      showBuildPicker: false,
      buildColumns: "",
      creditCode: "",
      showPartPicker: false,
      partColumns: "",
      projName: "",
      showArea: false,
      areaList: [],
      hospitalName: "",
      constructionUnitName: "",
      constructionUnitId: "",
      constructionUnit: "",
      department: "",
      industryAuthoritiesId: "",
      projectName: "",
      projectId: "",
      projectTerritory: "",
      showOrganPicker: false,
      licensingAuthorityId: "",
      organColumns: "",
      constructionAddress: "",
      constructionAddressId: "",
      constructionScale: "",
      current: "1",
      hotSpot: "",
      hotPersonName: "",
      hotPersonId: "",
      hotStartTime: "",
      hotEndTime: "",
      showStartTimePicker: false,
      showEndTimePicker: false,
      hotModeId: [],
      hotMethodList: [],
      showSpecial: false,
      specialWorkCertificateUrl: [],
      certificateNumber: "",
      certificateName: "",
      certificateSex: "",
      personPhoto: [],
      scenePhoto: [],
      approveId: "",
      showApprovePicker: false,
      ApproveColumns: "",
      jobClass: "",
      standardOperationItems: "",
      tutelageName: "",
      tutelageId: "",
      tutelagePhone: "",
      loginInfo: "",
      showPermitPicker: false,
      permitColumns: [],
      constructionPermitId: "",
      constructionPermitName: "",
      industryAuthoritiesName: "",
      licensingAuthorityName: "",
      approveName: "",
      cascaderValue: "",
      provinceCode: "",
      provinceName: "",
      downtownCode: "",
      downtownName: "",
      areaId: "",
      areaName: "",
      streetId: "",
      streetName: "",
      hotPhone: "",
      minDate: new Date(),
      hotContent: "",
      safetyMeasures: [],
      isSupervision: "1",
      showSupervisionPicker: false,
      supervisionUnit: "",
      fireExtinguishingEquipment: "",
      supervisionUnitId: "",
      constructPrincipalPhone: "",
      supervisorPrincipalPhone: "",
      supervisorPrincipalSeal: "",
      constructPrincipalSeal: "",
      constructionUnitCode: "",
      supervisionUnitCode: "",
      buildColumns2: [],
      currentDate: new Date(),
      currentDate2: new Date(),
      tutelageIdentityCard: "",
      searchSupervisionUnit: "",
      showHotUser: false,
      hotUserList: [],
      showAddress: false,
      addressList: [],
      showTutelage: false,
      showProjectName: false,
      projectNameList: [],
    };
  },
  methods: {
    async fetchRegions() {
      try {
        const response = await fetch("static/ipsm_sys_area.json");
        const data = await response.json();
        this.areaList = this.utils.transData(data.data, "code", "parent_code", "children");
      } catch (error) {
        console.error("Failed to fetch regions:", error);
      }
    },
    filterImagesForPreview(arr) {
      // 过滤出图片类型的文件内容,取item.content
      return arr.filter((item) => item.content.indexOf("image") > -1).map((item) => item.content);
    },
    nextStep() {
      if (!this.constructionPermitName) {
        return this.$toast("请选择施工许可");
      }
      if (!this.constructionUnitName) {
        return this.$toast("请选择建设单位");
      }
      if (!this.industryAuthoritiesName) {
        return this.$toast("请选择行业主管部门");
      }
      if (!this.projectName) {
        return this.$toast("请输入工程名称");
      }
      if (!this.projectTerritory) {
        return this.$toast("请选择工程属地");
      }
      if (!this.constructionAddress) {
        return this.$toast("请输入建设地址");
      }
      if (!this.constructionScale) {
        return this.$toast("请输入建设规模");
      }
      this.current = "2";
      window.scrollTo(0, 0);
    },
    nextStep2() {
      if (!this.hotSpot) {
        return this.$toast("请输入动火部位");
      }
      if (!this.hotPersonName) {
        return this.$toast("请输入动火人姓名");
      }
      if (!this.hotPhone) {
        return this.$toast("请输入动火人电话");
      }
      if (!this.hotContent) {
        return this.$toast("请输入动火内容");
      }
      if (this.safetyMeasures.length == 0) {
        return this.$toast("请上传现场消防安全措施");
      }
      if (!this.fireExtinguishingEquipment) {
        return this.$toast("请输入配备灭火器材");
      }
      if (this.hotModeId.length == 0) {
        return this.$toast("请选择动火方式");
      }
      if (this.hotModeId.indexOf("1") > -1 || this.hotModeId.indexOf("2") > -1 || this.hotModeId.indexOf("3") > -1) {
        if (this.specialWorkCertificateUrl.length == 0) {
          return this.$toast("请上传特种作业证");
        }
        if (this.personPhoto.length == 0) {
          return this.$toast("请上传人证合照");
        }
        // if (!this.certificateNumber) {
        //   return this.$toast("请输入证号");
        // }
        // if (!this.certificateName) {
        //   return this.$toast("请输入姓名");
        // }
        // if (!this.certificateSex) {
        //   return this.$toast("请选择性别");
        // }
        // if (!this.jobClass) {
        //   return this.$toast("请输入作业类别");
        // }
        // if (!this.standardOperationItems) {
        //   return this.$toast("请输入准操项目");
        // }
      }
      this.current = "3";
      window.scrollTo(0, 0);
    },
    power() {
      if (!YBS.hasPermission("storage")) {
        let pageParam = {
          title: "存储权限使用说明",
          cont: "用于存储图片、视频等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["storage"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
              if (!YBS.hasPermission("camera")) {
              YBS.reqPermission(["camera"], function (ret) {
                if (ret && ret.list.length > 0 && ret.list[0].granted) {
                }
              });
              return;
              }
            }
          });
          })
        return;
      }
      if (!YBS.hasPermission("camera")) {
        let pageParam = {
          title: "摄像机权限使用说明",
          cont: "用于扫描巡检码、空间码、拍照上传等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["camera"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
            }
          });
        })
        return;
      }
    },
    backFn() {
      if (this.current == "1") {
        api.closeFrame({});
      }
      if (this.current == "2") {
        this.current = "1";
      }
      if (this.current == "3") {
        this.current = "2";
      }
      if (this.current == "4") {
        api.closeFrame({});
      }
    },
    onSubmit() {},
    onConfirmBuild(val) {
      this.constructionUnitName = val.constructionUnitName;
      this.constructionUnitId = val.id;
      this.constructionUnitCode = val.constructionUnitCode;
      this.creditCode = val.creditCode;
      this.showBuildPicker = false;
      const params = {
        pageSize: 999,
        currentPage: 1,
        companyCode: this.constructionUnitCode,
      };
      this.axios.postContralHostBase("getOutsourcedUserList", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          this.hotUserList = data.list;
        } else {
          this.$toast(message || "获取外委人员失败");
        }
      });
      this.getProjectName(val.constructionUnitId);
    },
    clickProject() {
      if (this.projectNameList.length > 0) {
        this.showProjectName = true;
      } else {
        this.$toast("此施工单位下无工程名称请在pc端配置");
      }
    },
    getProjectName(id) {
      this.projectId = "";
      this.projectName = "";
      const params = {
        parentId: id || "",
        dictType: "project_name",
      };
      this.axios.postContralHostBase("getProjectNameList", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          this.projectNameList = data;
          if (id && data.length > 0) {
            this.projectId = data[0].id;
            this.projectName = data[0].dictName;
          }
        } else {
          this.$toast(message || "获取工程名称失败");
        }
      });
    },
    onConfirmPart(val) {
      console.log(val);
      this.industryAuthoritiesId = val.dictValue;
      this.industryAuthoritiesName = val.dictLabel;
      this.showPartPicker = false;
    },
    onConfirmArea(val) {
      console.log(val);
      this.provinceCode = val.selectedOptions[0].code;
      this.provinceName = val.selectedOptions[0].name;
      this.downtownCode = val.selectedOptions[1].code;
      this.downtownName = val.selectedOptions[1].name;
      this.areaId = val.selectedOptions[2].code;
      this.areaName = val.selectedOptions[2].name;
      this.streetId = val.selectedOptions[3].code;
      this.streetName = val.selectedOptions[3].name;
      this.projectTerritory = this.areaName + this.streetName;
      this.showArea = false;
    },
    onConfirmOrgan(val) {
      console.log(val);
      this.licensingAuthorityId = val.dictValue;
      this.licensingAuthorityName = val.dictLabel;
      this.showOrganPicker = false;
    },
    onStartTimeConfirm(val) {
      console.log(val);
      // 将时间格式化成yyyy-MM-dd HH:mm格式，不足两位补0
      let year = val.getFullYear();
      let month = val.getMonth() + 1;
      let day = val.getDate();
      let hour = val.getHours();
      let minute = val.getMinutes();
      month = month < 10 ? "0" + month : month;
      day = day < 10 ? "0" + day : day;
      hour = hour < 10 ? "0" + hour : hour;
      minute = minute < 10 ? "0" + minute : minute;
      this.hotStartTime = year + "-" + month + "-" + day + " " + hour + ":" + minute;
      this.showStartTimePicker = false;
    },
    onEndTimeConfirm(val) {
      console.log(val);
      // 将时间格式化成yyyy-MM-dd HH:mm格式，不足两位补0
      let year = val.getFullYear();
      let month = val.getMonth() + 1;
      let day = val.getDate();
      let hour = val.getHours();
      let minute = val.getMinutes();
      month = month < 10 ? "0" + month : month;
      day = day < 10 ? "0" + day : day;
      hour = hour < 10 ? "0" + hour : hour;
      minute = minute < 10 ? "0" + minute : minute;
      this.hotEndTime = year + "-" + month + "-" + day + " " + hour + ":" + minute;
      this.showEndTimePicker = false;
    },
    onConfirmApprove(val) {
      console.log(val);
      this.approveId = val.id;
      this.approveName = val.approvalName;
      this.showApprovePicker = false;
    },
    finalSubmit() {
      if (!this.constructionPermitName) {
        return this.$toast("请选择施工许可");
      }
      if (!this.constructionUnitName) {
        return this.$toast("请选择建设单位");
      }
      if (!this.industryAuthoritiesName) {
        return this.$toast("请选择行业主管部门");
      }
      if (!this.projectName) {
        return this.$toast("请输入工程名称");
      }
      if (!this.projectTerritory) {
        return this.$toast("请选择工程属地");
      }
      if (!this.constructionAddress) {
        return this.$toast("请输入建设地址");
      }
      if (!this.constructionScale) {
        return this.$toast("请输入建设规模");
      }
      if (!this.hotSpot) {
        return this.$toast("请输入动火部位");
      }
      if (!this.hotPersonName) {
        return this.$toast("请输入动火人姓名");
      }
      if (!this.hotPhone) {
        return this.$toast("请输入动火人电话");
      }
      if (!this.hotContent) {
        return this.$toast("请输入动火内容");
      }
      if (this.safetyMeasures.length == 0) {
        return this.$toast("请上传现场消防安全措施");
      }
      if (!this.fireExtinguishingEquipment) {
        return this.$toast("请输入配备灭火器材");
      }
      if (this.hotModeId.length == 0) {
        return this.$toast("请选择动火方式");
      }
      if (this.hotModeId.indexOf("1") > -1 || this.hotModeId.indexOf("2") > -1 || this.hotModeId.indexOf("3") > -1) {
        if (this.specialWorkCertificateUrl.length == 0) {
          return this.$toast("请上传特种作业证");
        }
        if (this.personPhoto.length == 0) {
          return this.$toast("请上传人证合照");
        }
        // if (!this.certificateNumber) {
        //   return this.$toast("请输入证号");
        // }
        // if (!this.certificateName) {
        //   return this.$toast("请输入姓名");
        // }
        // if (!this.certificateSex) {
        //   return this.$toast("请选择性别");
        // }
        // if (!this.jobClass) {
        //   return this.$toast("请输入作业类别");
        // }
        // if (!this.standardOperationItems) {
        //   return this.$toast("请输入准操项目");
        // }
      }
      if (!this.tutelageName) {
        return this.$toast("请输入监护人姓名");
      }
      if (!this.tutelagePhone) {
        return this.$toast("请输入监护人电话");
      }
      if (!this.tutelageIdentityCard) {
        return this.$toast("请输入监护人身份证号");
      }
      if (this.isSupervision == 0 && !this.supervisionUnit) {
        return this.$toast("请选择监理单位");
      }
      if (!this.approveName) {
        return this.$toast("请选择审批流");
      }
      this.$refs.hotPerson
        .validate(["hotStartTime", "hotEndTime"])
        .then(() => {
          // 遍历hotModeId和hotMethodList，如果hotModeId中有hotMethodList中的dictValue，就将dictLabel拼接成字符串
          let hotNameArr = [];
          this.hotModeId.forEach((item) => {
            this.hotMethodList.forEach((item2) => {
              if (item == item2.dictValue) {
                hotNameArr.push(item2.dictLabel);
              }
            });
          });
          let params = {
            projectType: this.projectType,
            constructionPermitId: this.constructionPermitId,
            constructionPermitName: this.constructionPermitName,
            constructionUnit: this.constructionUnit,
            creditCode: this.creditCode,
            industryAuthoritiesId: this.industryAuthoritiesId,
            industryAuthoritiesName: this.industryAuthoritiesName,
            projectName: this.projectName,
            projectId: this.projectId,
            projectTerritory: this.projectTerritory,
            licensingAuthorityId: this.licensingAuthorityId,
            licensingAuthorityName: this.licensingAuthorityName,
            constructionAddress: this.constructionAddress,
            constructionAddressId: this.constructionAddressId, // 建设地址id
            constructionScale: this.constructionScale,
            hotSpot: this.hotSpot,
            hotPersonName: this.hotPersonName,
            hotPersonId: this.hotPersonId, // 动火人id
            hotStartTime: this.hotStartTime,
            hotEndTime: this.hotEndTime,
            hotModeId: this.hotModeId.join(","),
            specialWorkCertificateUrl: this.specialWorkCertificateUrl.map((item) => item.url).join(","),
            certificateNumber: this.certificateNumber,
            certificateName: this.certificateName,
            certificateSex: this.certificateSex,
            personPhoto: this.personPhoto.map((item) => item.url).join(","),
            scenePhoto: this.scenePhoto.map((item) => item.url).join(","),
            approveId: this.approveId,
            jobClass: this.jobClass,
            standardOperationItems: this.standardOperationItems,
            tutelageName: this.tutelageName,
            tutelageId: this.tutelageId, // 监护人id
            tutelagePhone: this.tutelagePhone,
            // 逗号拼接
            constructionPermitUrl: this.constructionPermit.map((item) => item.url).join(","),
            provinceCode: this.provinceCode,
            provinceName: this.provinceName,
            downtownCode: this.downtownCode,
            downtownName: this.downtownName,
            areaId: this.areaId,
            areaName: this.areaName,
            streetId: this.streetId,
            streetName: this.streetName,
            hotPhone: this.hotPhone,
            hotModeName: hotNameArr.join(","),
            approveName: this.approveName,
            constructionUnitName: this.constructionUnitName,
            constructionUnitId: this.constructionUnitId,
            hotWorkContent: this.hotContent,
            fireExtinguishingEquipment: this.fireExtinguishingEquipment,
            supervisionUnitId: this.supervisionUnitId,
            supervisionUnitName: this.supervisionUnit,
            isThereAnySupervision: this.isSupervision,
            constructionUnitCode: this.constructionUnitCode,
            supervisionUnitCode: this.supervisionUnitCode,
            tutelageIdentityCard: this.tutelageIdentityCard,
          };
          let fireSafetyMeasuresArr = [];
          this.safetyMeasures.forEach((item) => {
            fireSafetyMeasuresArr.push({
              url: item.url,
              fileName: item.fileName,
            });
          });
          params.fireSafetyMeasures = JSON.stringify(fireSafetyMeasuresArr);
          console.log(params);
          this.$toast.loading({
            message: "提交中...",
            forbidClick: true,
            duration: 0,
          });
          this.axios.postContralHostBase("saveHot", params, (res) => {
            this.$toast.clear();
            console.log(res);
            if (res.code == 200) {
              this.current = "4";
            } else if (res.code == 400) {
              this.$toast(res.message);
            } else {
              this.$toast("提交失败！");
            }
          });
        })
        .catch((err) => {
          console.log("err", err);
          this.$toast("请填写完整信息!");
        });
    },
    afterImgRead(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.constructionPermit[this.constructionPermit.length - 1].url = data.fileKey;
        }
      });
    },
    onConfirmPermit(val) {
      console.log(val);
      this.constructionPermitId = val.dictValue;
      this.constructionPermitName = val.dictLabel;
      this.showPermitPicker = false;
    },
    afterImgRead2(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.personPhoto[this.personPhoto.length - 1].url = data.fileKey;
        }
      });
    },
    afterImgRead3(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.scenePhoto[this.scenePhoto.length - 1].url = data.fileKey;
        }
      });
    },
    afterImgRead4(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.specialWorkCertificateUrl[this.specialWorkCertificateUrl.length - 1].url = data.fileKey;
        }
      });
    },
    afterImgRead5(file) {
      this.$toast.loading({
        message: "上传中...",
        forbidClick: true,
      });
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        this.$toast.clear();
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.$set(this.safetyMeasures[this.safetyMeasures.length - 1], "url", data.fileKey);
          this.$set(this.safetyMeasures[this.safetyMeasures.length - 1], "fileName", data.fileName);
        }
      });
    },
    onConfirmSupervision(val) {
      console.log(val);
      this.supervisionUnit = val.companyName;
      this.supervisionUnitId = val.id;
      this.supervisionUnitCode = val.companyCode;
      this.showSupervisionPicker = false;
    },
    telFormatter(val) {
      return val.replace(/\D/g, "").slice(0, 11);
    },
    telFormatter2(val) {
      // 身份证号校验,15位或者18位，最后一位可以输入字母x
      return val.replace(/[^0-9xX]/g, "").slice(0, 18);
    },
    preview(item) {
      console.log(item);
      let url = item.url;
      // 判断如果url不是全路径，在前面补全https://sinomis.oss-cn-beijing.aliyuncs.com
      if (!url.match(/https:\/\/[^/]+\/(.+)/)) {
        url = __PATH.DOWNLOAD_URL + url;
      }
      api.download(
        {
          url: url,
          report: true,
          cache: true,
          allowResume: true,
        },
        function (ret, err) {
          if (ret.state == 1) {
            if (api.systemType == "ios") {
              api.openWin({
                name: "my/pdfview",
                url: "widget://html/common_window.html",
                bgColor: "rgba(250, 250, 250, 0)",
                hideHomeIndicator: true,
                bounces: false,
                scrollEnabled: false,
                useWKWebView: true,
                pageParam: {
                  title: "文件预览",
                  savePath: ret.savePath,
                  webUrl: ret.savePath,
                },
              });
            } else {
              // 安卓
              // 文档

              var docReader = api.require("docReader");
              docReader.open(
                {
                  path: ret.savePath,
                  autorotation: false,
                },
                function (ret, err) {
                  console.log(JSON.stringify(ret));
                  console.log(JSON.stringify(err));
                }
              );
            }
            console.log("下载成功");
          } else {
            console.log("下载失败");
          }
        }
      );
    },
    delFile(index) {
      this.safetyMeasures.splice(index, 1);
    },
    confirmUser(val, index) {
      this.hotPersonId = this.hotUserList[index].id;
      this.hotPersonName = this.hotUserList[index].name;
      this.hotPhone = this.hotUserList[index].mobilePhone;
      this.showHotUser = false;
    },
    confirmAddress(val, index) {
      this.constructionAddressId = this.addressList[index].id;
      this.constructionAddress = this.addressList[index].dictName;
      this.showAddress = false;
    },
    confirmTutelage(val, index) {
      this.tutelageId = this.hotUserList[index].id;
      this.tutelageName = this.hotUserList[index].name;
      this.tutelagePhone = this.hotUserList[index].mobilePhone;
      this.tutelageIdentityCard = this.hotUserList[index].identityCard || "";
      this.showTutelage = false;
    },
    confirmProjectName(val, index) {
      this.projectId = this.projectNameList[index].id;
      this.projectName = this.projectNameList[index].dictName;
      this.showProjectName = false;
    },
  },
  mounted() {
    setTimeout(() => {
      this.power();
    }, 1000);
    this.axios.postContralHostBase("getDictValueList", { dictType: "hot_mode" }, (res) => {
      console.log(res);
      this.hotMethodList = res.data;
    });
    this.axios.postContralHostBase("getDictValueList", { dictType: "construction_permit" }, (res) => {
      console.log(res);
      this.permitColumns = res.data;
    });
    this.axios.postContralHostBase("getDictValueList", { dictType: "industry_authorities" }, (res) => {
      console.log(res);
      this.partColumns = res.data;
    });
    this.axios.postContralHostBase("getDictValueList", { dictType: "licensing_authority" }, (res) => {
      console.log(res);
      this.organColumns = res.data;
    });
    this.axios.postContralHostBase("getApprovalSettingList", { pageNo: 1, pageSize: 20 }, (res) => {
      this.ApproveColumns = res.data.list;
      console.log(res);
    });
    this.axios.postContralHostBase("getHospitalArea", {}, (res) => {
      console.log("获取医院区域配置", res);
      if (res.code == 200) {
        this.cascaderValue = res.data.areaCode ? res.data.areaCode : "";
      }
    });
    // this.axios.postContralHostBase("selectAreaList", { parentCode: "" }, (res) => {
    //   // let index = res.data.findIndex((item) => item.code == "110100");
    //   // res.data.splice(index, 1);
    //   this.areaList = this.utils.transData(res.data, "code", "parent_code", "children");
    //   console.log(res);
    // });
    this.fetchRegions();
    this.axios.postContralHostBase(
      "getBuildUnitList",
      {
        constructionUnitCode: this.loginInfo.companyCode ? this.loginInfo.companyCode : "",
      },
      (res) => {
        this.buildColumns = res.data;
      }
    );
    this.axios.postContralHostBase("getSourcedCompanyList", {}, (res) => {
      console.log(res);
      this.buildColumns2 = res.data;
    });
    this.axios.postContralHostBase(
      "getPlanType",
      {
        dictType: "building_address",
        pageNo: 1,
        pageSize: 999,
      },
      (res) => {
        if (res.code == "200") {
          this.addressList = res.data.list;
        }
      }
    );
    this.getProjectName();
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.constructionUnit = this.loginInfo.hospitalName;
    setTimeout(() => {
      api.addEventListener(
        {
          name: "keyback666",
        },
        (ret, err) => {
          this.backFn();
        }
      );
    }, 100);
  },
  computed: {
    supervisionColumns() {
      // 通过searchSupervisionUnit过滤buildColumns2，返回一个新数组
      let arr = this.buildColumns2.filter((item) => item.companyName.indexOf(this.searchSupervisionUnit) > -1);
      return arr;
    },
  },
  watch: {
    hotModeId(val) {
      // 遍历val，如果val中有1,2,3其中一个，就显示showSpecial
      if (val.indexOf("1") > -1 || val.indexOf("2") > -1 || val.indexOf("3") > -1) {
        this.showSpecial = true;
      } else {
        this.showSpecial = false;
      }
    },
  },
  filters: {
    spliceFileName(val) {
      if (val.length < 20) {
        return val;
      }
      return val.slice(0, 20) + "...";
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 110vh;
}
.title {
  font-size: 18px;
  border-left: 3px solid #29bebc;
  padding-left: 12px;
}
.second-box {
  padding: 0 16px;
  padding-top: 24px;
}
.third-box {
  padding: 0 16px;
  padding-top: 24px;
}
.van-checkbox {
  margin-bottom: 8px;
}
.tips {
  font-size: 12px;
  color: red;
}
.btn-box {
  width: 60%;
  margin: 0 auto;
  margin-top: 12px;
  display: flex;
  justify-content: space-between;
}
.success-box {
  margin-top: 30vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.success-box > div:nth-child(2) {
  font-size: 18px;
}
.success-box > div:nth-child(3) {
  font-size: 16px;
}
.success-box img {
  width: 80px;
}
.permit-upload {
  transform: translateX(29vw);
  margin-top: 8px;
}
.star {
  position: relative;
}
/deep/ .star > div:nth-child(1)::before {
  content: "*";
  color: red;
  position: absolute;
  left: 5px;
  top: 14px;
}
.text-star {
  position: relative;
}
.text-star::before {
  content: "*";
  color: red;
  position: absolute;
  left: -12px;
  top: 6px;
}
.sub-title {
  font-size: 16px;
  margin-left: 16px;
  margin-top: 12px;
}
.title > span {
  color: red;
  font-size: 12px;
}
/deep/ .star-pro > div:nth-child(1)::before {
  top: 13px;
}
.padding-12 {
  padding-left: 12px;
}
.btn-box-1 {
  position: fixed;
  height: 60px;
  width: 100%;
  bottom: 0;
  background-color: #fff;
  display: flex;
  align-items: center;
  .van-button {
    width: 80%;
    margin: 0 auto;
  }
}
.btn-box {
  position: fixed;
  height: 60px;
  width: 90%;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  > button {
    width: 46%;
  }
}
.undertone {
  background-color: rgba(41, 190, 188, 0.15) !important;
  border-color: rgba(41, 190, 188, 0.15) !important;
  color: #29bebc !important;
}
/deep/ .van-uploader__wrapper {
  max-width: 60vw;
}
/deep/ .safe-box .van-field__control--custom {
  display: block;
}
/deep/ .safe-box .upload-box {
  display: flex;
  align-items: center;
}
.file-item {
  display: flex;
  align-items: center;
  > span:nth-child(1) {
    margin-right: 3px;
  }
}
/deep/ .proj-type .van-radio__label {
  height: 28px;
  display: flex;
  align-items: center;
}
</style>
