<template>
  <div>
    <Header title="动火审批" @backFun="backFn"></Header>
    <van-tabs v-model="activeName" sticky color="#29BEBC" @change="tabChange" offset-top="60px">
      <van-tab title="待审批" name="a">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" offset="100" :immediate-check="false">
          <div class="card" v-for="item in approveList" :key="item.id" @click="goDetails(item.id)">
            <div class="status2">
              <van-button v-if="item.hotState == '0'" round type="info">申请</van-button>
              <van-button v-if="item.hotState == '2'" round type="info">结束</van-button>
            </div>
            <div>
              <span>工程名称：</span>
              <span>{{ item.projectName }}</span>
            </div>
            <div>
              <span>建设单位：</span>
              <span>{{ item.constructionUnit }}</span>
            </div>
            <div>
              <span>施工单位：</span>
              <span>{{ item.constructionUnitName }}</span>
            </div>
            <div>
              <span>工程属地：</span>
              <span>{{ item.projectTerritory }}</span>
            </div>
            <div>
              <span>建设地址：</span>
              <span>{{ item.constructionAddress }}</span>
            </div>
            <div>
              <span>申请时间：</span>
              <span>{{ item.createTime }}</span>
            </div>
          </div>
        </van-list>
      </van-tab>
      <van-tab title="已审批" name="b">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" offset="100" :immediate-check="false">
          <div class="card" v-for="item in approveList" :key="item.id" @click="goDetails(item.id)">
            <div class="status">
              <div>
                <span>工程名称：</span>
                <span>{{ item.projectName }}</span>
              </div>
              <span v-if="item.hotState == '0' && item.approvalState == '0'" class="c1">动火前-待审批</span>
              <span v-if="item.hotState == '0' && item.approvalState == '1'" class="c1">动火前-审批通过</span>
              <span v-if="item.hotState == '0' && item.approvalState == '2'" class="c2">动火前-审批驳回</span>
              <span v-if="item.hotState == '1'">动火中</span>
              <span v-if="item.hotState == '2'" class="c3">动火后-待结束</span>
              <span v-if="item.hotState == '3'" class="c3">动火后-已结束</span>
            </div>
            <div>
              <span>建设单位：</span>
              <span>{{ item.constructionUnit }}</span>
            </div>
            <div>
              <span>施工单位：</span>
              <span>{{ item.constructionUnitName }}</span>
            </div>
            <div>
              <span>工程属地：</span>
              <span>{{ item.projectTerritory }}</span>
            </div>
            <div>
              <span>建设地址：</span>
              <span>{{ item.constructionAddress }}</span>
            </div>
            <div>
              <span>申请时间：</span>
              <span>{{ item.createTime }}</span>
            </div>
          </div>
        </van-list>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import bus from "@/centralControl/bus/bus.js";
export default {
  name: "hotApprove",
  data() {
    return {
      activeName: "a",
      loading: false,
      finished: false,
      pageNo: 1,
      pageSize: 10,
      approveList: []
    };
  },
  methods: {
    backFn() {
      api.closeFrame({});
    },
    onLoad() {
      this.pageNo++;
      this.getList();
    },
    getList() {
      this.axios.postContralHostBase(
        "selectHotApprovalList",
        {
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          approvalState: this.activeName == "a" ? 0 : -1
        },
        res => {
          console.log(res);
          this.loading = false
          if (res.code == 200) {
            this.approveList = res.data.list;
            this.finished = this.approveList.length >= res.data.total;
          }
        }
      );
    },
    goDetails(id) {
      // bus.$emit("emptyData");
      this.$router.push({
        path: "/hotApproveDetail",
        query: {
          id: id,
          approvalState: this.activeName == "a" ? 0 : -1
        }
      });
    },
    tabChange(val) {
      this.pageNo = 1;
      this.approveList = [];
      this.getList();
    }
  },
  created() {
    setTimeout(() => {
      api.addEventListener(
        {
          name: "keyback666"
        },
        (ret, err) => {
          this.backFn();
        }
      );
    }, 100);
  },
  mounted() {
    this.loading = true
    this.getList();
  }
};
</script>

<style lang="scss" scoped>
/deep/ .van-tab__pane {
  min-height: calc(100vh - 100px);
}
/deep/ .van-tab__pane {
  background-color: #f2f4f9;
  padding: 0 16px;
  padding-top: 12px;
}
.card {
  background-color: #fff;
  border-radius: 8px;
  font-size: 16px;
  padding: 12px;
  margin-bottom: 12px;
  div > span:nth-child(1) {
    color: #4e5969;
  }
  div > span:nth-child(2) {
    color: #1d2129;
  }
}
.card > div {
  margin-bottom: 6px;
}
.status {
  border-bottom: 1px solid #f2f4f9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  > div {
    max-width: 64%;
  }
  > span {
    transform: translateY(-3px);
    color: #f53f3f;
  }
}
.status2 {
  border-bottom: 1px solid #f2f4f9;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .van-button {
    height: 28px;
    transform: translateY(-4px);
  }
  div > span:nth-child(1) {
    color: #fff;
  }
}
.c1 {
  color: #3562db !important;
  background-color: #e6effc;
  padding: 4px 6px;
  font-size: 14px;
}
.c2 {
  color: #f53f3f !important;
  background-color: #ffece8;
  padding: 4px 6px;
  font-size: 14px;
}
.c3 {
  color: #4e5969 !important;
  background-color: #f2f3f5;
  padding: 4px 6px;
  font-size: 14px;
}
</style>
