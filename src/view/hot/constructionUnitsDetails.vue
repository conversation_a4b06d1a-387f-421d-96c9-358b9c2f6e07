<template>
  <div>
    <Header title="施工单位申报" @backFun="backFn"></Header>
    <div class="main">
      <div class="edit-title">
        <div class="title">单位信息</div>
        <img class="edit-icon" src="@/assets/images/edit-icon.png" @click="showBtn = true" />
      </div>
      <van-field readonly clickable class="star" name="picker" :value="constructionUnitName" label="施工单位" placeholder="请选择施工单位" @click="showconstructionUnitPicker" />
      <van-popup v-model="showBuildPicker" position="bottom">
        <van-picker show-toolbar :columns="buildColumns" value-key="companyName" @confirm="onConfirmBuild" @cancel="showBuildPicker = false" />
      </van-popup>
      <van-field v-model="creditCode" class="star" name="creditCode" label="信用代码" placeholder="请输入信用代码" maxlength="18" :readonly="!showBtn" />
      <van-field v-model="corporateName" class="star" name="corporateName" label="法人姓名" placeholder="请输入法人姓名" maxlength="12" :readonly="!showBtn" />
      <van-field name="identityCard" label="法人身份证" class="star identity">
        <template #input>
          <van-uploader
            v-model="identityCardRight"
            accept="image/*"
            max-count="1"
            :after-read="afterImgRead"
            :disabled="!showBtn"
            :deletable="showBtn"
            :preview-options="{
              images: identityCardRight.map((item) => item.content || item.url),
            }"
          >
            <div class="upload-box">
              <i class="van-icon van-icon-photograph van-uploader__upload-icon"></i>
              <span>上传身份证正面照片</span>
            </div>
          </van-uploader>
          <van-uploader
            v-model="identityCardReverse"
            accept="image/*"
            max-count="1"
            :after-read="afterImgRead2"
            :disabled="!showBtn"
            :deletable="showBtn"
            :preview-options="{
              images: identityCardReverse.map((item) => item.content || item.url),
            }"
          >
            <div class="upload-box">
              <i class="van-icon van-icon-photograph van-uploader__upload-icon"></i>
              <span>上传身份证反面照片</span>
            </div>
          </van-uploader>
        </template>
      </van-field>
      <van-field name="safetyProtocol" label="施工安全协议" class="star">
        <template #input>
          <van-uploader
            v-model="safetyProtocol"
            accept="image/*"
            max-count="10"
            :after-read="afterImgRead3"
            :disabled="!showBtn"
            :deletable="showBtn"
            :preview-options="{
              images: safetyProtocol.map((item) => item.content || item.url),
            }"
          >
            <!-- <div class="upload-box pro">
              <i class="van-icon van-icon-photograph van-uploader__upload-icon"></i>
              <span>上传施工安全协议照片</span>
            </div> -->
          </van-uploader>
        </template>
      </van-field>
      <van-field name="safetyCommitment" label="消防安全承诺" class="star">
        <template #input>
          <van-uploader
            v-model="safetyCommitment"
            accept="image/*"
            max-count="10"
            :after-read="afterImgRead4"
            :disabled="!showBtn"
            :deletable="showBtn"
            :preview-options="{
              images: safetyCommitment.map((item) => item.content || item.url),
            }"
          >
            <!-- <div class="upload-box pro">
              <i class="van-icon van-icon-photograph van-uploader__upload-icon"></i>
              <span>上传消防安全承诺照片</span>
            </div> -->
          </van-uploader>
        </template>
      </van-field>
      <div class="title">人员信息</div>
      <div class="staff-item" v-for="item in staffInfoList" :key="item.id">
        <van-field v-model="item.constructionStaffName" class="star" name="constructionStaffName" label="姓名" placeholder="请输入姓名" maxlength="12" :readonly="!showBtn" />
        <van-field name="gender" label="性别" class="star">
          <template #input>
            <van-radio-group v-model="item.gender" direction="horizontal" :disabled="!showBtn">
              <van-radio name="0" checked-color="#29BEBC">男</van-radio>
              <van-radio name="1" checked-color="#29BEBC">女</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          v-model="item.cellPhoneNumber"
          class="star"
          name="cellPhoneNumber"
          label="手机号"
          placeholder="请输入手机号"
          maxlength="11"
          :formatter="phoneFormatter"
          :readonly="!showBtn"
        />
        <van-field
          v-model="item.idNumber"
          class="star"
          name="idNumber"
          label="身份证号"
          placeholder="请输入身份证号"
          maxlength="18"
          :formatter="idFormatter"
          :readonly="!showBtn"
        />
        <van-field name="qualificationCertificate" label="资质证书" class="star">
          <template #input>
            <van-uploader
              v-model="item.qualificationCertificateArr"
              accept="image/*"
              max-count="10"
              @delete="(file) => handleDelete(file, item.qualificationCertificateArr)"
              :after-read="(file) => afterImgReadCommon(file, item.qualificationCertificateArr)"
              :disabled="!showBtn"
              :deletable="showBtn"
              :preview-options="{
                images: item.qualificationCertificateArr.map((item) => item.content || item.url),
              }"
            >
              <!-- <div class="upload-box pro">
                <i class="van-icon van-icon-photograph van-uploader__upload-icon"></i>
                <span>资质证书照片</span>
              </div> -->
            </van-uploader>
          </template>
        </van-field>
      </div>
      <div class="btn-box">
        <van-button color="#29BEBC" @click="submit" block v-if="showBtn">提交</van-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "constructionUnitsDetails",
  data() {
    return {
      detailData: "",
      constructionUnitName: "",
      constructionUnitId: "",
      constructionUnitCode: "",
      showBuildPicker: false,
      buildColumns: [],
      creditCode: "",
      corporateName: "",
      identityCardRight: [],
      identityCardReverse: [],
      safetyProtocol: [],
      safetyCommitment: [],
      staffInfoList: [],
      showBtn: false,
    };
  },
  mounted() {
    this.getData();
  },
  created() {
    setTimeout(() => {
      api.addEventListener(
        {
          name: "keyback666",
        },
        (ret, err) => {
          this.backFn();
        }
      );
    }, 100);
  },
  methods: {
    showconstructionUnitPicker() {
      if (this.showBtn) {
        if (this.buildColumns.length == 0) {
          return this.$toast("暂无建设单位可选");
        }
        this.showBuildPicker = true;
      }
    },
    phoneFormatter(val) {
      return val.replace(/\D/g, "");
    },
    idFormatter(val) {
      return val.replace(/[^0-9xX]/g, "").slice(0, 18);
    },
    backFn() {
      // this.$router.go(-1);
      this.$router.push("/constructionApplication");
    },
    getBuildList() {
      this.axios.postContralHostBase("getOutSourcedCompanyList", {}, (res) => {
        console.log(res);
        this.buildColumns = res.data;
      });
    },
    getData() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        overlay: true,
        message: "加载中...",
      });
      this.axios.postContralHostBase(
        "constructionUnitDetails",
        {
          id: this.$route.query.id,
        },
        (res) => {
          this.$toast.clear();
          console.log(res);
          if (res.code == 200) {
            this.detailData = res.data;
            this.initData();
            this.getBuildList();
          }
        }
      );
    },
    initData() {
      this.constructionUnitName = this.detailData.constructionUnitName;
      this.constructionUnitId = this.detailData.constructionUnitId;
      this.constructionUnitCode = this.detailData.constructionUnitCode;
      this.creditCode = this.detailData.creditCode;
      this.corporateName = this.detailData.legalPersonName;
      this.identityCardRight = [
        {
          url: this.detailData.corporateIdentityCardFront,
        },
      ];
      this.identityCardReverse = [
        {
          url: this.detailData.corporateIdentityCardReversed,
        },
      ];
      this.detailData.securityProtocol &&
        this.detailData.securityProtocol.split(",").forEach((item) => {
          this.safetyProtocol.push({
            url: item,
          });
        });
      this.detailData.safetyCommitment &&
        this.detailData.safetyCommitment.split(",").forEach((item) => {
          this.safetyCommitment.push({
            url: item,
          });
        });
      this.staffInfoList = JSON.parse(this.detailData.constructionStaff);
      this.staffInfoList.forEach((item) => {
        item.qualificationCertificateArr = [];
        item.qualificationCertificate &&
          item.qualificationCertificate.split(",").forEach((item2) => {
            item.qualificationCertificateArr.push({
              url: item2,
            });
          });
      });
    },
    onConfirmBuild(val) {
      this.constructionUnitName = val.companyName;
      this.constructionUnitId = val.id;
      this.constructionUnitCode = val.companyCode;
      this.showBuildPicker = false;
    },
    afterImgRead(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.identityCardRight[this.identityCardRight.length - 1].url = data.fileKey;
        }
      });
    },
    afterImgRead2(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.identityCardReverse[this.identityCardReverse.length - 1].url = data.fileKey;
        }
      });
    },
    afterImgRead3(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.safetyProtocol[this.safetyProtocol.length - 1].url = data.fileKey;
        }
      });
    },
    afterImgRead4(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.safetyCommitment[this.safetyCommitment.length - 1].url = data.fileKey;
        }
      });
    },
    afterImgReadCommon(file, arr) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          arr[arr.length - 1].url = data.fileKey;
          this.$forceUpdate();
        }
      });
    },
    handleDelete(file, arr) {
      arr.splice(arr.indexOf(file), 1);
      // 强制更新
      this.$forceUpdate();
    },
    submit() {
      if (this.identityCardRight[0].url.match(/https:\/\/[^/]+\/(.+)/)) {
        this.identityCardRight[0].url = this.identityCardRight[0].url.match(/https:\/\/[^/]+\/(.+)/)[1];
      }
      if (this.identityCardReverse[0].url.match(/https:\/\/[^/]+\/(.+)/)) {
        this.identityCardReverse[0].url = this.identityCardReverse[0].url.match(/https:\/\/[^/]+\/(.+)/)[1];
      }
      this.safetyProtocol = this.safetyProtocol.map((item) => {
        if (item.url.match(/https:\/\/[^/]+\/(.+)/)) {
          item.url = item.url.match(/https:\/\/[^/]+\/(.+)/)[1];
        }
        return item;
      });
      this.safetyCommitment = this.safetyCommitment.map((item) => {
        if (item.url.match(/https:\/\/[^/]+\/(.+)/)) {
          item.url = item.url.match(/https:\/\/[^/]+\/(.+)/)[1];
        }
        return item;
      });
      let arr = [];
      this.staffInfoList.forEach((item) => {
        // if (item.qualificationCertificateArr.length > 0 && item.qualificationCertificateArr[0].url.match(/https:\/\/[^/]+\/(.+)/)) {
        //   item.qualificationCertificateArr[0].url = item.qualificationCertificateArr[0].url.match(/https:\/\/[^/]+\/(.+)/)[1];
        // }
        // 支持多个图片
        item.qualificationCertificateArr = item.qualificationCertificateArr.map((item2) => {
          if (item2.url.match(/https:\/\/[^/]+\/(.+)/)) {
            item2.url = item2.url.match(/https:\/\/[^/]+\/(.+)/)[1];
          }
          return item2;
        });
        arr.push({
          constructionStaffName: item.constructionStaffName,
          gender: item.gender,
          cellPhoneNumber: item.cellPhoneNumber,
          idNumber: item.idNumber,
          qualificationCertificate: item.qualificationCertificateArr.length > 0 ? item.qualificationCertificateArr.map((item3) => item3.url).join(",") : "",
        });
      });
      let params = {
        id: this.detailData.id,
        constructionUnitName: this.constructionUnitName,
        constructionUnitId: this.constructionUnitId,
        constructionUnitCode: this.constructionUnitCode,
        legalPersonName: this.corporateName,
        corporateIdentityCardFront: this.identityCardRight.length > 0 ? this.identityCardRight[0].url : "",
        corporateIdentityCardReversed: this.identityCardReverse.length > 0 ? this.identityCardReverse[0].url : "",
        securityProtocol: this.safetyProtocol.length > 0 ? this.safetyProtocol.map((item) => item.url).join(",") : "",
        safetyCommitment: this.safetyCommitment.length > 0 ? this.safetyCommitment.map((item) => item.url).join(",") : "",
        creditCode: this.creditCode,
        constructionStaff: JSON.stringify(arr),
      };
      // return console.log(params);
      this.axios.postContralHostBase("editConstructionUnit", params, (res) => {
        console.log(res);
        if (res.code == 200) {
          this.$toast("修改成功");
          // 清空图片
          this.identityCardRight = [];
          this.identityCardReverse = [];
          this.safetyProtocol = [];
          this.safetyCommitment = [];
          this.getData();
          this.showBtn = false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.main {
  padding: 0 16px;
}
.title {
  position: relative;
  font-size: 18px;
  margin: 12px 0;
  padding-left: 12px;
}
.title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  width: 3px;
  height: 80%;
  background-color: #29bebc;
  transform: translateY(-50%);
}
.star {
  position: relative;
}
/deep/ .star > div:nth-child(1)::before {
  content: "*";
  color: red;
  position: absolute;
  left: 5px;
  top: 14px;
}
.upload-box {
  width: 60vw;
  height: 150px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f7f8fa;
  i {
    font-size: 38px;
  }
  span {
    width: 100%;
    position: absolute;
    font-size: 14px;
    left: 50%;
    transform: translateX(-50%);
    bottom: 12px;
    text-align: center;
    color: #ccc;
  }
}
/deep/ .identity .van-field__control--custom {
  flex-wrap: wrap;
}
.identity .van-uploader:nth-child(1) {
  margin-bottom: 12px;
}
/deep/ .identity .van-uploader__preview .van-uploader__preview-image {
  width: 60vw;
  height: 150px;
}
.safety .upload-box {
  width: 45vw;
  height: 150px;
}
/deep/ .safety .van-uploader__preview .van-uploader__preview-image {
  width: 45vw;
  height: 150px;
}
.staff-item {
  border-bottom: 1px solid #f2f3f5;
}
.btn-box {
  margin-top: 12px;
}
.edit-icon {
  width: 24px;
  height: 24px;
}
.edit-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
