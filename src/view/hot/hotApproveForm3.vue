<template>
  <div>
    <Header title="待审批" @backFun="backFn"></Header>
    <div v-show="test" class="sign" style="position: fixed; top: 0; z-index: 9999; bottom: 0">
      <signature @closePop="test = false" @saveImg="complete"></signature>
    </div>
    <div class="container">
      <div class="tips">
        <span style="color: red">*</span> 请逐一检查现场人员、资质、消防设备、隐患点、制度、应急预案是否满足动火要求，并上传现场照片和视频。若有一项不满足要求需要整改后再审批。
      </div>
      <div class="title">第一项</div>
      <van-field name="radio" label="现场人员是否与申报一致" label-width="180" class="star">
        <template #input>
          <van-radio-group v-model="item1Val" direction="horizontal">
            <van-radio name="0" checked-color="#29BEBC">是</van-radio>
            <van-radio name="1" checked-color="#29BEBC">否</van-radio>
          </van-radio-group>
        </template>
      </van-field>
      <van-field v-model="item1ValText" name="文字记录" label="文字记录" placeholder="请输入文字记录" maxlength="200" />
      <van-field label="现场照片和视频" label-width="128" class="star">
        <template #input>
          <van-uploader
            v-model="imageUrl"
            accept="image/*"
            max-count="5"
            :after-read="afterImgRead"
            upload-text="添加图片"
            :preview-options="{
              images: imageUrl.map((item) => item.content),
            }"
          />
          <van-uploader v-model="videoUrl" accept="video/*" max-count="1" :after-read="afterVideoRead" upload-text="添加视频" @oversize="onOversize" :max-size="50 * 1024 * 1024" />
        </template>
      </van-field>

      <div class="title">第二项</div>
      <van-field name="radio" label="消防设备是否合规" label-width="180" class="star">
        <template #input>
          <van-radio-group v-model="item3Val" direction="horizontal">
            <van-radio name="0" checked-color="#29BEBC">是</van-radio>
            <van-radio name="1" checked-color="#29BEBC">否</van-radio>
          </van-radio-group>
        </template>
      </van-field>
      <van-field v-model="item3ValText" name="文字记录" label="文字记录" placeholder="请输入文字记录" maxlength="200" />
      <van-field label="现场照片和视频" label-width="128" class="star">
        <template #input>
          <van-uploader
            v-model="imageUrl3"
            accept="image/*"
            max-count="5"
            :after-read="afterImgRead3"
            upload-text="添加图片"
            :preview-options="{
              images: imageUrl3.map((item) => item.content),
            }"
          />
          <van-uploader
            v-model="videoUrl3"
            accept="video/*"
            max-count="1"
            :after-read="afterVideoRead3"
            upload-text="添加视频"
            @oversize="onOversize"
            :max-size="50 * 1024 * 1024"
          />
        </template>
      </van-field>

      <div class="title">第三项</div>
      <van-field name="radio" label="现场无安全隐患" label-width="180" class="star">
        <template #input>
          <van-radio-group v-model="item4Val" direction="horizontal">
            <van-radio name="0" checked-color="#29BEBC">是</van-radio>
            <van-radio name="1" checked-color="#29BEBC">否</van-radio>
          </van-radio-group>
        </template>
      </van-field>
      <van-field v-model="item4ValText" name="文字记录" label="文字记录" placeholder="请输入文字记录" maxlength="200" />
      <van-field label="现场照片和视频" label-width="128" class="star">
        <template #input>
          <van-uploader
            v-model="imageUrl4"
            accept="image/*"
            max-count="5"
            :after-read="afterImgRead4"
            upload-text="添加图片"
            :preview-options="{
              images: imageUrl4.map((item) => item.content),
            }"
          />
          <van-uploader
            v-model="videoUrl4"
            accept="video/*"
            max-count="1"
            :after-read="afterVideoRead4"
            upload-text="添加视频"
            @oversize="onOversize"
            :max-size="50 * 1024 * 1024"
          />
        </template>
      </van-field>
      <van-button block type="info" color="#29BEBC" @click="handleSubmit">提交</van-button>
    </div>
  </div>
</template>

<script>
import signature from "@/view/jobManagement/components/signature.vue";
import YBS from "@/centralControl/utils/utils.js";
import bus from "@/centralControl/bus/bus.js";
export default {
  name: "hotApproveForm3",
  components: {
    signature,
  },
  data() {
    return {
      detailData: "",
      id: "",
      item1Val: "",
      item1ValText: "",
      imageUrl: [],
      videoUrl: [],
      item2Val: "",
      item2ValText: "",
      imageUrl2: [],
      videoUrl2: [],
      item3Val: "",
      item3ValText: "",
      imageUrl3: [],
      videoUrl3: [],
      item4Val: "",
      item4ValText: "",
      imageUrl4: [],
      videoUrl4: [],
      item5Val: "",
      item5ValText: "",
      imageUrl5: [],
      videoUrl5: [],
      item6Val: "",
      item6ValText: "",
      imageUrl6: [],
      videoUrl6: [],
      approvalAuthorityOpinion: "",
      imgData: "",
      test: false,
      approvalAuthorityResult: "",
    };
  },
  created() {
    this.detailData = JSON.parse(this.$route.query.detailData);
    this.id = this.$route.query.id;
  },
  mounted() {
    // 回到页面最顶部
    window.scrollTo(0, 0);
    setTimeout(() => {
      this.power();
    }, 1000);
    // bus.$on("emptyData", (val) => {
    //   this.empty();
    // });
  },
  methods: {
    onOversize() {
      this.$toast.fail("文件大小不能超过50M");
    },
    empty() {
      // 清空数据
      this.item1Val = "";
      this.item1ValText = "";
      this.imageUrl = [];
      this.videoUrl = [];
      this.item2Val = "";
      this.item2ValText = "";
      this.imageUrl2 = [];
      this.videoUrl2 = [];
      this.item3Val = "";
      this.item3ValText = "";
      this.imageUrl3 = [];
      this.videoUrl3 = [];
      this.item4Val = "";
      this.item4ValText = "";
      this.imageUrl4 = [];
      this.videoUrl4 = [];
      this.item5Val = "";
      this.item5ValText = "";
      this.imageUrl5 = [];
      this.videoUrl5 = [];
      this.item6Val = "";
      this.item6ValText = "";
      this.imageUrl6 = [];
      this.videoUrl6 = [];
    },
    power() {
      if (!YBS.hasPermission("storage")) {
        let pageParam = {
          title: "存储权限使用说明",
          cont: "用于存储图片、视频等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["storage"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
              if (!YBS.hasPermission("camera")) {
                let pageParam = {
                  title: "摄像机权限使用说明",
                  cont: "用于扫描巡检码、空间码、拍照上传等场景"
                };
                YBS.openCustomDialog(pageParam, function () {
                  YBS.reqPermission(["camera"], function (ret) {
                    if (ret && ret.list.length > 0 && ret.list[0].granted) {
                    }
                  });
                })
                return;
              }
            }
          });
        })
        return;
      }
      if (!YBS.hasPermission("camera")) {
        let pageParam = {
          title: "摄像机权限使用说明",
          cont: "用于扫描巡检码、空间码、拍照上传等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["camera"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
            }
          });
        })
        return;
      }
    },
    backFn() {
      this.$router.go(-1);
    },
    afterImgRead(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.imageUrl[this.imageUrl.length - 1].url = data.fileKey;
        }
      });
    },
    afterVideoRead(file) {
      // 增加提示框
      this.$toast.loading({
        message: "上传中...",
        forbidClick: true,
        duration: 0,
      });
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        this.$toast.clear();
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.videoUrl[this.videoUrl.length - 1].url = data.fileKey;
        } else {
          this.$toast.fail("上传失败");
        }
      });
    },
    afterImgRead2(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.imageUrl2[this.imageUrl2.length - 1].url = data.fileKey;
        }
      });
    },
    afterVideoRead2(file) {
      this.$toast.loading({
        message: "上传中...",
        forbidClick: true,
        duration: 0,
      });
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        this.$toast.clear();
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.videoUrl2[this.videoUrl2.length - 1].url = data.fileKey;
        } else {
          this.$toast.fail("上传失败");
        }
      });
    },
    afterImgRead3(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.imageUrl3[this.imageUrl3.length - 1].url = data.fileKey;
        }
      });
    },
    afterVideoRead3(file) {
      this.$toast.loading({
        message: "上传中...",
        forbidClick: true,
        duration: 0,
      });
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        this.$toast.clear();
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.videoUrl3[this.videoUrl3.length - 1].url = data.fileKey;
        } else {
          this.$toast.fail("上传失败");
        }
      });
    },
    afterImgRead4(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.imageUrl4[this.imageUrl4.length - 1].url = data.fileKey;
        }
      });
    },
    afterVideoRead4(file) {
      this.$toast.loading({
        message: "上传中...",
        forbidClick: true,
        duration: 0,
      });
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        this.$toast.clear();
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.videoUrl4[this.videoUrl4.length - 1].url = data.fileKey;
        } else {
          this.$toast.fail("上传失败");
        }
      });
    },
    afterImgRead5(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.imageUrl5[this.imageUrl5.length - 1].url = data.fileKey;
        }
      });
    },
    afterVideoRead5(file) {
      this.$toast.loading({
        message: "上传中...",
        forbidClick: true,
        duration: 0,
      });
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        this.$toast.clear();
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.videoUrl5[this.videoUrl5.length - 1].url = data.fileKey;
        } else {
          this.$toast.fail("上传失败");
        }
      });
    },
    afterImgRead6(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          console.log(data);
          file.status = "success";
          file.message = "上传成功";
          this.imageUrl6[this.imageUrl6.length - 1].url = data.fileKey;
        }
      });
    },
    afterVideoRead6(file) {
      this.$toast.loading({
        message: "上传中...",
        forbidClick: true,
        duration: 0,
      });
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        console.log(res);
        const { code, data, message } = res;
        this.$toast.clear();
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.videoUrl6[this.videoUrl6.length - 1].url = data.fileKey;
        } else {
          this.$toast.fail("上传失败");
        }
      });
    },
    showPopup() {
      this.test = !this.test;
    },
    complete(data) {
      if (!data) return this.$message.error("保存签名失败");
      this.test = false;
      this.imgData = data;
      this.sign = "base64";
      // this.saveApi({ signature: data });
    },
    handleSubmit() {
      // 必填校验
      if (!this.item1Val) return this.$toast.fail("现场人员是否与申报一致不能为空");
      // if (!this.item1ValText) return this.$toast.fail("现场人员是否与申报一致文字记录不能为空");
      // if (!this.item2Val) return this.$toast.fail("资质是否与申报一致不能为空");
      // if (!this.item2ValText) return this.$toast.fail("资质是否与申报一致文字记录不能为空");
      if (!this.item3Val) return this.$toast.fail("消防设备是否合规不能为空");
      // if (!this.item3ValText) return this.$toast.fail("消防设备是否合规文字记录不能为空");
      if (!this.item4Val) return this.$toast.fail("现场无安全隐患不能为空");
      // if (!this.item4ValText) return this.$toast.fail("现场无安全隐患文字记录不能为空");
      // if (!this.item5Val) return this.$toast.fail("现场是否有相关制度不能为空");
      // if (!this.item5ValText) return this.$toast.fail("现场是否有相关制度文字记录不能为空");
      // if (!this.item6Val) return this.$toast.fail("现场是否有应急预案不能为空");
      // if (!this.item6ValText) return this.$toast.fail("现场是否有应急预案文字记录不能为空");
      // 视频和图片必须有一个
      if (!this.imageUrl.length && !this.videoUrl.length) return this.$toast.fail("现场照片和视频不能为空");
      // if (!this.imageUrl2.length && !this.videoUrl2.length) return this.$toast.fail("现场照片和视频不能为空");
      if (!this.imageUrl3.length && !this.videoUrl3.length) return this.$toast.fail("现场照片和视频不能为空");
      if (!this.imageUrl4.length && !this.videoUrl4.length) return this.$toast.fail("现场照片和视频不能为空");
      // if (!this.imageUrl5.length && !this.videoUrl5.length) return this.$toast.fail("现场照片和视频不能为空");
      // if (!this.imageUrl6.length && !this.videoUrl6.length) return this.$toast.fail("现场照片和视频不能为空");
      let approvalItemsJson = [
        {
          itemTitle: "第一项",
          itemSort: 1,
          itemName: "现场人员是否与申报一致",
          isTrue: this.item1Val,
          transcript: this.item1ValText,
          // 多张图片用逗号拼接
          imageUrl: this.imageUrl.length ? this.imageUrl.map((item) => item.url).join(",") : "",
          videoUrl: this.videoUrl.length ? this.videoUrl[0].url : "",
        },
        {
          itemTitle: "第二项",
          itemSort: 3,
          itemName: "消防设备是否合规",
          isTrue: this.item3Val,
          transcript: this.item3ValText,
          imageUrl: this.imageUrl3.length ? this.imageUrl3.map((item) => item.url).join(",") : "",
          videoUrl: this.videoUrl3.length ? this.videoUrl3[0].url : "",
        },
        {
          itemTitle: "第三项",
          itemSort: 4,
          itemName: "现场无安全隐患",
          isTrue: this.item4Val,
          transcript: this.item4ValText,
          imageUrl: this.imageUrl4.length ? this.imageUrl4.map((item) => item.url).join(",") : "",
          videoUrl: this.videoUrl4.length ? this.videoUrl4[0].url : "",
        },
      ];
      // let params = {
      //   approvalStatus: this.approvalAuthorityResult,
      //   approvalOpinions: this.approvalAuthorityOpinion,
      //   approvalStatusName: this.approvalAuthorityResult == 0 ? "通过" : "驳回",
      //   approvalSignature: this.imgData,
      //   approveId: this.detailData.approveId,
      //   approvalItemsJson: JSON.stringify(approvalItemsJson)
      // };
      // 跳转到/hotApproveDetail
      this.$router.push({
        path: "/hotApproveDetail",
        query: {
          id: this.id,
          approvalItemsJson: JSON.stringify(approvalItemsJson),
          approvalState: this.$route.query.approvalState,
        },
      });
      // this.axios.postContralHostBase("approveFireApplication", { id: this.id, ...params }, res => {
      //   console.log(res);
      //   if (res.code == 200) {
      //     this.$toast.success("审批成功");
      //     this.$router.go(-2);
      //   }
      // });
    },
  },
};
</script>

<style lang="scss" scoped>
.tips {
  font-size: 14px;
}
.container {
  padding: 0 16px;
  padding-top: 12px;
}
.title {
  font-size: 18px;
  border-left: 3px solid #29bebc;
  padding-left: 12px;
  margin: 12px 0;
}
.my_flex {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 10px;
  justify-content: space-between;
  margin-top: 0.1875rem;
  font-size: 16px;
  .sign_title {
    flex: 1;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(53, 53, 53, 1);
  }
}
.signature_edit {
  width: 252px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #00cac8;

  .no_singature {
    display: inline-block;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #00cac8;
    line-height: 22px;
  }

  img {
    width: 16px;
    margin-right: 4px;
  }
}
.resign-txt {
  color: #00cac8;
  font-style: normal;
  margin: auto 0;
  text-align: center;
}
.resign {
  flex: 3;
  text-align: center;
  display: flex;
}
.sign-img {
  // width: 60px;
  height: 60px;
  margin: auto;
  // transform: rotate(90deg);
}
.star {
  position: relative;
}
/deep/ .star > div:nth-child(1)::before {
  content: "*";
  color: red;
  position: absolute;
  left: 5px;
  top: 14px;
}
/deep/ .van-field__control--custom {
  display: block;
}
/deep/ .van-radio-group--horizontal {
  flex-wrap: nowrap;
}
</style>
