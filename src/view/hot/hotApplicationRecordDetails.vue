<template>
  <div>
    <Header title="详情" @backFun="backFn"></Header>
    <van-tabs v-model="activeName" color="#29BEBC">
      <van-tab title="申请信息" name="1">
        <van-collapse v-model="activeNames">
          <van-collapse-item title="工程信息" name="1">
            <template #value>
              <van-button type="primary" color="#29BEBC" size="mini" @click.stop="edit">编辑</van-button>
            </template>
            <div class="form-box">
              <van-field name="projectType" label="工程类型" class="star">
                <template #input>
                  <van-radio-group v-model="projectType" direction="horizontal" :disabled="!isEdit">
                    <van-radio name="1" checked-color="#29BEBC">改建（扩建/内装修）</van-radio>
                    <van-radio name="2" checked-color="#29BEBC">新建</van-radio>
                    <van-radio name="3" checked-color="#29BEBC" style="margin-top: 10px">维修</van-radio>
                  </van-radio-group>
                </template>
              </van-field>
              <van-field
                readonly
                clickable
                class="star"
                name="picker"
                :value="constructionPermitName"
                label="施工许可"
                placeholder="请选择施工许可"
                @click="
                  if (isEdit) {
                    showPermitPicker = true;
                  }
                "
              />
              <van-popup v-model="showPermitPicker" position="bottom">
                <van-picker show-toolbar :columns="permitColumns" value-key="dictLabel" @confirm="onConfirmPermit" @cancel="showPermitPicker = false" />
              </van-popup>
              <van-uploader
                :disabled="!isEdit"
                class="permit-upload"
                v-model="constructionPermit"
                :max-size="50 * 1024 * 1024"
                accept=".jpg,.jpeg,.png,application/msword,application/pdf"
                max-count="10"
                :after-read="afterImgRead"
                :deletable="isEdit"
                :preview-options="{
                  images: constructionPermit.map((item) => item.content || item.url),
                }"
              />
              <van-field v-model="constructionUnit" class="star" name="建设单位" label="建设单位" readonly />
              <van-popup v-model="showBuildPicker" position="bottom">
                <van-picker show-toolbar :columns="buildColumns" value-key="constructionUnitName" @confirm="onConfirmBuild" @cancel="showBuildPicker = false" />
              </van-popup>
              <van-field v-model="creditCode" class="star" name="信用代码" label="信用代码" readonly />
              <van-field
                readonly
                clickable
                class="star"
                name="picker"
                :value="industryAuthoritiesName"
                label="行业主管部门"
                placeholder="请选择行业主管部门"
                @click="
                  if (isEdit) {
                    showPartPicker = true;
                  }
                "
              />
              <van-popup v-model="showPartPicker" position="bottom">
                <van-picker show-toolbar :columns="partColumns" value-key="dictLabel" @confirm="onConfirmPart" @cancel="showPartPicker = false" />
              </van-popup>
              <van-field
                readonly
                clickable
                v-model="projectName"
                class="star"
                name="工程名称"
                label="工程名称"
                placeholder="请输入工程名称"
                @click="
                  if (isEdit) {
                    showProjectName = true;
                  }
                "
              />
              <van-popup v-model="showProjectName" position="bottom">
                <van-picker value-key="hotUser" show-toolbar :columns="projectNameList.map((i) => i.dictName)" @confirm="confirmProjectName" @cancel="showProjectName = false" />
              </van-popup>
              <van-field
                readonly
                clickable
                class="star"
                name="picker"
                :value="constructionUnitName"
                label="施工单位"
                placeholder="请选择施工单位"
                @click="
                  if (isEdit) {
                    showBuildPicker = true;
                  }
                "
              />
              <van-field
                readonly
                clickable
                class="star"
                name="area"
                :value="projectTerritory"
                label="工程属地"
                placeholder="选择区/街道"
                @click="
                  if (isEdit) {
                    showArea = true;
                  }
                "
              />
              <van-popup v-model="showArea" position="bottom">
                <van-cascader
                  v-model="cascaderValue"
                  title="请选择所在地区"
                  :field-names="{ text: 'name', value: 'code', children: 'children' }"
                  :options="areaList"
                  @close="showArea = false"
                  @finish="onConfirmArea"
                  active-color="#29BEBC"
                />
              </van-popup>
              <van-field
                v-model="constructionAddress"
                class="star"
                name="建设地址"
                label="建设地址"
                placeholder="请输入详细建设地址"
                readonly
                clickable
                @click="
                  if (isEdit) {
                    showAddress = true;
                  }
                "
              />
              <van-popup v-model="showAddress" position="bottom">
                <van-picker value-key="address" show-toolbar :columns="addressList.map((i) => i.dictName)" @confirm="confirmAddress" @cancel="showAddress = false" />
              </van-popup>
              <van-field v-model="constructionScale" class="star" name="建设规模" label="建设规模" placeholder="请输入建设规模" :readonly="!isEdit" />
              <van-field
                readonly
                clickable
                name="picker"
                :value="licensingAuthorityName"
                label="许可发证机关"
                placeholder="请选择许可发证机关"
                @click="
                  if (isEdit) {
                    showOrganPicker = true;
                  }
                "
              />
              <van-popup v-model="showOrganPicker" position="bottom">
                <van-picker show-toolbar :columns="organColumns" value-key="dictLabel" @confirm="onConfirmOrgan" @cancel="showOrganPicker = false" />
              </van-popup>
            </div>
          </van-collapse-item>
          <van-collapse-item title="动火人信息" name="2">
            <div class="form-box">
              <van-field v-model="hotSpot" class="star" name="动火部位" label="动火部位" placeholder="请输入动火部位" :readonly="!isEdit" />
              <van-field
                v-model="hotPersonName"
                class="star"
                name="动火人姓名"
                label="动火人姓名"
                placeholder="请选择动火人姓名"
                readonly
                clickable
                @click="
                  if (isEdit) {
                    showHotUser = true;
                  }
                "
              />
              <van-popup v-model="showHotUser" position="bottom">
                <van-picker value-key="hotUser" show-toolbar :columns="hotUserList.map((i) => i.name)" @confirm="confirmUser" @cancel="showHotUser = false" />
              </van-popup>
              <van-field v-model="hotPhone" class="star" name="电话" label="电话" placeholder="请输入动火人电话" :readonly="!isEdit" :formatter="telFormatter" />
              <van-field
                readonly
                clickable
                name="hotStartTime"
                :value="hotStartTime"
                label="动火开始时间"
                placeholder="请选择动火开始时间"
                @click="
                  if (isEdit) {
                    showStartTimePicker = true;
                  }
                "
                :rules="[{ required: true, message: '' }]"
                class="star"
              />
              <van-popup v-model="showStartTimePicker" position="bottom">
                <van-datetime-picker type="datetime" v-model="currentDate" @confirm="onStartTimeConfirm" @cancel="showStartTimePicker = false" />
              </van-popup>
              <van-field
                readonly
                clickable
                name="hotEndTime"
                :value="hotEndTime"
                label="动火结束时间"
                placeholder="请选择动火结束时间"
                @click="
                  if (isEdit) {
                    showEndTimePicker = true;
                  }
                "
                :rules="[{ required: true, message: '' }]"
                class="star"
              />
              <van-popup v-model="showEndTimePicker" position="bottom">
                <van-datetime-picker type="datetime" v-model="currentDate2" :min-date="currentDate" @confirm="onEndTimeConfirm" @cancel="showEndTimePicker = false" />
              </van-popup>
              <van-field
                v-model="hotContent"
                class="star star-pro"
                rows="2"
                autosize
                label="动火内容"
                type="textarea"
                maxlength="500"
                placeholder="请输入动火内容"
                show-word-limit
                :readonly="!isEdit"
              />
              <van-field name="safetyMeasures" label="现场消防安全措施" class="star star-pro safe-box">
                <template #input>
                  <div class="upload-box">
                    <van-uploader
                      :disabled="!isEdit"
                      v-model="safetyMeasures"
                      :max-size="50 * 1024 * 1024"
                      accept=".jpg,.jpeg,.png,application/msword,application/pdf"
                      max-count="10"
                      :after-read="afterImgRead5"
                      :preview-image="false"
                    />
                    <!-- <span class="tips">支持上传word、pdf格式，文件大小不可超过50M</span> -->
                  </div>
                  <div class="file-item" v-for="(item, index) in safetyMeasures" :key="item.url" @click="preview(item)">
                    <span>{{ item.fileName | spliceFileName }}</span>
                    <van-icon name="cross" color="#f53f3f" v-show="isEdit" @click="delFile(index)" />
                  </div>
                </template>
              </van-field>
              <van-field
                v-model="fireExtinguishingEquipment"
                class="star star-pro"
                rows="2"
                autosize
                label="配备灭火器材"
                type="textarea"
                maxlength="500"
                placeholder="请输入灭火器材"
                show-word-limit
                :readonly="!isEdit"
              />
            </div>
          </van-collapse-item>
          <van-collapse-item title="动火证件信息" name="3">
            <van-field name="hotModeId" label="动火方式" class="star star-pro">
              <template #input>
                <van-checkbox-group v-model="hotModeId" direction="horizontal" :disabled="!isEdit">
                  <van-checkbox :name="item.dictValue" checked-color="#29BEBC" shape="square" v-for="item in hotMethodList" :key="item.dictValue">{{
                    item.dictLabel
                  }}</van-checkbox>
                </van-checkbox-group>
              </template>
            </van-field>
            <van-form v-show="showSpecial">
              <van-field name="specialWorkCertificateUrl" label="特种作业证" class="star star-pro">
                <template #input>
                  <van-uploader
                    :disabled="!isEdit"
                    v-model="specialWorkCertificateUrl"
                    :max-size="50 * 1024 * 1024"
                    accept=".jpg,.jpeg,.png,application/msword,application/pdf"
                    max-count="10"
                    :after-read="afterImgRead4"
                    :deletable="isEdit"
                    :preview-options="{
                      images: specialWorkCertificateUrl.map((item) => item.content || item.url),
                    }"
                  />
                </template>
              </van-field>
              <van-field v-model="certificateNumber" class="star" name="证号" label="证号" placeholder="请输入证件号码" :readonly="!isEdit" />
              <van-field v-model="certificateName" class="star" name="姓名" label="姓名" placeholder="请输入姓名" :readonly="!isEdit" />
              <van-field name="radio" label="性别" class="star">
                <template #input>
                  <van-radio-group v-model="certificateSex" direction="horizontal" :disabled="!isEdit">
                    <van-radio name="1">男</van-radio>
                    <van-radio name="2">女</van-radio>
                  </van-radio-group>
                </template>
              </van-field>
              <van-field v-model="jobClass" class="star" name="作业类别" label="作业类别" placeholder="请输入作业类别" :readonly="!isEdit" />
              <van-field v-model="standardOperationItems" class="star" name="准操项目" label="准操项目" placeholder="请输入准操项目" :readonly="!isEdit" />
              <van-field name="personPhoto" label="人证合照" class="star star-pro">
                <template #input>
                  <van-uploader
                    :disabled="!isEdit"
                    v-model="personPhoto"
                    :max-size="50 * 1024 * 1024"
                    accept=".jpg,.jpeg,.png,application/msword,application/pdf"
                    max-count="10"
                    :after-read="afterImgRead2"
                    :deletable="isEdit"
                    :preview-options="{
                      images: personPhoto.map((item) => item.content || item.url),
                    }"
                  />
                  <!-- <span class="tips">请动火人手持《特种作业证》合照图片。</span> -->
                </template>
              </van-field>
            </van-form>
          </van-collapse-item>
          <van-collapse-item title="监护人信息" name="4">
            <van-field
              v-model="tutelageName"
              class="star"
              name="姓名"
              label="姓名"
              placeholder="请输入监护人姓名"
              readonly
              clickable
              @click="
                if (isEdit) {
                  showTutelage = true;
                }
              "
            />
            <van-popup v-model="showTutelage" position="bottom">
              <van-picker value-key="tutelage" show-toolbar :columns="hotUserList.map((i) => i.name)" @confirm="confirmTutelage" @cancel="showTutelage = false" />
            </van-popup>
            <van-field v-model="tutelagePhone" class="star" name="电话" label="电话" placeholder="请输入监护人电话" :readonly="!isEdit" :formatter="telFormatter" />
            <van-field
              v-model="tutelageIdentityCard"
              class="star"
              name="身份证号"
              label="身份证号"
              placeholder="请输入监护人身份证号"
              :readonly="!isEdit"
              :formatter="telFormatter2"
            />
          </van-collapse-item>
          <van-collapse-item title="现场拍照" name="5">
            <van-field name="scenePhoto" label="现场照片">
              <template #input>
                <van-uploader
                  :disabled="!isEdit"
                  v-model="scenePhoto"
                  :max-size="50 * 1024 * 1024"
                  accept=".jpg,.jpeg,.png,application/msword,application/pdf"
                  max-count="10"
                  :after-read="afterImgRead3"
                  :deletable="isEdit"
                  :preview-options="{
                    images: scenePhoto.map((item) => item.content || item.url),
                  }"
                />
              </template>
            </van-field>
          </van-collapse-item>
          <van-collapse-item title="审批流" name="6">
            <div class="sub-title star-pro">是否有监理单位</div>
            <van-field name="radio">
              <template #input>
                <van-radio-group v-model="isSupervision" direction="horizontal" :disabled="!isEdit">
                  <van-radio checked-color="#29BEBC" name="1">否</van-radio>
                  <van-radio checked-color="#29BEBC" name="0">是</van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field
              v-if="isSupervision == 0"
              readonly
              clickable
              name="picker"
              :value="supervisionUnit"
              label="监理单位"
              placeholder="请选择监理单位"
              @click="
                if (isEdit) {
                  showSupervisionPicker = true;
                }
              "
              class="star"
            />
            <van-popup v-model="showSupervisionPicker" position="bottom">
              <van-search v-model="searchSupervisionUnit" placeholder="请输入搜索关键词" />
              <van-picker show-toolbar :columns="supervisionColumns" value-key="companyName" @confirm="onConfirmSupervision" @cancel="showSupervisionPicker = false" />
            </van-popup>
            <div class="sub-title">选择主管部门审批</div>
            <van-field
              readonly
              clickable
              class="star"
              name="picker"
              :value="approveName"
              label="选择审批流"
              placeholder="请选择审批流"
              @click="
                if (isEdit) {
                  showApprovePicker = true;
                }
              "
            />
            <van-popup v-model="showApprovePicker" position="bottom">
              <van-picker show-toolbar :columns="ApproveColumns" value-key="approvalName" @confirm="onConfirmApprove" @cancel="showApprovePicker = false" />
            </van-popup>
          </van-collapse-item>
        </van-collapse>
        <div class="btn-box" v-if="isEdit">
          <van-button color="#29BEBC" @click="finalSubmit">提交</van-button>
        </div>
      </van-tab>
      <van-tab title="审批记录" name="2">
        <div class="title">动火作业审批记录</div>
        <van-steps direction="vertical" :active="recordList.length - 1" active-color="#29BEBC">
          <van-step v-for="item in recordList" :key="item.id">
            <div class="record-item">
              <span>审批</span>
              <span>{{ item.operatorTime }}</span>
            </div>
            <div class="record-item">
              <span>审核结果</span>
              <span>{{ item.approvalStatus == "0" ? "同意" : "驳回" }}</span>
            </div>
            <div class="record-item">
              <span>审核意见</span>
              <span>{{ item.opinions }}</span>
            </div>
            <div class="record-item">
              <span>审核人</span>
              <span>{{ item.operatorName }}</span>
            </div>
            <div class="record-item">
              <span v-if="item.approvalNode == 8">保卫处审批人</span>
              <span v-if="item.approvalNode == 7">主管领导</span>
              <span v-if="item.approvalNode == 6">监管部门处长</span>
              <span v-if="item.approvalNode == 5">监管部门负责人</span>
              <span v-if="item.approvalNode == 4">主管部门处长</span>
              <span v-if="item.approvalNode == 3">主管部门负责人</span>
              <span v-if="item.approvalNode == 2">监理单位</span>
              <span v-if="item.approvalNode == 1">施工单位</span>
              <span>{{ item.operatingDepartmentName }}</span>
            </div>
            <div class="record-item">
              <span>签名</span>
              <img :src="item.approvalSignature" />
            </div>
            <div class="record-item">
              <span>印章（图片）</span>
              <img v-if="item.companySeal" :src="item.companySeal" />
              <span v-else>暂无</span>
            </div>
          </van-step>
        </van-steps>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeName: "1",
      activeNames: ["1", "2", "3", "4", "5", "6"],
      projectType: "1",
      constructionPermitName: "",
      constructionPermitId: "",
      showPermitPicker: false,
      permitColumns: [],
      constructionPermit: [],
      constructionUnitName: "",
      showBuildPicker: false,
      buildColumns: [],
      constructionUnitId: "",
      constructionUnitCode: "",
      creditCode: "",
      loginInfo: "",
      constructionUnit: "",
      industryAuthoritiesName: "",
      showPartPicker: false,
      partColumns: [],
      industryAuthoritiesId: "",
      projectName: "",
      projectId: "",
      projectTerritory: "",
      showArea: false,
      cascaderValue: "",
      areaList: [],
      provinceCode: "",
      provinceName: "",
      downtownCode: "",
      downtownName: "",
      areaId: "",
      areaName: "",
      streetId: "",
      streetName: "",
      constructionAddress: "",
      constructionAddressId: "",
      constructionScale: "",
      licensingAuthorityName: "",
      licensingAuthorityId: "",
      showOrganPicker: false,
      organColumns: [],
      hotSpot: "",
      hotPersonName: "",
      hotPersonId: "",
      hotPhone: "",
      hotStartTime: "",
      hotEndTime: "",
      showStartTimePicker: false,
      showEndTimePicker: false,
      minDate: new Date(),
      hotContent: "",
      safetyMeasures: [],
      fireExtinguishingEquipment: "",
      hotModeId: [],
      hotMethodList: [],
      showSpecial: false,
      specialWorkCertificateUrl: [],
      certificateNumber: "",
      certificateName: "",
      certificateSex: "1",
      jobClass: "",
      standardOperationItems: "",
      personPhoto: [],
      tutelageName: "",
      tutelageId: "",
      tutelagePhone: "",
      scenePhoto: [],
      isSupervision: "1",
      supervisionUnit: "",
      showSupervisionPicker: false,
      buildColumns2: [],
      supervisionUnitId: "",
      supervisionUnitCode: "",
      approveName: "",
      showApprovePicker: false,
      ApproveColumns: [],
      isEdit: false,
      detailData: {},
      recordList: [],
      currentDate: new Date(),
      currentDate2: new Date(),
      tutelageIdentityCard: "",
      searchSupervisionUnit: "",
      showHotUser: false,
      hotUserList: [],
      showAddress: false,
      addressList: [],
      showTutelage: false,
      showProjectName: false,
      projectNameList: [],
    };
  },
  computed: {
    supervisionColumns() {
      // 通过searchSupervisionUnit过滤buildColumns2，返回一个新数组
      let arr = this.buildColumns2.filter((item) => item.companyName.indexOf(this.searchSupervisionUnit) > -1);
      return arr;
    },
  },
  watch: {
    hotModeId(val) {
      // 遍历val，如果val中有1,2,3其中一个，就显示showSpecial
      if (val.indexOf("1") > -1 || val.indexOf("2") > -1 || val.indexOf("3") > -1) {
        this.showSpecial = true;
      } else {
        this.showSpecial = false;
      }
    },
  },
  created() {
    this.id = this.$route.query.id;
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.constructionUnit = this.loginInfo.hospitalName;
    setTimeout(() => {
      api.addEventListener(
        {
          name: "keyback666",
        },
        (ret, err) => {
          this.backFn();
        }
      );
    }, 100);
  },
  mounted() {
    this.getDetail();
    this.axios.postContralHostBase("getDictValueList", { dictType: "construction_permit" }, (res) => {
      console.log(res);
      this.permitColumns = res.data;
    });
    this.axios.postContralHostBase(
      "getBuildUnitList",
      {
        constructionUnitCode: this.loginInfo.companyCode ? this.loginInfo.companyCode : "",
      },
      (res) => {
        console.log(res);
        this.buildColumns = res.data;
      }
    );
    this.axios.postContralHostBase("getDictValueList", { dictType: "industry_authorities" }, (res) => {
      console.log(res);
      this.partColumns = res.data;
    });
    this.fetchRegions();
    this.axios.postContralHostBase("getDictValueList", { dictType: "licensing_authority" }, (res) => {
      console.log(res);
      this.organColumns = res.data;
    });
    this.axios.postContralHostBase("getDictValueList", { dictType: "hot_mode" }, (res) => {
      console.log(res);
      this.hotMethodList = res.data;
    });
    this.axios.postContralHostBase("getSourcedCompanyList", {}, (res) => {
      console.log(res);
      this.buildColumns2 = res.data;
    });
    this.axios.postContralHostBase("getApprovalSettingList", { pageNo: 1, pageSize: 20 }, (res) => {
      this.ApproveColumns = res.data.list;
      console.log(res);
    });
    this.axios.postContralHostBase(
      "getPlanType",
      {
        dictType: "building_address",
        pageNo: 1,
        pageSize: 999,
      },
      (res) => {
        if (res.code == "200") {
          this.addressList = res.data.list;
        }
      }
    );
    this.getProjectName();
  },
  filters: {
    spliceFileName(val) {
      if (val.length < 20) {
        return val;
      }
      return val.slice(0, 20) + "...";
    },
  },
  methods: {
    async fetchRegions() {
      try {
        const response = await fetch("static/ipsm_sys_area.json");
        const data = await response.json();
        this.areaList = this.utils.transData(data.data, "code", "parent_code", "children");
      } catch (error) {
        console.error("Failed to fetch regions:", error);
      }
    },
    telFormatter(val) {
      return val.replace(/\D/g, "").slice(0, 11);
    },
    telFormatter2(val) {
      // 身份证号校验,15位或者18位，最后一位可以输入字母x
      return val.replace(/[^0-9xX]/g, "").slice(0, 18);
    },
    getDetail() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        overlay: true,
        message: "加载中...",
      });
      this.axios.postContralHostBase("getFireApplication", { id: this.id }, (res) => {
        console.log(res);
        if (res.code == 200) {
          this.detailData = res.data;
          const params = {
            pageSize: 999,
            currentPage: 1,
            companyCode: res.data.constructionUnitCode,
          };
          this.axios.postContralHostBase("getOutsourcedUserList", params, (res) => {
            const { code, data, message } = res;
            if (code == 200) {
              this.hotUserList = data.list;
            } else {
              this.$toast(message || "获取外委人员失败");
            }
          });
          if (this.detailData.id) {
            this.initData();
            this.getListByBusinessId();
          }
        }
      });
    },
    initData() {
      this.cascaderValue = this.detailData.streetId;
      this.projectType = this.detailData.projectType;
      this.constructionPermitId = this.detailData.constructionPermitId;
      this.constructionPermitName = this.detailData.constructionPermitName;
      this.constructionUnit = this.detailData.constructionUnit;
      this.creditCode = this.detailData.creditCode;
      this.industryAuthoritiesId = this.detailData.industryAuthoritiesId;
      this.industryAuthoritiesName = this.detailData.industryAuthoritiesName;
      this.projectName = this.detailData.projectName;
      this.projectId = this.detailData.projectId;
      this.projectTerritory = this.detailData.projectTerritory;
      this.licensingAuthorityId = this.detailData.licensingAuthorityId;
      this.licensingAuthorityName = this.detailData.licensingAuthorityName;
      this.constructionAddress = this.detailData.constructionAddress;
      this.constructionAddressId = this.detailData.constructionAddressId;
      this.constructionScale = this.detailData.constructionScale;
      this.hotSpot = this.detailData.hotSpot;
      this.hotPersonId = this.detailData.hotPersonId;
      this.hotPersonName = this.detailData.hotPersonName;
      this.hotStartTime = this.detailData.hotStartTime;
      this.hotEndTime = this.detailData.hotEndTime;
      this.currentDate = new Date(this.detailData.hotStartTime);
      this.currentDate2 = new Date(this.detailData.hotEndTime);
      this.hotModeId = this.detailData.hotModeId.split(",");
      this.tutelageIdentityCard = this.detailData.tutelageIdentityCard;
      // 兼容多个地址
      this.specialWorkCertificateUrl = this.detailData.specialWorkCertificateUrl
        ? this.detailData.specialWorkCertificateUrl.split(",").map((item) => {
            return {
              url: item,
            };
          })
        : [];
      this.certificateNumber = this.detailData.certificateNumber;
      this.certificateName = this.detailData.certificateName;
      this.certificateSex = this.detailData.certificateSex;
      this.personPhoto = this.detailData.personPhoto
        ? this.detailData.personPhoto.split(",").map((item) => {
            return {
              url: item,
            };
          })
        : [];
      // 兼容多个地址
      this.scenePhoto = this.detailData.scenePhoto
        ? this.detailData.scenePhoto.split(",").map((item) => {
            return {
              url: item,
            };
          })
        : [];
      this.approveId = this.detailData.approveId;
      this.jobClass = this.detailData.jobClass;

      this.standardOperationItems = this.detailData.standardOperationItems;
      this.tutelageName = this.detailData.tutelageName;
      this.tutelageId = this.detailData.tutelageId;
      this.tutelagePhone = this.detailData.tutelagePhone;
      //  this.detailData.constructionPermitUrly有可能是多个地址通过逗号拼接
      this.constructionPermit = this.detailData.constructionPermitUrl
        ? this.detailData.constructionPermitUrl.split(",").map((item) => {
            return {
              url: item,
            };
          })
        : [];
      this.safetyMeasures = JSON.parse(this.detailData.fireSafetyMeasures).map((item) => {
        return {
          url: item.url,
          fileName: item.fileName,
        };
      });
      this.areaId = this.detailData.areaId;
      this.areaName = this.detailData.areaName;
      this.streetId = this.detailData.streetId;
      this.streetName = this.detailData.streetName;
      this.hotPhone = this.detailData.hotPhone;
      this.approveName = this.detailData.approveName;
      this.constructionUnitName = this.detailData.constructionUnitName;
      this.constructionUnitId = this.detailData.constructionUnitId;
      this.hotContent = this.detailData.hotWorkContent;
      this.fireExtinguishingEquipment = this.detailData.fireExtinguishingEquipment;
      this.supervisionUnitId = this.detailData.supervisionUnitId;
      this.supervisionUnit = this.detailData.supervisionUnitName;
      this.isSupervision = this.detailData.isThereAnySupervision;
      this.constructionUnitCode = this.detailData.constructionUnitCode;
      this.supervisionUnitCode = this.detailData.supervisionUnitCode;
    },
    backFn() {
      this.$router.go(-1);
    },
    edit() {
      if (this.detailData.approvalState == 1 || (this.detailData.approvalState == 0 && this.detailData.approvalNode != 1)) {
        this.$toast("审批中的数据不可编辑");
        return;
      }
      this.isEdit = true;
    },
    onConfirmPermit(val) {
      console.log(val);
      this.constructionPermitId = val.dictValue;
      this.constructionPermitName = val.dictLabel;
      this.showPermitPicker = false;
    },
    afterImgRead(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.constructionPermit[this.constructionPermit.length - 1].url = data.fileKey;
        }
      });
    },
    onConfirmBuild(val) {
      this.constructionUnitName = val.constructionUnitName;
      this.constructionUnitId = val.id;
      this.constructionUnitCode = val.constructionUnitCode;
      this.creditCode = val.creditCode;
      this.showBuildPicker = false;
      const params = {
        pageSize: 999,
        currentPage: 1,
        companyCode: this.constructionUnitCode,
      };
      this.axios.postContralHostBase("getOutsourcedUserList", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          this.hotUserList = data.list;
        } else {
          this.$toast(message || "获取外委人员失败");
        }
      });
      this.getProjectName(val.constructionUnitId);
    },
    getProjectName(id) {
      this.projectId = "";
      this.projectName = "";
      const params = {
        parentId: id || "",
        dictType: "project_name",
      };
      this.axios.postContralHostBase("getProjectNameList", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          this.projectNameList = data;
          if (id && data.length > 0) {
            this.projectId = data[0].id;
            this.projectName = data[0].dictName;
          }
        } else {
          this.$toast(message || "获取工程名称失败");
        }
      });
    },
    onConfirmPart(val) {
      console.log(val);
      this.industryAuthoritiesId = val.dictValue;
      this.industryAuthoritiesName = val.dictLabel;
      this.showPartPicker = false;
    },
    onConfirmArea(val) {
      console.log(val);
      this.provinceCode = val.selectedOptions[0].code;
      this.provinceName = val.selectedOptions[0].name;
      this.downtownCode = val.selectedOptions[1].code;
      this.downtownName = val.selectedOptions[1].name;
      this.areaId = val.selectedOptions[2].code;
      this.areaName = val.selectedOptions[2].name;
      this.streetId = val.selectedOptions[3].code;
      this.streetName = val.selectedOptions[3].name;
      this.projectTerritory = this.areaName + this.streetName;
      this.showArea = false;
    },
    onConfirmOrgan(val) {
      console.log(val);
      this.licensingAuthorityId = val.dictValue;
      this.licensingAuthorityName = val.dictLabel;
      this.showOrganPicker = false;
    },
    onStartTimeConfirm(val) {
      console.log(val);
      // 将时间格式化成yyyy-MM-dd HH:mm格式，不足两位补0
      let year = val.getFullYear();
      let month = val.getMonth() + 1;
      let day = val.getDate();
      let hour = val.getHours();
      let minute = val.getMinutes();
      month = month < 10 ? "0" + month : month;
      day = day < 10 ? "0" + day : day;
      hour = hour < 10 ? "0" + hour : hour;
      minute = minute < 10 ? "0" + minute : minute;
      this.hotStartTime = year + "-" + month + "-" + day + " " + hour + ":" + minute;
      this.showStartTimePicker = false;
    },
    onEndTimeConfirm(val) {
      console.log(val);
      // 将时间格式化成yyyy-MM-dd HH:mm格式，不足两位补0
      let year = val.getFullYear();
      let month = val.getMonth() + 1;
      let day = val.getDate();
      let hour = val.getHours();
      let minute = val.getMinutes();
      month = month < 10 ? "0" + month : month;
      day = day < 10 ? "0" + day : day;
      hour = hour < 10 ? "0" + hour : hour;
      minute = minute < 10 ? "0" + minute : minute;
      this.hotEndTime = year + "-" + month + "-" + day + " " + hour + ":" + minute;
      this.showEndTimePicker = false;
    },
    afterImgRead2(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.personPhoto[this.personPhoto.length - 1].url = data.fileKey;
        }
      });
    },
    afterImgRead3(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.scenePhoto[this.scenePhoto.length - 1].url = data.fileKey;
        }
      });
    },
    afterImgRead4(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.specialWorkCertificateUrl[this.specialWorkCertificateUrl.length - 1].url = data.fileKey;
        }
      });
    },
    afterImgRead5(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.$set(this.safetyMeasures[this.safetyMeasures.length - 1], "url", data.fileKey);
          this.$set(this.safetyMeasures[this.safetyMeasures.length - 1], "fileName", data.fileName);
        }
      });
    },
    onConfirmSupervision(val) {
      console.log(val);
      this.supervisionUnit = val.companyName;
      this.supervisionUnitId = val.id;
      this.supervisionUnitCode = val.companyCode;
      this.showSupervisionPicker = false;
    },
    onConfirmApprove(val) {
      console.log(val);
      this.approveId = val.id;
      this.approveName = val.approvalName;
      this.showApprovePicker = false;
    },
    getListByBusinessId() {
      this.axios.postContralHostBase("getListByBusinessId", { businessId: this.id, operationCode: "1", approvalVersion: this.detailData.approvalVersion }, (res) => {
        console.log(res);
        this.$toast.clear();
        if (res.code == 200) {
          this.recordList = res.data;
          if (this.approvalState == -1 && this.detailData.hotState == 3) {
            this.lastData = res.data[res.data.length - 1];
            this.recordList.pop();
          }
        }
      });
    },
    preview(item) {
      console.log(item);
      let url = item.url;
      // 判断如果url不是全路径，在前面补全https://sinomis.oss-cn-beijing.aliyuncs.com
      if (!url.match(/https:\/\/[^/]+\/(.+)/)) {
        url = __PATH.DOWNLOAD_URL + url;
      }
      api.download(
        {
          url: url,
          report: true,
          cache: true,
          allowResume: true,
        },
        function (ret, err) {
          if (ret.state == 1) {
            if (api.systemType == "ios") {
              api.openWin({
                name: "my/pdfview",
                url: "widget://html/common_window.html",
                bgColor: "rgba(250, 250, 250, 0)",
                hideHomeIndicator: true,
                bounces: false,
                scrollEnabled: false,
                useWKWebView: true,
                pageParam: {
                  title: "文件预览",
                  savePath: ret.savePath,
                  webUrl: ret.savePath,
                },
              });
            } else {
              // 安卓
              // 文档

              var docReader = api.require("docReader");
              docReader.open(
                {
                  path: ret.savePath,
                  autorotation: false,
                },
                function (ret, err) {
                  console.log(JSON.stringify(ret));
                  console.log(JSON.stringify(err));
                }
              );
            }
            console.log("下载成功");
          } else {
            console.log("下载失败");
          }
        }
      );
    },
    delFile(index) {
      this.safetyMeasures.splice(index, 1);
    },
    finalSubmit() {
      let hotNameArr = [];
      this.hotModeId.forEach((item) => {
        this.hotMethodList.forEach((item2) => {
          if (item == item2.dictValue) {
            hotNameArr.push(item2.dictLabel);
          }
        });
      });
      this.safetyMeasures = this.safetyMeasures.map((item) => {
        if (item.url.match(/https:\/\/[^/]+\/(.+)/)) {
          item.url = item.url.match(/https:\/\/[^/]+\/(.+)/)[1];
        }
        return item;
      });
      this.constructionPermit = this.constructionPermit.map((item) => {
        if (item.url.match(/https:\/\/[^/]+\/(.+)/)) {
          item.url = item.url.match(/https:\/\/[^/]+\/(.+)/)[1];
        }
        return item;
      });
      this.personPhoto = this.personPhoto.map((item) => {
        if (item.url.match(/https:\/\/[^/]+\/(.+)/)) {
          item.url = item.url.match(/https:\/\/[^/]+\/(.+)/)[1];
        }
        return item;
      });
      this.scenePhoto = this.scenePhoto.map((item) => {
        if (item.url.match(/https:\/\/[^/]+\/(.+)/)) {
          item.url = item.url.match(/https:\/\/[^/]+\/(.+)/)[1];
        }
        return item;
      });
      this.specialWorkCertificateUrl = this.specialWorkCertificateUrl.map((item) => {
        if (item.url.match(/https:\/\/[^/]+\/(.+)/)) {
          item.url = item.url.match(/https:\/\/[^/]+\/(.+)/)[1];
        }
        return item;
      });
      let params = {
        id: this.id,
        projectType: this.projectType,
        constructionPermitId: this.constructionPermitId,
        constructionPermitName: this.constructionPermitName,
        constructionUnit: this.constructionUnit,
        creditCode: this.creditCode,
        industryAuthoritiesId: this.industryAuthoritiesId,
        industryAuthoritiesName: this.industryAuthoritiesName,
        projectName: this.projectName,
        projectTerritory: this.projectTerritory,
        licensingAuthorityId: this.licensingAuthorityId,
        licensingAuthorityName: this.licensingAuthorityName,
        constructionAddress: this.constructionAddress,
        constructionAddressId: this.constructionAddressId, // 建设地址id
        constructionScale: this.constructionScale,
        hotSpot: this.hotSpot,
        hotPersonName: this.hotPersonName,
        hotPersonId: this.hotPersonId, // 动火人id
        hotStartTime: this.hotStartTime,
        hotEndTime: this.hotEndTime,
        hotModeId: this.hotModeId.join(","),
        specialWorkCertificateUrl: this.specialWorkCertificateUrl.map((item) => item.url).join(","), // 特种作业证
        certificateNumber: this.certificateNumber,
        certificateName: this.certificateName,
        certificateSex: this.certificateSex,
        personPhoto: this.personPhoto.map((item) => item.url).join(","), // 人证合照
        scenePhoto: this.scenePhoto.map((item) => item.url).join(","), // 现场照片
        approveId: this.approveId,
        jobClass: this.jobClass,
        standardOperationItems: this.standardOperationItems,
        tutelageName: this.tutelageName,
        tutelageId: this.tutelageId, // 监护人id
        tutelagePhone: this.tutelagePhone,
        constructionPermitUrl: this.constructionPermit.map((item) => item.url).join(","),
        areaId: this.areaId,
        areaName: this.areaName,
        streetId: this.streetId,
        streetName: this.streetName,
        hotPhone: this.hotPhone,
        hotModeName: hotNameArr.join(","),
        approveName: this.approveName,
        constructionUnitName: this.constructionUnitName,
        constructionUnitId: this.constructionUnitId,
        hotWorkContent: this.hotContent,
        fireExtinguishingEquipment: this.fireExtinguishingEquipment,
        supervisionUnitId: this.supervisionUnitId,
        supervisionUnitName: this.supervisionUnit,
        isThereAnySupervision: this.isSupervision,
        constructionUnitCode: this.constructionUnitCode,
        supervisionUnitCode: this.supervisionUnitCode,
        tutelageIdentityCard: this.tutelageIdentityCard,
      };
      let fireSafetyMeasuresArr = [];
      this.safetyMeasures.forEach((item) => {
        fireSafetyMeasuresArr.push({
          url: item.url,
          fileName: item.fileName,
        });
      });
      params.fireSafetyMeasures = JSON.stringify(fireSafetyMeasuresArr);
      this.axios.postContralHostBase("saveHot", params, (res) => {
        console.log(res);
        if (res.code == 200) {
          this.$toast("提交成功！");
          this.$router.go(-1);
        } else {
          this.$toast("提交失败！");
        }
      });
    },
    confirmUser(val, index) {
      this.hotPersonId = this.hotUserList[index].id;
      this.hotPersonName = this.hotUserList[index].name;
      this.hotPhone = this.hotUserList[index].mobilePhone;
      this.showHotUser = false;
    },
    confirmAddress(val, index) {
      this.constructionAddressId = this.addressList[index].id;
      this.constructionAddress = this.addressList[index].dictName;
      this.showAddress = false;
    },
    confirmTutelage(val, index) {
      this.tutelageId = this.hotUserList[index].id;
      this.tutelageName = this.hotUserList[index].name;
      this.tutelagePhone = this.hotUserList[index].mobilePhone;
      this.tutelageIdentityCard = this.hotUserList[index].identityCard || "";
      this.showTutelage = false;
    },
    confirmProjectName(val, index) {
      this.projectId = this.projectNameList[index].id;
      this.projectName = this.projectNameList[index].dictName;
      this.showProjectName = false;
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .van-collapse .van-collapse-item > .van-cell:nth-child(1) .van-cell__title {
  position: relative;
}
/deep/ .van-collapse .van-collapse-item > .van-cell:nth-child(1) .van-cell__title:before {
  position: absolute;
  top: 5px;
  left: -7px;
  content: "";
  width: 2px;
  height: 14px;
  background-color: #29bebc;
}
/deep/ .van-collapse .van-collapse-item > .van-cell:nth-child(1) .van-cell__value {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .van-button {
    transform: translateX(-10px);
    padding: 0 10px;
  }
}
.form-box .van-cell {
  padding: 10px 0;
  padding-left: 12px;
}
.permit-upload {
  transform: translateX(29vw);
  margin-top: 8px;
}
.star {
  position: relative;
}
/deep/ .star > div:nth-child(1)::before {
  content: "*";
  color: red;
  position: absolute;
  left: 0;
  top: 14px;
}
.star-pro {
  position: relative;
}
.star-pro::before {
  content: "*";
  color: red;
  position: absolute;
  left: -15px;
  top: 4px;
}
.tips {
  font-size: 12px;
  color: red;
}
.van-checkbox {
  margin-bottom: 8px;
}
.sub-title {
  font-size: 16px;
  margin-left: 16px;
  margin-top: 12px;
}
.btn-box {
  width: 90%;
  margin: 0 auto;
  margin-top: 12px;
}
.btn-box .van-button {
  width: 100%;
}
.title {
  position: relative;
  font-size: 18px;
  margin: 12px 0;
  margin-left: 12px;
  padding-left: 12px;
}
.title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  width: 3px;
  height: 80%;
  background-color: #29bebc;
  transform: translateY(-50%);
}
.record-item {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}
.record-item > span:nth-child(1) {
  display: inline-block;
  width: 88px;
}
.record-item img {
  width: 100px;
  height: 100px;
}
/deep/ .safe-box .van-field__control--custom {
  display: block;
}
/deep/ .safe-box .upload-box {
  display: flex;
  align-items: center;
}
.file-item {
  display: flex;
  align-items: center;
  > span:nth-child(1) {
    margin-right: 3px;
  }
}
</style>
