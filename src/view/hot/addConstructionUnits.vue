<template>
  <div>
    <Header title="施工单位申报" @backFun="backFn"></Header>
    <div v-show="currentStep == '1'" class="content">
      <van-field readonly clickable class="star" name="picker" :value="constructionUnitName" label="施工单位" placeholder="请选择施工单位" @click="showBuildPicker = true" />
      <van-popup v-model="showBuildPicker" position="bottom">
        <van-picker show-toolbar :columns="buildColumns" value-key="companyName" @confirm="onConfirmBuild" @cancel="showBuildPicker = false" />
      </van-popup>
      <van-field v-model="creditCode" class="star" name="creditCode" label="信用代码" placeholder="请输入信用代码" maxlength="18" :formatter="creditCodeFormatter" />
      <van-field v-model="corporateName" class="star" name="corporateName" label="法人姓名" placeholder="请输入法人姓名" maxlength="12" />
      <van-field name="identityCard" label="法人身份证" class="star identity">
        <template #input>
          <van-uploader
            v-model="identityCardRight"
            accept="image/*"
            max-count="1"
            :after-read="afterImgRead"
            :preview-options="{
              images: identityCardRight.map((item) => item.content),
            }"
          >
            <div class="upload-box">
              <i class="van-icon van-icon-photograph van-uploader__upload-icon"></i>
              <span>上传身份证正面照片</span>
            </div>
          </van-uploader>
          <van-uploader
            v-model="identityCardReverse"
            accept="image/*"
            max-count="1"
            :after-read="afterImgRead2"
            :preview-options="{
              images: identityCardReverse.map((item) => item.content),
            }"
          >
            <div class="upload-box">
              <i class="van-icon van-icon-photograph van-uploader__upload-icon"></i>
              <span>上传身份证反面照片</span>
            </div>
          </van-uploader>
        </template>
      </van-field>
      <van-field name="safetyProtocol" label="施工安全协议" class="star">
        <template #input>
          <van-uploader
            v-model="safetyProtocol"
            accept=".jpg,.jpeg,.png,application/msword,application/pdf"
            :max-size="10 * 1024 * 1024"
            max-count="10"
            :after-read="afterImgRead3"
            :preview-options="{
              images: safetyProtocol.map((item) => item.content),
            }"
          >
            <!-- <div class="upload-box pro">
              <i class="van-icon van-icon-photograph van-uploader__upload-icon"></i>
              <span>上传施工安全协议照片</span>
            </div> -->
          </van-uploader>
        </template>
      </van-field>
      <van-field name="safetyCommitment" label="消防安全承诺" class="star">
        <template #input>
          <van-uploader
            v-model="safetyCommitment"
            accept=".jpg,.jpeg,.png,application/msword,application/pdf"
            :max-size="10 * 1024 * 1024"
            max-count="10"
            :after-read="afterImgRead4"
            :preview-options="{
              images: safetyCommitment.map((item) => item.content),
            }"
          >
            <!-- <div class="upload-box pro">
              <i class="van-icon van-icon-photograph van-uploader__upload-icon"></i>
              <span>上传消防安全承诺照片</span>
            </div> -->
          </van-uploader>
        </template>
      </van-field>
    </div>
    <div v-show="currentStep == '2'" class="content">
      <div class="staff-item" v-for="(item, index) in staffInfoList" :key="item.id">
        <van-field v-model="item.constructionStaffName" class="star" name="constructionStaffName" label="姓名" placeholder="请输入姓名" maxlength="12" />
        <van-field name="gender" label="性别" class="star">
          <template #input>
            <van-radio-group v-model="item.gender" direction="horizontal">
              <van-radio name="0" checked-color="#29BEBC">男</van-radio>
              <van-radio name="1" checked-color="#29BEBC">女</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field v-model="item.cellPhoneNumber" class="star" name="cellPhoneNumber" label="手机号" placeholder="请输入手机号" maxlength="11" :formatter="phoneFormatter" />
        <van-field v-model="item.idNumber" class="star" name="idNumber" label="身份证号" placeholder="请输入身份证号" maxlength="18" :formatter="idFormatter" />
        <van-field name="qualificationCertificate" label="资质证书" class="star">
          <template #input>
            <van-uploader
              v-model="item.qualificationCertificateArr"
              accept="image/*"
              max-count="10"
              :max-size="10 * 1024 * 1024"
              :after-read="(file) => afterImgReadCommon(file, item.qualificationCertificateArr)"
              :preview-options="{
                images: item.qualificationCertificateArr.map((item) => item.content),
              }"
            >
              <!-- <div class="upload-box pro">
                <i class="van-icon van-icon-photograph van-uploader__upload-icon"></i>
                <span>资质证书照片</span>
              </div> -->
            </van-uploader>
          </template>
        </van-field>
        <div class="handle-box">
          <div @click="deletePerson(index)">
            <img src="@/assets/images/delete-icon.png" />
            <span>删除</span>
          </div>
          <div @click="addPerson">
            <img src="@/assets/images/add-icon.png" />
            <span>新增员工信息</span>
          </div>
        </div>
      </div>
    </div>
    <div class="btn-box" v-if="currentStep == '1'">
      <van-button color="#29BEBC" @click="nextStep" block>下一步</van-button>
    </div>
    <div class="btn-box2" v-if="currentStep == '2'">
      <van-button class="undertone" color="#29BEBC" @click="currentStep = '1'" block>上一步</van-button>
      <van-button color="#29BEBC" block @click="handleSubmit">提交</van-button>
    </div>
  </div>
</template>
<script>
import YBS from "@/centralControl/utils/utils.js";
export default {
  name: "addConstructionUnits",
  data() {
    return {
      constructionUnitName: "", // 建设单位名称
      showBuildPicker: false,
      buildColumns: [],
      constructionUnitId: "", // 建设单位id
      constructionUnitCode: "", // 建设单位编码
      constructPrincipalPhone: "", // 建设单位负责人电话
      constructPrincipalSeal: "", // 建设单位负责人盖章
      creditCode: "", // 信用代码
      corporateName: "", // 法人姓名
      identityCardRight: [], // 身份证正面
      identityCardReverse: [], // 身份证反面
      safetyProtocol: [], // 施工安全协议
      safetyCommitment: [], // 消防安全承诺
      currentStep: "1",
      staffInfoList: [
        {
          id: Math.random(),
          constructionStaffName: "",
          gender: "0",
          cellPhoneNumber: "",
          idNumber: "",
          qualificationCertificateArr: [],
        },
      ],
    };
  },
  methods: {
    phoneFormatter(val) {
      return val.replace(/\D/g, "");
    },
    idFormatter(val) {
      return val.replace(/[^0-9xX]/g, "").slice(0, 18);
    },
    creditCodeFormatter(val) {
      return val.replace(/[^\w\.\/]/g, "");
    },
    nextStep() {
      if (!this.constructionUnitName) {
        return this.$toast("请选择建设单位");
      }
      if (!this.creditCode) {
        return this.$toast("请输入信用代码");
      }
      if (!this.corporateName) {
        return this.$toast("请输入法人姓名");
      }
      if (this.identityCardRight.length == 0) {
        return this.$toast("请上传身份证正面照片");
      }
      if (this.identityCardReverse.length == 0) {
        return this.$toast("请上传身份证反面照片");
      }
      if (this.safetyProtocol.length == 0) {
        return this.$toast("请上传施工安全协议照片");
      }
      if (this.safetyCommitment.length == 0) {
        return this.$toast("请上传消防安全承诺照片");
      }
      this.currentStep = "2";
    },
    power() {
      if (!YBS.hasPermission("storage")) {
        let pageParam = {
          title: "存储权限使用说明",
          cont: "用于存储图片、视频等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["storage"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
              if (!YBS.hasPermission("camera")) {
              YBS.reqPermission(["camera"], function (ret) {
                if (ret && ret.list.length > 0 && ret.list[0].granted) {
                }
              });
              return;
              }
            }
          });
        })
        return;
      }
      if (!YBS.hasPermission("camera")) {
        let pageParam = {
          title: "摄像机权限使用说明",
          cont: "用于扫描巡检码、空间码、拍照上传等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["camera"], function (ret) {
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
            }
          });
        })
        return;
      }
    },
    backFn() {
      if (this.currentStep == "2") {
        return (this.currentStep = "1");
      }
      this.$router.go(-1);
    },
    onConfirmBuild(val) {
      console.log(val);
      this.constructionUnitName = val.companyName;
      this.constructionUnitId = val.id;
      this.constructionUnitCode = val.companyCode;
      this.showBuildPicker = false;
    },
    afterImgRead(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.identityCardRight[this.identityCardRight.length - 1].url = data.fileKey;
        }
      });
    },
    afterImgRead2(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.identityCardReverse[this.identityCardReverse.length - 1].url = data.fileKey;
        }
      });
    },
    afterImgRead3(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.safetyProtocol[this.safetyProtocol.length - 1].url = data.fileKey;
        }
      });
    },
    afterImgRead4(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.safetyCommitment[this.safetyCommitment.length - 1].url = data.fileKey;
        }
      });
    },
    afterImgReadCommon(file, arr) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostBase("uploadImg", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          arr[arr.length - 1].url = data.fileKey;
        }
      });
    },
    addPerson() {
      this.staffInfoList.push({
        id: Math.random(),
        constructionStaffName: "",
        gender: "0",
        cellPhoneNumber: "",
        idNumber: "",
        qualificationCertificateArr: [],
      });
    },
    deletePerson(index) {
      if (this.staffInfoList.length == 1) {
        return this.$toast("至少保留一条员工信息");
      }
      this.staffInfoList.splice(index, 1);
    },
    handleSubmit() {
      console.log("执行了提交方法");
      if (!this.constructionUnitName) {
        return this.$toast("请选择建设单位");
      }
      if (!this.creditCode) {
        return this.$toast("请输入信用代码");
      }
      if (!this.corporateName) {
        return this.$toast("请输入法人姓名");
      }
      if (this.identityCardRight.length == 0) {
        return this.$toast("请上传身份证正面照片");
      }
      if (this.identityCardReverse.length == 0) {
        return this.$toast("请上传身份证反面照片");
      }
      if (this.safetyProtocol.length == 0) {
        return this.$toast("请上传施工安全协议照片");
      }
      if (this.safetyCommitment.length == 0) {
        return this.$toast("请上传消防安全承诺照片");
      }
      for (let i = 0; i < this.staffInfoList.length; i++) {
        if (!this.staffInfoList[i].constructionStaffName) {
          return this.$toast("请输入员工姓名");
        }
        if (!this.staffInfoList[i].cellPhoneNumber) {
          return this.$toast("请输入手机号");
        }
        if (!this.staffInfoList[i].idNumber) {
          return this.$toast("请输入身份证号");
        }
        if (this.staffInfoList[i].qualificationCertificateArr.length == 0) {
          return this.$toast("请上传资质证书照片");
        }
      }
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: "提交中...",
      });
      let arr = [];
      this.staffInfoList.forEach((item) => {
        arr.push({
          constructionStaffName: item.constructionStaffName,
          gender: item.gender,
          cellPhoneNumber: item.cellPhoneNumber,
          idNumber: item.idNumber,
          qualificationCertificate: item.qualificationCertificateArr.map((item) => item.url).join(","),
        });
      });
      let params = {
        constructionUnitName: this.constructionUnitName,
        constructionUnitId: this.constructionUnitId,
        constructionUnitCode: this.constructionUnitCode,
        legalPersonName: this.corporateName,
        corporateIdentityCardFront: this.identityCardRight.length > 0 ? this.identityCardRight[0].url : "",
        corporateIdentityCardReversed: this.identityCardReverse.length > 0 ? this.identityCardReverse[0].url : "",
        securityProtocol: this.safetyProtocol.map((item) => item.url).join(","),
        safetyCommitment: this.safetyCommitment.map((item) => item.url).join(","),
        creditCode: this.creditCode,
        constructionStaff: JSON.stringify(arr),
      };
      // return console.log(params);
      this.axios.postContralHostBase("saveConstructionUnit", params, (res) => {
        this.$toast.clear();
        console.log(res);
        if (res.code == 200) {
          this.$toast("提交成功");
          console.log(res);
          this.$router.push({
            path: "/constructionUnitsDetails",
            query: {
              id: res.data,
            },
          });
        }
      });
    },
  },
  mounted() {
    setTimeout(() => {
      this.power();
    }, 1000);
    this.axios.postContralHostBase("getOutSourcedCompanyList", {}, (res) => {
      console.log(res);
      this.buildColumns = res.data;
    });
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding: 0 12px;
  padding-bottom: 60px;
}
.star {
  position: relative;
}
/deep/ .star > div:nth-child(1)::before {
  content: "*";
  color: red;
  position: absolute;
  left: 5px;
  top: 14px;
}
.upload-box {
  width: 60vw;
  height: 150px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f7f8fa;
  i {
    font-size: 38px;
  }
  span {
    width: 100%;
    position: absolute;
    font-size: 14px;
    left: 50%;
    transform: translateX(-50%);
    bottom: 12px;
    text-align: center;
    color: #ccc;
  }
}
/deep/ .identity .van-field__control--custom {
  flex-wrap: wrap;
}
.identity .van-uploader:nth-child(1) {
  margin-bottom: 12px;
}
/deep/ .identity .van-uploader__preview .van-uploader__preview-image {
  width: 60vw;
  height: 150px;
}
.safety .upload-box {
  width: 45vw;
  height: 150px;
}
/deep/ .safety .van-uploader__preview .van-uploader__preview-image {
  width: 45vw;
  height: 150px;
}
.btn-box {
  position: fixed;
  height: 60px;
  width: 100%;
  bottom: 0;
  background-color: #fff;
  display: flex;
  align-items: center;
  .van-button {
    width: 80%;
    margin: 0 auto;
  }
}
.btn-box2 {
  position: fixed;
  height: 60px;
  width: 90%;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  > button {
    width: 46%;
  }
}
.handle-box {
  display: flex;
  justify-content: space-between;
  > div {
    width: 40%;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    > span {
      margin-left: 5px;
    }
  }
  img {
    width: 16px;
    height: 16px;
  }
}
.undertone {
  background-color: rgba(41, 190, 188, 0.15) !important;
  border-color: rgba(41, 190, 188, 0.15) !important;
  color: #29bebc !important;
}
</style>
