<template>
  <div class="container">
    <div v-if="test" class="sign" style="position: fixed; top: 0; z-index: 9999; bottom: 0">
      <signature ref="signRef" @closePop="test = false" @saveImg="complete"></signature>
    </div>
    <Header :title="approvalState == 0 ? '待审批' : '已审批'" @backFun="backFn"></Header>
    <div class="main-box">
      <template v-if="constructionUnitDeclare">
        <div class="title">
          <span>施工单位信息</span>
          <span @click="goFirePrinting">动火许可证</span>
        </div>
        <van-cell title="施工单位名称" :value="constructionUnitDeclare.constructionUnitName" />
        <van-cell title="信用代码" :value="constructionUnitDeclare.creditCode" />
        <van-cell title="法人姓名" :value="constructionUnitDeclare.legalPersonName" />
        <van-cell title="法人身份证">
          <template #right-icon>
            <div class="img-box">
              <img :src="fullUrl(constructionUnitDeclare.corporateIdentityCardFront)" />
              <img :src="fullUrl(constructionUnitDeclare.corporateIdentityCardReversed)" />
            </div>
          </template>
        </van-cell>
        <van-cell title="施工安全协议">
          <template #right-icon>
            <div class="img-box" v-if="constructionUnitDeclare.securityProtocol">
              <img v-for="(item, index) in constructionUnitDeclare.securityProtocol.split(',')" :key="index" :src="fullUrl(item)" />
            </div>
          </template>
        </van-cell>
        <van-cell title="消防安全承诺">
          <template #right-icon>
            <div class="img-box" v-if="constructionUnitDeclare.safetyCommitment">
              <img v-for="(item, index) in constructionUnitDeclare.safetyCommitment.split(',')" :key="index" :src="fullUrl(item)" />
            </div>
          </template>
        </van-cell>
      </template>
      <div class="title">
        <span>工程信息</span>
      </div>
      <van-cell title="工程类型" :value="detailData.projectType == 1 ? '改建' : detailData.projectType == 2 ? '新建' : '维修'" />
      <van-cell title="施工许可" :value="detailData.constructionPermitName" />
      <div class="img-box">
        <img :src="fullUrl(detailData.constructionPermitUrl)" />
      </div>
      <van-cell title="建设单位" :value="detailData.constructionUnit" />
      <van-cell title="信用代码" :value="detailData.creditCode" />
      <van-cell title="行业主管部门" :value="detailData.industryAuthoritiesName" />
      <van-cell title="工程名称" :value="detailData.projectName" />
      <van-cell title="施工单位" :value="detailData.constructionUnitName" />
      <van-cell title="工程属地" :value="detailData.projectTerritory" />
      <van-cell title="建设地址" :value="detailData.constructionAddress" />
      <van-cell title="建设规模" :value="detailData.constructionScale" />
      <van-cell title="许可发证机关" :value="detailData.licensingAuthorityName" />
      <div class="title" style="margin: 8px 0">动火人信息</div>
      <van-cell title="动火部位" :value="detailData.hotSpot" />
      <van-cell title="动火人姓名" :value="detailData.hotPersonName" />
      <van-cell title="电话" :value="detailData.hotPhone" />
      <van-cell title="动火开始时间" :value="detailData.hotStartTime" />
      <van-cell title="动火结束时间" :value="detailData.hotEndTime" />
      <div class="title" style="margin: 8px 0">动火证件信息</div>
      <van-cell title="动火方式" :value="detailData.hotModeName" />
      <van-cell title="特种作业证">
        <template #right-icon>
          <div class="img-box">
            <img :src="fullUrl(detailData.specialWorkCertificateUrl)" />
          </div>
        </template>
      </van-cell>
      <van-cell title="证号" :value="detailData.certificateNumber" />
      <van-cell title="姓名" :value="detailData.certificateName" />
      <van-cell title="性别" :value="detailData.certificateSex" />
      <van-cell title="作业类别" :value="detailData.jobClass" />
      <van-cell title="准操项目" :value="detailData.standardOperationItems" />
      <van-cell title="人证合照">
        <template #right-icon>
          <div class="img-box img-box-style">
            <img v-for="(item, index) in detailData.personPhoto.split(',')" :key="index" :src="fullUrl(item)" />
          </div>
        </template>
      </van-cell>
      <div class="title" style="margin: 8px 0">监护人信息</div>
      <van-cell title="姓名" :value="detailData.tutelageName" />
      <van-cell title="电话" :value="detailData.tutelagePhone" />
      <van-cell title="身份证号" :value="detailData.tutelageIdentityCard" />
      <div class="title" style="margin: 8px 0">现场拍照</div>
      <van-cell title="现场照片">
        <template #right-icon>
          <div class="img-box img-box-style">
            <img v-for="(item, index) in detailData.scenePhoto.split(',')" :key="index" :src="fullUrl(item)" />
          </div>
        </template>
      </van-cell>
      <div v-if="approvalState == -1" class="title" style="margin: 8px 0">审批流</div>
      <van-cell v-if="approvalState == -1" title="审批流名称" :value="detailData.approveName" />
      <div v-if="recordList.length" class="title" style="margin: 8px 0">审批记录</div>
      <van-steps direction="vertical" :active="0" active-color="#29BEBC">
        <van-step v-for="item in recordList" :key="item.id">
          <div class="record-item">
            <span>审批</span>
            <span>{{ item.operatorTime }}</span>
          </div>
          <div class="record-item">
            <span>审核结果</span>
            <span>{{ item.approvalStatus == "0" ? "同意" : "驳回" }}</span>
          </div>
          <div class="record-item">
            <span style="min-width: 88px">审核意见</span>
            <span>{{ item.opinions }}</span>
          </div>
          <div class="record-item">
            <span>审核人</span>
            <span>{{ item.operatorName }}</span>
          </div>
          <div class="record-item">
            <span v-if="item.approvalNode == 7">主管领导</span>
            <span v-if="item.approvalNode == 6">监管部门处长</span>
            <span v-if="item.approvalNode == 5">监管部门负责人</span>
            <span v-if="item.approvalNode == 4">主管部门处长</span>
            <span v-if="item.approvalNode == 3">主管部门负责人</span>
            <span v-if="item.approvalNode == 2">监理单位</span>
            <span v-if="item.approvalNode == 1">施工单位</span>
            <span>{{ item.operatingDepartmentName }}</span>
          </div>
          <div class="record-item">
            <span>签名</span>
            <img v-if="item.approvalSignature" :src="item.approvalSignature" />
            <span v-else>暂无</span>
          </div>
          <div class="record-item">
            <span>印章（图片）</span>
            <img v-if="item.companySeal" :src="item.companySeal" />
            <span v-else>暂无</span>
          </div>
        </van-step>
      </van-steps>
      <template v-if="approvalState == 0">
        <div v-if="detailData.hotState != 2" class="title" style="margin: 8px 0">动火开始审批信息</div>
        <div v-else class="title" style="margin: 8px 0">动火结束审批信息</div>
        <van-field name="radio" label="审核结果" v-if="detailData.approvalState == 0 || detailData.hotState == 2" class="star">
          <template #input>
            <van-radio-group v-model="approvalAuthorityResult" direction="horizontal">
              <van-radio name="0" checked-color="#29BEBC">通过</van-radio>
              <van-radio name="1" checked-color="#29BEBC">驳回</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          v-if="detailData.approvalState == 0 || detailData.hotState == 2"
          v-model="approvalAuthorityOpinion"
          rows="2"
          autosize
          label="审核意见"
          type="textarea"
          maxlength="120"
          placeholder="请输入审核意见，120字以内"
          show-word-limit
        />
        <div class="my_flex" v-if="detailData.approvalState == 0 || detailData.hotState == 2">
          <div class="sign_title">签名</div>
          <div v-if="imgData" class="resign">
            <img class="sign-img" :src="imgData" />
            <span @click="showPopup" class="resign-txt" v-if="imgData != ' '">重新签名</span>
          </div>
          <div v-else class="signature_edit" @click="showPopup">
            <div>
              <span class="no_singature">签名</span>
            </div>
          </div>
        </div>
        <van-cell title="印章（图片）">
          <template #right-icon>
            <div class="img-box">
              <img v-if="detailData.approvalSealUrl" :src="fullUrl(detailData.approvalSealUrl)" />
              <span v-else>暂无</span>
            </div>
          </template>
        </van-cell>
      </template>
      <template v-if="approvalState == -1 && (detailData.hotState == 3 || detailData.hotState == 2)">
        <div class="title" style="margin: 8px 0">动火结束信息</div>
        <van-cell title="上报时间" :value="detailData.reportTime" />
        <van-cell title="上报人" :value="detailData.reportPersonName" />

        <div class="end-info">
          <span>现场照片和视频</span>
          <div class="media" v-if="detailData.finishImageUrl || detailData.finishVideoUrl">
            <img :src="item" v-for="(item, index) in detailData.finishImageUrl.split(',')" :key="index" />
            <video v-if="detailData.finishVideoUrl" :src="detailData.finishVideoUrl" controls="controls"></video>
          </div>
        </div>
        <div class="record-box">
          <div class="record-item">
            <span>审批</span>
            <span>{{ lastData.operatorTime }}</span>
          </div>
          <div class="record-item">
            <span>审核结果</span>
            <span>{{ lastData.approvalStatus == "0" ? "同意" : "驳回" }}</span>
          </div>
          <div class="record-item">
            <span>审核意见</span>
            <span>{{ lastData.opinions }}</span>
          </div>
          <div class="record-item">
            <span>审核人</span>
            <span>{{ lastData.operatorName }}</span>
          </div>
          <div class="record-item">
            <span>批准部门</span>
            <span>{{ lastData.operatingDepartmentName }}</span>
          </div>
          <div class="record-item">
            <span>签名</span>
            <img :src="lastData.approvalSignature" />
          </div>
          <div class="record-item">
            <span>印章（图片）</span>
            <img v-if="lastData.companySeal" :src="lastData.companySeal" />
            <span v-else>暂无</span>
          </div>
        </div>
      </template>
      <!-- <div v-if="detailData.approvalState == 1 && approvalState == 0" class="title" style="margin:8px 0;">主管部门审批信息</div>
      <template v-if="detailData.approvalState == 1 && approvalState == 0">
        <van-cell title="审批人" :value="detailData.approvalAuthorityUserName" />
        <van-cell title="审批时间" :value="detailData.approvalAuthorityTime" />
        <van-cell title="审批结果" :value="detailData.approvalAuthorityResult" />
        <van-cell title="审批意见" :value="detailData.approvalAuthorityOpinion" />
        <van-cell title="签名">
          <template #right-icon>
            <div class="img-box">
              <img :src="fullUrl(detailData.approvalAuthoritySignature)" />
            </div>
          </template>
        </van-cell>
        <van-button block type="info" color="#29BEBC" @click="goApprovePage">审批</van-button>
      </template> -->
      <van-cell title="监理单位检查" v-if="detailData.approvalNode == 2 && approvalState != -1 && !isShowCheckBtn">
        <template #right-icon>
          <van-button class="check-btn" size="small" block type="info" color="#29BEBC" @click="goApprovePage3">去检查</van-button>
        </template>
      </van-cell>
      <van-cell title="主管部门检查" v-if="detailData.approvalNode == 3 && approvalState != -1 && !isShowCheckBtn">
        <template #right-icon>
          <van-button class="check-btn" size="small" block type="info" color="#29BEBC" @click="goApprovePage">去检查</van-button>
        </template>
      </van-cell>
      <van-cell title="保卫处检查" v-if="detailData.approvalNode == 5 && approvalState != -1 && !isShowCheckBtn">
        <template #right-icon>
          <van-button class="check-btn" size="small" block type="info" color="#29BEBC" @click="goApprovePage2">去检查</van-button>
        </template>
      </van-cell>
      <van-button v-if="(detailData.approvalState == 0 || detailData.hotState == 2) && approvalState == 0" block type="info" color="#29BEBC" @click="handleSubmit">提交</van-button>
    </div>
  </div>
</template>

<script>
import signature from "@/view/jobManagement/components/signature.vue";
import YBS from "@/centralControl/utils/utils.js";
export default {
  name: "hotApproveDetail",
  components: {
    signature,
  },
  data() {
    return {
      id: "",
      approvalState: "",
      detailData: "",
      approvalAuthorityResult: "",
      approvalAuthorityOpinion: "",
      test: false,
      imgData: "",
      sign: "base64",
      recordList: [],
      lastData: "",
      constructionUnitDeclare: {},
    };
  },
  beforeRouteLeave(to, from, next) {
    console.log("to", to);
    console.log("from", from);
    // 去这些界面我需要缓存
    const routerArr = ["/hotApprove"];
    if (routerArr.includes(to.path)) {
      // 不在缓存列表中，从cachedViews缓存列表中移除
      this.$store.commit("cachedViews/DEL_CACHED_VIEW", from.name);
      this.$store.commit("cachedViews/DEL_CACHED_VIEW", "hotApproveForm");
      this.$store.commit("cachedViews/DEL_CACHED_VIEW", "hotApproveForm2");
      this.$store.commit("cachedViews/DEL_CACHED_VIEW", "hotApproveForm3");
    }
    next();
  },
  methods: {
    power() {
      if (!YBS.hasPermission("storage")) {
        let pageParam = {
          title: "存储权限使用说明",
          cont: "用于存储图片、视频等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["storage"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
              if (!YBS.hasPermission("camera")) {
              YBS.reqPermission(["camera"], function (ret) {
                if (ret && ret.list.length > 0 && ret.list[0].granted) {
                }
              });
              return;
              }
            }
          });
        })
        return;
      }
      if (!YBS.hasPermission("camera")) {
        let pageParam = {
          title: "摄像机权限使用说明",
          cont: "用于扫描巡检码、空间码、拍照上传等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["camera"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
            }
          });
        })
        return;
      }
    },
    backFn() {
      this.$router.go(-1);
    },
    getDetail() {
      // 显示加载loading
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        overlay: true,
        message: "加载中...",
      });
      this.axios.postContralHostBase("getFireApplication", { id: this.id }, (res) => {
        console.log(res);
        if (res.code == 200) {
          this.detailData = res.data;
          this.constructionUnitDeclare = res.data.constructionUnitDeclare;
          this.getListByBusinessId();
        }
      });
    },
    fullUrl(url) {
      return url;
    },
    showPopup() {
      this.test = !this.test;
    },
    complete(data) {
      if (!data) return this.$message.error("保存签名失败");
      this.test = false;
      this.imgData = data;
      this.sign = "base64";
      // this.saveApi({ signature: data });
    },
    handleSubmit() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        message: "提交中...",
      });
      // 校验审核结果
      if (this.approvalAuthorityResult == "") {
        return this.$toast("请选择审核结果！");
      }
      // 校验approvalItemsJson,给提示去检查项必填
      if (
        !this.$route.query.approvalItemsJson &&
        ((this.detailData.approvalNode == 5 && this.approvalState != -1) ||
          (this.detailData.approvalNode == 3 && this.approvalState != -1) ||
          (this.detailData.approvalNode == 2 && this.approvalState != -1)) &&
        !this.isShowCheckBtn
      ) {
        return this.$toast("请先填写检查项！");
      }
      let params = {
        approvalStatus: this.approvalAuthorityResult,
        approvalOpinions: this.approvalAuthorityOpinion,
        approvalStatusName: this.approvalAuthorityResult == 0 ? "通过" : "驳回",
        approvalSignature: this.imgData,
        approveId: this.detailData.approveId,
        approvalNode: this.detailData.approvalNode,
      };
      if (this.$route.query.approvalItemsJson) {
        params.approvalItemsJson = this.$route.query.approvalItemsJson;
      }
      this.axios.postContralHostBase("approveFireApplication", { id: this.id, ...params }, (res) => {
        console.log(res);
        this.$toast.clear();
        if (res.code == 200) {
          // this.$toast.success("审批成功");
          this.$toast.loading({
            duration: 1500,
            forbidClick: true,
            message: "审批成功",
          });
        } else {
          this.$toast.fail(res.message);
        }
        // 跳转到hotApprove
        setTimeout(() => {
          this.$router.push({
            path: "/hotApprove",
          });
        }, 1500);
      });
    },
    goApprovePage() {
      this.$router.push({
        path: "/hotApproveForm",
        query: {
          id: this.id,
          approvalState: this.approvalState,
          detailData: JSON.stringify(this.detailData),
        },
      });
    },
    goApprovePage2() {
      this.$router.push({
        path: "/hotApproveForm2",
        query: {
          id: this.id,
          approvalState: this.approvalState,
          detailData: JSON.stringify(this.detailData),
        },
      });
    },
    goApprovePage3() {
      this.$router.push({
        path: "/hotApproveForm3",
        query: {
          id: this.id,
          approvalState: this.approvalState,
          detailData: JSON.stringify(this.detailData),
        },
      });
    },
    getListByBusinessId() {
      this.axios.postContralHostBase("getListByBusinessId", { businessId: this.id, operationCode: "1", approvalVersion: this.detailData.approvalVersion }, (res) => {
        console.log(res);
        this.$toast.clear();
        if (res.code == 200) {
          this.recordList = res.data;
          if (this.approvalState == -1 && (this.detailData.hotState == 3 || this.detailData.hotState == 2)) {
            // lastData取res.data里approvalNode为8的数据
            this.lastData = this.recordList.find((item) => item.approvalNode == 8);
            // 删除recordList里approvalNode为8的数据
            this.recordList = this.recordList.filter((item) => item.approvalNode != 8);
          }
        }
      });
    },
    goFirePrinting() {
      // 如果是待审批状态，不允许跳转
      if (this.detailData.approvalState != 1) {
        return this.$toast.fail("当前状态不可查看");
      }
      this.$router.push({
        path: "/firePrinting",
        query: {
          id: this.id,
        },
      });
    },
  },
  created() {
    setTimeout(() => {
      api.addEventListener(
        {
          name: "keyback666",
        },
        (ret, err) => {
          this.backFn();
        }
      );
    }, 100);
  },
  mounted() {
    setTimeout(() => {
      this.power();
    }, 1000);
    // this.getDetail();
    // if (this.approvalState == -1) {
    //   this.getListByBusinessId();
    // }
  },
  computed: {
    isShowCheckBtn() {
      // 遍历recordList,如果里面的approvalNode为3或者5，决定是否展示去检查按钮
      return this.recordList.some(
        (item) =>
          (item.approvalNode == 3 && this.detailData.approvalNode == 3) ||
          (item.approvalNode == 5 && this.detailData.approvalNode == 5) ||
          (item.approvalNode == 2 && this.detailData.approvalNode == 2)
      );
    },
  },
  activated() {
    if (this.$route.query.approvalItemsJson) {
      // 跳转到页面最底部
      window.scrollTo(0, document.body.scrollHeight);
    }
    this.id = this.$route.query.id;
    this.approvalState = this.$route.query.approvalState;
    this.getDetail();
  },
  // 判断如果从/hotApprove路由来的，不缓存数据，重新请求接口
  // beforeRouteEnter(to, from, next) {
  //   if (from.path == "/hotApprove") {
  //     // 清空data中的缓存
  //     next((vm) => {
  //       vm.approvalAuthorityResult = "";
  //       vm.approvalAuthorityOpinion = "";
  //       vm.imgData = "";
  //       vm.recordList = [];
  //       setTimeout(() => {
  //         vm.$refs.signRef.clear();
  //       }, 100);
  //     });
  //   } else {
  //     next();
  //   }
  // },
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 18px;
  border-left: 3px solid #29bebc;
  padding-left: 12px;
  margin: 8px 0;
}
.header {
  margin-bottom: 12px;
}
.main-box {
  padding: 0 16px;
}
.img-box {
  text-align: right;
  margin: 12px 0;
}
.img-box img {
  width: 100px;
}
.my_flex {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 10px;
  justify-content: space-between;
  margin-top: 0.1875rem;
  font-size: 16px;
  padding-left: 16px;
  .sign_title {
    flex: 1;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(53, 53, 53, 1);
  }
}
.signature_edit {
  width: 252px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #00cac8;

  .no_singature {
    display: inline-block;
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #00cac8;
    line-height: 22px;
  }

  img {
    width: 16px;
    margin-right: 4px;
  }
}
.resign-txt {
  color: #00cac8;
  font-style: normal;
  margin: auto 0;
  text-align: center;
}
.resign {
  flex: 3;
  text-align: center;
  display: flex;
}
.sign-img {
  // width: 60px;
  height: 60px;
  margin: auto;
  // transform: rotate(90deg);
}
.end-info {
  font-size: 16px;
}
.end-info > span:nth-child(1) {
  margin-left: 16px;
}
.media {
  margin-top: 12px;
}
.media img {
  width: 200px;
}
.media video {
  width: 200px;
}
.van-steps {
  h3 {
    font-size: 14px;
    margin-top: 0;
  }
  p {
    font-size: 12px;
  }
}
.main-box > div:nth-child(1) {
  display: flex;
  justify-content: space-between;
}
.main-box > div:nth-child(1) > span:nth-child(2) {
  color: #00cac8;
  font-size: 14px;
}
.check-btn {
  width: 100px;
}
.record-item {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}
.record-item > span:nth-child(1) {
  display: inline-block;
  width: 88px;
}
.record-item img {
  width: 100px;
  height: 100px;
}
.record-box {
  font-size: 14px;
  padding-left: 16px;
  margin-top: 12px;
}
.star {
  position: relative;
}
/deep/ .star > div:nth-child(1)::before {
  content: "*";
  color: red;
  position: absolute;
  left: 5px;
  top: 14px;
}
.img-box-style {
  max-width: 75%;
  img {
    margin-right: 5px;
  }
}
</style>
