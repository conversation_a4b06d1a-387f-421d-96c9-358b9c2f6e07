<template>
  <div class="steps">
    <div class="finished" v-for="item in data" :key="item.id" @click="goNext(item.taskId,item.id,item.locationPointReleaseList,item.engineerCode,item.carryOutFlag,item.roomCode,item.executeOrder,item.maintainProjectRelease,item.taskPointId,item.sourceId,item.locationPointReleaseList)">
      <!-- <div class="status">
        <span :class="item.carryOutFlag=='0'?'unfinished':'complete'"></span>
        <div class="unfinished-location van-ellipsis">{{item.taskPointName}}
        </div>
      </div>
      <div class="results">
        <div v-if="item.carryOutFlag != '0'" class="results-name">
          <span>巡检结果:{{ resultState(item.state) }}</span>
          <span style="display: inline-block;">{{ item.executeTime }}</span>
        </div>
      </div> -->
      <div class="status">
        <span :class="item.carryOutFlag=='0'?'red-color':''">{{item.carryOutFlag=='0'?'未巡':'已巡'}}</span>
        <div :class="['clock-icon',item.carryOutFlag=='0'?'clock-icon-red':'']">
          <img v-if="item.carryOutFlag=='1'" src="../../assets/images/clockGreen.png" />
          <img v-if="item.carryOutFlag=='0'" src="../../assets/images/clockRed.png" />
        </div>
        <div v-if="item.carryOutFlag=='1'" :class="['info',item.state=='2'?'pass-info':'']">
          <span :class="['red-color',item.state=='2'?'pass':'']">{{item.state | resultState}}</span>
          <span style="color:#B2B2B2">{{item.executeTime}}</span>
          <div class="location van-ellipsis">{{item.taskPointName}}</div>
        </div>
        <div v-if="item.carryOutFlag=='0'" class="unfinished-location van-ellipsis">{{item.taskPointName}}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props:['data'],
  methods:{
    goNext(taskId,id,ibeacon,engineerCode,carryOutFlag,roomCode,executeOrder,maintainProjectRelease,taskPointId,sourceId,location) {
      this.$parent.goDetail(taskId,id,ibeacon,engineerCode,carryOutFlag,roomCode,executeOrder,maintainProjectRelease,taskPointId,sourceId,location)
    }
  },
  filters:{
    resultState(val) {
      if(val=='2') return '合格'
      if(val=='3') return '不合格'
      if(val=='4') return '异常'
    }
  }
};
</script>

<style scoped>
.steps {
  padding-top: 22px;
  font-size: 16px;
  padding-left: 16px;
}
.steps > div {
  margin-bottom: 50px;
}
.finished {
  height: auto;
}
.finished .status {
  display: flex;
  align-items: center;
}
.finished .info {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f5f6fb;
  border-radius: 15px;
  font-size: 14px;
  height: 30px;
  padding: 0 12px 0 22px;
}
.finished .info::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 10px;
  width: 6px;
  height: 6px;
  background-color: #ff2836;
  border-radius: 50%;
  transform: translateY(-50%);
}
.finished .pass-info::before {
  background-color: #29bebc;
}
.info span:nth-child(1) {
  margin-right: 12px;
}
.red-color {
  color: #ff2836;
}
.pass {
  color: #333330;
}
.steps img {
  width: 18px;
  height: 18px;
  margin: 0 8px;
}
.steps>div:last-child .clock-icon::before{
    display: none;
}
.location {
  position: absolute;
  left: 10px;
  top: 40px;
  width:100%;
}
.finished .clock-icon {
  position: relative;
}
.finished .clock-icon::before {
  position: absolute;
  top: 120%;
  left: 50%;
  content: "";
  height: 45px;
  width: 1px;
  background-color: #dddddd;
  transform: translateX(-50%);
}
.finished .clock-icon-red::before {
    height: 24px;
}
.unfinished-location {
    color: #B2B2B2;
    font-size: 15px;
    padding-left: 10px;
    width: 75%;
}
</style>