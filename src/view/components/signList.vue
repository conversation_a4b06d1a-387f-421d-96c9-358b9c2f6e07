<template>
    <div class="list">
        <div class="theader">
            <span>序号</span>
            <span>部门</span>
            <span>人员</span>
            <span>次数</span>
        </div>
        <div class="items" v-for="(item,index) in listData" :key="item.distribution_team_id">
            <span>{{index+1}}</span>
            <span>{{item.distribution_team_name}}</span>
            <span>{{item.create_by_name}}</span>
            <span @click="goUserSignDetail(item)">{{item.number}}</span>
        </div>
    </div>
</template>

<script>
export default {
    props:['listData'],
    data() {
        return {}
    },
    methods:{
        goUserSignDetail(item) {
            this.$router.push({
                path:'userSignDetail',
                query:{
                    details:item
                }
            })
        }
    }
}
</script>

<style scoped>
.list .theader {
    display: flex;
    background-color: #E8F0FF;
    height: 40px;
    padding: 0 16px;
}
.list .theader span {
    color: #2D4A74;
    font-size: 14px;
    font-weight: 500;
    line-height: 40px;
}
.list >div span:nth-child(1) {
    width: 20%;
}
.list >div span:nth-child(2) {
    width: 30%;
}
.list >div span:nth-child(3) {
    width: 30%;
}
.list >div span:nth-child(4) {
    width: 20%;
}
.list .items {
    background-color: #fff;
    border-bottom: 1px solid #F5F6FB;
    height: 48px;
    display: flex;
    padding: 0 16px;
}
.list .items span {
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 48px;
}
.list .items span:nth-child(4) {
    color: #29BEBC;
}
</style>