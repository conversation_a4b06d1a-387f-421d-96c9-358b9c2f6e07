<template>
  <div class="list">
    <div class="theader">
      <span>序号</span>
      <span>{{ type == "1" ? "部门" : "任务点" }}</span>
      <span>次数</span>
    </div>
    <template v-if="type == '1'">
      <div
        class="items"
        v-for="(item, index) in listData"
        :key="item.distribution_team_id"
      >
        <span>{{ index + 1 }}</span>
        <span class="van-ellipsis">{{ item.distribution_team_name }}</span>
        <span @click="goSignDetail(item.distribution_team_id)">{{ item.number }}</span>
      </div>
    </template>
    <template v-else>
      <div
        class="items"
        v-for="(item, index) in listData"
        :key="item.room_name"
      >
        <span>{{ index + 1 }}</span>
        <span class="van-ellipsis">{{item.taskPointName}}</span>
        <span>{{ item.number }}</span>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: ["listData", "type"],
  data() {
    return {};
  },
  methods: {
    goSignDetail(val) {
      this.$router.push({
        path: "signRecordDetail",
        query:{
            id:val
        }
      });
    },
  },
};
</script>

<style scoped>
.list .theader {
  display: flex;
  background-color: #e8f0ff;
  height: 40px;
  padding: 0 16px;
}
.list .theader span {
  color: #2d4a74;
  font-size: 14px;
  font-weight: 500;
  line-height: 40px;
}
.list > div span:nth-child(1) {
  width: 20%;
}
.list > div span:nth-child(2) {
  width: 55%;
}
.list > div span:nth-child(3) {
  width: 25%;
}
.list .items {
  background-color: #fff;
  border-bottom: 1px solid #f5f6fb;
  height: 48px;
  display: flex;
  padding: 0 16px;
}
.list .items span {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 48px;
}
.list .items span:nth-child(3) {
  color: #29bebc;
}
</style>