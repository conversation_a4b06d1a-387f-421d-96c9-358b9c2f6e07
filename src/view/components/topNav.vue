<template>
    <div class="top-nav">
      <van-icon
        name="arrow-left"
        size="20"
        class="arrow-icon"
        @click="goBack"
      />
      <span>{{ title }}</span>
    </div>
</template>

<script>
export default {
  props: ["title"],
  methods: {
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style scoped>
.top-nav {
  height: 64px;
  background-color: #29bebc;
  text-align: center;
  color: #fff;
  position: relative;
}
.top-nav span {
  font-size: 18px;
  line-height: 80px;
}
.arrow-icon {
  position: absolute;
  top: 50%;
  left: 16px;
  transform: translateY(-4px);
}
</style>