<template>
  <div class="container" v-loading="loading">
    <Header title="巡检任务分析" @backFun="goBack"> </Header>
    <div class="nav">
      <div class="title">巡检任务总览</div>
      <div class="btns">
        <div
          :class="['btn', dateType == 'day' ? 'active-btn' : '']"
          @click="changeBtn('day')"
        >
          今日
        </div>
        <div
          :class="['btn', dateType == 'month' ? 'active-btn' : '']"
          @click="changeBtn('month')"
        >
          本月
        </div>
        <div
          :class="['btn', dateType == 'year' ? 'active-btn' : '']"
          @click="changeBtn('year')"
        >
          本年
        </div>
        <div
          :class="['btn', dateType == 'custom' ? 'active-btn' : '']"
          @click="changeBtn('custom')"
        >
          自定义
        </div>
      </div>
    </div>
    <van-empty v-if="this.echartData.length == 0" description="暂无数据" />
    <div v-else id="myChart" style="width: 100vw; height: 30vh"></div>
    <van-empty v-if="this.echartData2.length == 0" description="暂无数据" />
    <div v-else id="myChart2" style="width: 100vw; height: 30vh"></div>
    <div style="padding: 0 8px">
      <el-table
        :data="tableData"
        style="width: 100%">
        <el-table-column
          align="center"
          type="index"
          label="序号"
          width="50">
        </el-table-column>
        <el-table-column
          align="center"
          show-overflow-tooltip
          prop="plan_name"
          label="计划名称">
        </el-table-column>
        <el-table-column
          align="center"
          prop="taskSum"
          label="任务总数"
          width="80">
        </el-table-column>
        <el-table-column
          align="center"
          prop="finishNumber"
          label="已巡"
          width="50">
        </el-table-column>
        <el-table-column
          align="center"
          prop="percentage"
          label="完成率"
          width="80">
        </el-table-column>
      </el-table>
    </div>
    <van-popup v-model="show" title="请选择时间区间" closeable round @close="onClose">
      <div style="width: 70vw;height:30vh">
        <div class="time-text">
          <div class="date-title">
            <span>请选择时间区间</span>
          </div>
          <div class="items">
            <span>开始时间：</span>
            <van-field
              class="date-picker"
              placeholder="点击选择时间"
              type="date"
              v-model="startTime"
              :value="startTime"
              center
              :clearable="false"
              is-link
              use-button-slot
            />
          </div>
          <div class="items">
            <span>结束时间：</span>
            <van-field
              class="date-picker"
              placeholder="点击选择时间"
              type="date"
              v-model="endTime"
              :value="startTime"
              center
              :clearable="false"
              is-link
              use-button-slot
            />
          </div>
          <van-button style="height:30px" type="info" @click="getData">查询</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
import taskList from "./list.vue";
import Table from './table.vue';
export default {
  components: {
    topNav,
    taskList,
    Table
  },
  data() {
    return {
      loading: false,
      show: false,
      echartData: [],
      echartData2: [],
      listData: [],
      dateType: "day",
      tableData: [],
      startTime: '',
      endTime: '',
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
    };
  },
  created() {
    this.loading = true
    this.getTableData();
    this.getChart1Data();
    this.getChart2Data();
    apiready = () => {
      this.sysClickBack()
      // if (!localStorage.getItem("loginInfo")) {
      var userInfo = api.getPrefs({
        sync: true,
        key: "userInfo"
      });
      userInfo = JSON.parse(userInfo);
      userInfo.hospitalCode = api.pageParam.hospitalCode
      userInfo.hospitalName = api.pageParam.hospitalName
      if (userInfo.id) {
        const virtualToken = encodeURIComponent(userInfo.hospitalName);
        localStorage.setItem('token', virtualToken);
        localStorage.setItem("loginInfo", JSON.stringify(userInfo));
      }
      // }
      this.getTableData();
      this.getChart1Data();
      this.getChart2Data();
    }
    this.sysClickBack()
  },
  mounted() {},
  methods: {
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    initChart() {
      let myChart = this.$echarts.init(document.getElementById("myChart"));
      myChart.setOption({
        backgroundColor: "#fff",
        color: ["rgba(91, 143, 249, 0.85)", "rgba(93, 112, 146, 0.85)"],
        series: [
          {
            type: "pie",
            // radius: ["40%", "60%"],
            radius: "60%",
            center: ["50%", "50%"],
            data: this.echartData,
            hoverAnimation: false,
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 1,
              labelLine: {
                length: 20,
                length2: 16,
                show: true,
                lineStyle: {
                  color: "#ccc",
                },
              },
            },
            label: {
              position: "outside",
              formatter: (param) => {
                return `{a|${param.percent}%}\n{b|${param.data.name}${param.value}次}`;
              },
              backgroundColor: "auto", //圆点颜色，auto：映射的系列色
              // height,width,lineHeight必须为0
              height: 0,
              width: 0,
              lineHeight: 18,
              // radius和padding为圆点大小，圆点半径为几radius和padding各项数值就为几如：圆点半径为1
              // borderRadius: 4,
              // padding: [4, -4, 4, -4],
              borderRadius: 4,
              padding: [4, -4, 4, -4],
              rich: {
                a: {
                  fontSize: 14,
                  padding: [18, 8, 0, 10],
                },
                b: {
                  padding: [18, 8, 0, 10],
                  color: "#595959",
                },
              },
            },
          },
        ],
      });
    },
    initChart2() {
      let myChart = this.$echarts.init(document.getElementById("myChart2"));
      myChart.setOption({
        backgroundColor: "#fff",
        color: [
          "rgba(246, 189, 22, 0.85)",
          "rgba(90, 216, 166, 0.85)",
          "rgba(91, 143, 249, 0.85)",
          "rgba(93, 112, 146, 0.85)",
          "#e3584e",
        ],
        series: [
          {
            type: "pie",
            // radius: ["40%", "60%"],
            radius: "60%",
            center: ["50%", "50%"],
            data: this.echartData2,
            hoverAnimation: false,
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 1,
              labelLine: {
                length: 20,
                length2: 16,
                show: true,
                lineStyle: {
                  color: "#ccc",
                },
              },
            },
            label: {
              position: "outside",
              formatter: "{b}",
              color: "#595959",
            },
          },
        ],
      });
    },
    getTableData(custom) {
      let params = {
        dateType: custom ? '' : this.dateType,
        startTime: custom ? this.startTime + ' 00:00:00' : '',
        endTime: custom ? this.endTime + ' 23:59:59' : ''
      };
      this.axios.postContralHostBase("getAnalysisList", params, (res) => {
        if (res.code == "200") {
          this.tableData = res.data.map((i ,index) => {
            i.index = index + 1
            i.percentage = i.percentage + '%'
            return i
          })
          this.loading = false
        }
      });
    },
    getChart1Data(custom) {
      let params = {
        dateType: custom ? '' : this.dateType,
        startTime: custom ? this.startTime + ' 00:00:00' : '',
        endTime: custom ? this.endTime + ' 23:59:59' : ''
      };
      this.axios.postContralHostBase("getAnalysisChart1", params, (res) => {
        if (res.code == "200") {
          this.echartData = res.data;
          this.loading = false
          this.$nextTick(() => {
            this.initChart()
          })
        }
      });
    },
    getChart2Data(custom) {
      let params = {
        dateType: custom ? '' : this.dateType,
        startTime: custom ? this.startTime + ' 00:00:00' : '',
        endTime: custom ? this.endTime + ' 23:59:59' : ''
      };
      this.axios.postContralHostBase("getAnalysisChart2", params, (res) => {
        if (res.code == "200") {
          res.data.forEach(item => {
            if(item.name.length>8) {
              item.name = item.name.substr(0,8) + '...'
            }
          })
          this.echartData2 = res.data;
          this.loading = false
          this.$nextTick(() => {
            this.initChart2()
          })
        }
      });
    },
    changeBtn(val) {
      this.dateType = val;
      if (val != 'custom') {
        this.getTableData(this.show);
        this.getChart1Data(this.show);
        this.getChart2Data(this.show);
      } else {
        this.show = true
      }
    },
    getData() {
      this.getTableData(this.show);
      this.getChart1Data(this.show);
      this.getChart2Data(this.show);
      this.show = false
    },
    onClose () {
      this.show = false
      this.startTime = ''
      this.endTime = ''
    },
    goBack () {
      api.closeFrame({});
    }
  }
};
</script>

<style scoped>
.container {
  width: 100vw;
  background-color: #fff;
  padding-bottom: 5vh;
}
.nav {
  /* display: flex; */
  align-items: center;
  /* justify-content: space-between; */
  /* height: 60px; */
  background-color: #fff;
  /* padding: 0 16px; */
}
.nav .title {
  padding: 0 16px;
  font-size: 16px;
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid #CCC4C4;
  color: #353535;
  font-weight: 600;
}
.btns {
  padding: 0 16px;
  margin: 10px 0 0 0;
  display: flex;
  width: 52%;
  justify-content: space-between;
}
.btns .btn {
  width: 50px;
  height: 26px;
  border: 1px solid #797979;
  color: #333333;
  font-size: 13px;
  text-align: center;
  line-height: 26px;
}
.btn:nth-child(2) {
  border-left: none;
  border-right: none;
}
.btn:nth-child(3) {
  border-right: none;
}
.active-btn {
  background-color: #29bebc;
  color: #fff !important;
}
.date-title {
  padding: 10px 0;
  text-align: center;
  font-size: 16px;
  font-weight: 600px;
}
.time-text {
  font-size: 13px;
  margin: 0 auto;
  text-align: center;
}
>>>.van-cell {
  width: auto;
}
.items {
  padding: 5px 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.date-picker {
  padding: 10px 8px;
  min-height: 24px;
  width: 60%;
}
</style>