<template>
  <div class="list">
    <div class="theader">
      <span>序号</span>
      <span>{{ type == 1 ? "任务名称" : "部门" }}</span>
      <span>任务总数</span>
      <span>已巡</span>
      <span>完成率</span>
    </div>
    <template v-if="type == 1">
      <div class="items" v-for="(item, index) in listData" :key="'list1-'+index">
        <span>{{ index + 1 }}</span>
        <span class="van-ellipsis">{{ item.plan_name }}</span>
        <span>{{ item.taskSum }}</span>
        <span>{{ item.finishNumber }}</span>
        <span>{{ item.percentage + "%" }}</span>
      </div>
    </template>
    <template v-if="type == 2">
      <div class="items" v-for="(item, index) in listData" :key="'list2-'+index">
        <span>{{ index + 1 }}</span>
        <span class="van-ellipsis">{{ item.distributionTeamName }}</span>
        <span>{{ item.sumNumber }}</span>
        <span>{{ item.finish }}</span>
        <span>{{ item.rate }}</span>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: ["listData", "type"],
  data() {
    return {};
  },
  methods: {},
};
</script>

<style scoped>
.list .theader {
  display: flex;
  background-color: #e8f0ff;
  height: 40px;
  padding: 0 16px;
}
.list .theader span {
  color: #2d4a74;
  font-size: 14px;
  font-weight: 500;
  line-height: 40px;
}
.list > div span:nth-child(1) {
  width: 12%;
}
.list > div span:nth-child(2) {
  width: 40%;
}
.list > div span:nth-child(3) {
  width: 20%;
}
.list > div span:nth-child(4) {
  width: 12%;
}
.list > div span:nth-child(5) {
  width: 16%;
}
.list .items {
  background-color: #fff;
  border-bottom: 1px solid #f5f6fb;
  height: 48px;
  display: flex;
  padding: 0 16px;
}
.list .items span {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 48px;
}
.list .items span:nth-child(3) {
  color: #29bebc;
}
</style>