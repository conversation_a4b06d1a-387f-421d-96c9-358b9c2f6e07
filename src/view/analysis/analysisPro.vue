<template>
  <div class="container">
    <Header title="任务概览" @backFun="$router.go(-1)"> </Header>
    <!-- <top-nav title="任务概览"></top-nav> -->
    <div class="nav">
      <span class="title">任务统计</span>
      <div class="btns">
        <div
          :class="['btn', dateType == 'day' ? 'active-btn' : '']"
          @click="changeBtn('day')"
        >
          今日
        </div>
        <div
          :class="['btn', dateType == 'month' ? 'active-btn' : '']"
          @click="changeBtn('month')"
        >
          本月
        </div>
        <div
          :class="['btn', dateType == 'year' ? 'active-btn' : '']"
          @click="changeBtn('year')"
        >
          本年
        </div>
      </div>
    </div>
    <div id="myChart" style="width: 100vw; height: 30vh"></div>
    <div id="myChart2" style="width: 100vw; height: 30vh"></div>
    <task-list :listData="listData" type="2"></task-list>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
import taskList from "./list.vue";
export default {
  components: {
    topNav,
    taskList,
  },
  data() {
    return {
      dateType: "month",
      echartData: [],
      echartData2: [],
      listData: [],
    };
  },
  created() {
    this.getChart1Data();
    this.getChart2Data();
    this.getTableData();
  },
  methods: {
    changeBtn(val) {
      this.dateType = val;
      this.getTableData();
      this.getChart1Data();
      this.getChart2Data();
    },
    initChart() {
      let myChart = this.$echarts.init(document.getElementById("myChart"));
      myChart.setOption({
        backgroundColor: "#fff",
        series: [
          // 主要展示层
          {
            radius: ["55%", "65%"],
            center: ["50%", "50%"],
            type: "pie",
            hoverAnimation: false,
            clockwise: true,
            startAngle: 360,
            silent: true,
            itemStyle: {
              normal: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#000081", // 0% 处的颜色
                    },
                    {
                      offset: 0.5,
                      color: "#0000fe",
                    },
                    {
                      offset: 1,
                      color: "#a5a5ff", // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
            },
            labelLine: {
              show: false,
            },
            label: {
              show: false,
            },
            data: [
              {
                value: this.echartData.taskFinishPercent,
              },
              {
                value: parseFloat(this.echartData.taskFinishPercent)+100,
                itemStyle: {
                  normal: {
                    color: "#fff",
                  },
                },
              },
            ],
          },
          {
            type: "pie",
            radius: "45%",
            center: ["50%", "50%"],
            z: 8,
            hoverAnimation: false,
            animation: false,
            itemStyle: {
              shadowBlur: 12,
              shadowColor: "rgba(0,0,0,.2)",
              // shadowOffsetX: 5,
              // shadowOffsetY: 5,
            },
            data: [
              {
                name: "",
                value: this.echartData.taskFinishPercent,
                itemStyle: {
                  normal: {
                    color: "#fff",
                  },
                },
                label: {
                  normal: {
                    rich: {
                      a: {
                        color: "#0025b3",
                        align: "center",
                        fontSize: 20,
                        fontWeight: "bold",
                        padding: 5,
                      },
                    },
                    color: "#000",
                    fontSize: 12,
                    formatter: function (params) {
                      return `巡检完成率\n{a|${params.value}%}`;
                    },
                    position: "center",
                    show: true,
                  },
                },
                labelLine: {
                  show: false,
                },
              },
            ],
          },
          // {
          //   type: "gauge",
          //   title: {
          //     show: false,
          //   },
          //   detail: {
          //     show: false,
          //   },
          //   itemStyle: {
          //     color: "#fff",
          //   },
          //   axisLine: {
          //     show: false,
          //   },
          //   splitLine: {
          //     show: false,
          //   },
          //   axisTick: {
          //     show: false,
          //   },
          //   axisLabel: {
          //     show: false,
          //   },
          //   pointer: {
          //     show: true,
          //     width: 15,
          //     length: "120%",
          //   },
          //   data: [
          //     {
          //       itemStyle: {
          //         color: "#000073",
          //       },
          //       value: 56,
          //       name: "2",
          //     },
          //   ],
          // },
        ],
      });
    },
    initChart2() {
      let myChart = this.$echarts.init(document.getElementById("myChart2"));
      myChart.setOption({
        backgroundColor: "#fff",
        color: [
          "rgba(246, 189, 22, 0.85)",
          "rgba(90, 216, 166, 0.85)",
          "rgba(91, 143, 249, 0.85)",
          "rgba(93, 112, 146, 0.85)",
          "#e3584e",
        ],
        series: [
          {
            type: "pie",
            // radius: ["40%", "60%"],
            radius: "60%",
            center: ["50%", "50%"],
            data: this.echartData2,
            hoverAnimation: false,
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 1,
              labelLine: {
                length: 20,
                length2: 16,
                show: true,
                lineStyle: {
                  color: "#ccc",
                },
              },
            },
            label: {
              position: "outside",
              formatter: (param) => {
                // console.log(param);
                return `{a|${param.percent}%}\n{b|${param.data.name}${param.value}件}`;
              },
              backgroundColor: "auto", //圆点颜色，auto：映射的系列色
              // height,width,lineHeight必须为0
              height: 0,
              width: 0,
              lineHeight: 18,
              // radius和padding为圆点大小，圆点半径为几radius和padding各项数值就为几如：圆点半径为1
              // borderRadius: 4,
              // padding: [4, -4, 4, -4],
              borderRadius: 4,
              padding: [4, -4, 4, -4],
              rich: {
                a: {
                  fontSize: 14,
                  padding: [18, 8, 0, 10],
                },
                b: {
                  padding: [18, 8, 0, 10],
                  color: "#595959",
                },
              },
            },
          },
        ],
      });
    },
    getChart1Data() {
      let params = {
        dateType: this.dateType,
      };
      this.axios.postContralHostBase("getAnalysisProChart2", params, (res) => {
        if (res.code == "200") {
          this.echartData = res.data;
          this.initChart();
        }
      });
    },
    getChart2Data() {
      let params = {
        dateType: this.dateType,
      };
      this.axios.postContralHostBase("getAnalysisChart2", params, (res) => {
        if (res.code == "200") {
          res.data.forEach((item) => {
            if (item.name.length > 8) {
              item.name = item.name.substr(0, 8) + "...";
            }
          });
          this.echartData2 = res.data;
          this.initChart2();
        }
      });
    },
    getTableData() {
      let params = {
        dateType: this.dateType,
      };
      this.axios.postContralHostBase("getAnalysisProList", params, (res) => {
        if (res.code == "200") {
          this.listData = res.data;
        }
      });
    },
  },
  mounted() {
    this.initChart();
    this.initChart2();
  },
};
</script>

<style scoped>
.container {
  width: 100vw;
  background-color: #f5f6fb;
}
.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  background-color: #fff;
  padding: 0 16px;
}
.nav .title {
  font-size: 16px;
  color: #353535;
  font-weight: 600;
}
.btns {
  display: flex;
  width: 52%;
  justify-content: space-between;
}
.btns .btn {
  width: 50px;
  height: 26px;
  border: 1px solid #29bebc;
  color: #29bebc;
  font-size: 13px;
  text-align: center;
  line-height: 26px;
  border-radius: 2px;
}
.active-btn {
  background-color: #29bebc;
  color: #fff !important;
}
</style>