<template>
  <div class="container">
    <Header title="风险统计" @backFun="goBack"> </Header>
    <!-- <div class="nav">
      <div class="title">风险类型统计</div>
    </div> -->
    <div id="myChart" style="width: 100vw; height: 300px"></div>
    <!-- <div class="nav">
      <div class="title">风险数量对比</div>
    </div> -->
    <div id="myChart2" style="width: 100vw; height: 400px"></div>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
import taskList from "../analysis/list.vue";
import Table from '../analysis/table.vue';
import axios from 'axios'
export default {
  components: {
    topNav,
    taskList,
    Table
  },
  data() {
    return {
      echartData: [],
      echartData2: [],
      listData: [],
      dateType: "month",
      loginInfo: '',
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
    };
  },
  created() {
    console.log('created')
    // apiready = () => {
    //   // if (!localStorage.getItem("loginInfo")) {
    //   var userInfo = api.getPrefs({
    //     sync: true,
    //     key: "userInfo"
    //   });
    //   userInfo = JSON.parse(userInfo);
    //   userInfo.hospitalCode = api.pageParam.hospitalCode
    //   userInfo.hospitalName = api.pageParam.hospitalName
    //   if (userInfo.id) {
    //     const virtualToken = encodeURIComponent(userInfo.hospitalName);
    //     localStorage.setItem('token', virtualToken);
    //     localStorage.setItem("loginInfo", JSON.stringify(userInfo));
    //   }
    //   // }
    //   this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    //   this.getChart1Data();
    //   this.getChart2Data();
    //   this.sysClickBack()
    // }
    if (JSON.parse(localStorage.getItem("loginInfo")).isHospital == 0) {
      var userInfo = api.getPrefs({
        sync: true,
        key: "userInfo"
      });
      userInfo = JSON.parse(userInfo);
      userInfo.hospitalCode = api.pageParam.hospitalCode
      userInfo.hospitalName = api.pageParam.hospitalName
      if (userInfo.id) {
        const virtualToken = encodeURIComponent(userInfo.hospitalName);
        localStorage.setItem('token', virtualToken);
        localStorage.setItem("loginInfo", JSON.stringify(userInfo));
      }
    }
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.getChart1Data();
    this.getChart2Data();
    this.sysClickBack()
  },
  mounted() {},
  methods: {
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    initChart() {
      let myChart = this.$echarts.init(document.getElementById("myChart"));
      myChart.setOption({
        title: {
          text: '风险类型统计',
          left: 'center'
        },
        backgroundColor: "#fff",
        color: ["rgba(91, 143, 249, 0.85)", "rgba(93, 112, 146, 0.85)"],
        xAxis: {
          type: 'category',
          data: this.echartData.xAxis
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: this.echartData.series,
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(180, 180, 180, 0.2)'
            },
            itemStyle: {
            }
          }
        ]
      });
    },
    initChart2() {
      let myChart = this.$echarts.init(document.getElementById("myChart2"));
      myChart.setOption({
        title: {
          text: '风险数量对比',
          left: 'center'
        },
        backgroundColor: "#fff",
        legend: {
          orient: 'vertical',
          left: 'left',
          data: this.echartData2.legend
        },
        color: [
          "rgba(246, 189, 22, 0.85)",
          "rgba(90, 216, 166, 0.85)",
          "rgba(91, 143, 249, 0.85)",
          "rgba(93, 112, 146, 0.85)",
          "#e3584e",
        ],
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            type: "pie",
            radius: ["35%", "60%"],
            center: ["50%", "50%"],
            data: this.echartData2.series,
            hoverAnimation: false,
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 1,
            },
            labelLine: {
              length: 10,
              length2: 5,
              show: true,
            },
            label: {
              position: "outside",
              formatter: "{b}",
              color: "#595959",
            },
          },
        ],
      });
    },
    getChart1Data() {
      const data = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode
      }
      axios({
        method: 'post',
        url: __PATH.BASEURL + 'statisticsController/getRiskStatsByType',
        params: data
      }).then(res => {
        if (res.data.code == '200') {
          this.echartData = res.data.data
          this.$nextTick(() => {
            this.initChart();
          })
        }
      }).catch(err => {
        console.log(err)
      })

      
    },
    getChart2Data() {
      const data = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode
      }
      axios({
        method: 'post',
        url: __PATH.BASEURL + 'statisticsController/getRiskStatsByLevel',
        params: data
      }).then(res => {
        if (res.data.code == '200') {
          console.log('数量', res.data.data)
          this.echartData2 = res.data.data
          this.$nextTick(() => {
            this.initChart2();
          })
        }
      }).catch(err => {
        console.log(err)
      })
    },
    goBack () {
      api.closeFrame({});
    }
  }
};
</script>

<style scoped>
.container {
  width: 100vw;
  background-color: #fff;
  padding-bottom: 5vh;
}
.nav {
  /* display: flex; */
  align-items: center;
  /* justify-content: space-between; */
  /* height: 60px; */
  background-color: #fff;
  /* padding: 0 16px; */
}
.nav .title {
  padding: 0 16px;
  font-size: 16px;
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid #CCC4C4;
  color: #353535;
  font-weight: 600;
}
</style>