<template>
  <div class="my-container-add">
    <Header title="耗材选择" @backFun="backFn"></Header>
    <!-- 搜索 -->
    <!-- <van-search
      v-model="name"
      show-action
      input-align="center"
      background="#F5F6FB"
      placeholder="请输入配件名称"
    >
      <template #action>
        <div class='search-btn' @click="onSearch">搜索</div>
      </template>
    </van-search> -->

    <page-loading v-show="pageLoading" />
    <div v-show="!pageLoading">
      <div class="my-content">
        <div v-for="(item, index) in accessoriesList" :key="index">
          <second-title
            :title="item.name"
            :style="{ 'margin-top': index == 0 ? '0' : '6px' }"
            :hasBorder="true"
          >
            <template #right>
              <!-- <div class="second-right" @click="showMore(index)">
                  <i class='iconfont'>&#xe66f;</i>
              </div> -->
            </template>
          </second-title>
          <div class="type-list">
            <div
              v-for="(item2, index2) in item.children"
              :key="index2"
              :class="item2.show ? 'type-checked' : ''"
              @click="chooseNum(index, index2)"
            >
              {{ item2.name }}
            </div>
          </div>
        </div>
      </div>

      <div class="my-bottom">
        <div>
          <van-badge :content="count" v-if="count">
            <div class="btm-num" @click="toList">
              <i class="iconfont">&#xeb6c;</i>
            </div>
          </van-badge>
        </div>

        <div class="btm-btn">
          <div class="btn-cancel" @click="cancel">取消</div>
          <div class="btn-comfirm" @click="confirm">确定</div>
        </div>
      </div>
    </div>

    <van-action-sheet v-model="show">
      <van-form
        @submit="onSubmit"
        style="margin-top: 20px"
        :show-error-message="false"
      >
        <div class="action-title">已选耗材（共{{ count }}件）</div>
        <div v-for="(item, index) in accessoriesList" :key="index">
          <div v-for="(item2, index2) in item.children" :key="index2">
            <van-field
              name="stepper"
              :label="item2.name"
              input-align="right"
              v-if="item2.show"
            >
              <template #button>
                <van-stepper
                  v-model="item2.accOutNum"
                  theme="round"
                  button-size="18"
                  disable-input
                  @overlimit="overlimit($event, index, index2)"
                  @change="numChange($event, index, index2)"
                />
              </template>
            </van-field>
          </div>
        </div>
      </van-form>
    </van-action-sheet>
  </div>
</template>

<script>
export default {
  name: "AddAccessories",

  data() {
    return {
      name: "",
      accessoriesList: [],
      show: false,
      price: "",
      stepper: "",
      currentAccessorie: {},
      count: 0,
      pageLoading: true,
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 返回
    backFn() {
      this.$router.go(-1);
    },
    numChange(e, index, index2) {
      this.getCount();
    },
    overlimit(e, index, index2) {
      this.accessoriesList[index].children[index2].show = false;
      this.getCount();
    },

    cancel() {
      this.$router.go(-1);
    },
    confirm() {
      localStorage.setItem(
        "addessoriesChecked",
        JSON.stringify(this.accessoriesList)
      );
      this.cancel();
    },
    getList() {
      let params = {
        workTypeCode: "8",
        name: this.name,
      };
      this.axios.postContralHostUrlOne("getConsumables", params, (res) => {
        const { code, data, message } = res;
        this.pageLoading = false;
        if (code == 200) {
          if (data && data.length) {
            data.forEach((item, index) => {
              if (item.nodeLevel == "1") {
                data.splice(index, 1);
              }
              item.show = false;
            });
            data[0].show = true;

            this.accessoriesList = this.utils.transData(
              data,
              "id",
              "parentId",
              "children"
            );

            let addessoriesCheckedList =
              localStorage.getItem("addessoriesChecked");
            addessoriesCheckedList = JSON.parse(addessoriesCheckedList);
            if (addessoriesCheckedList && addessoriesCheckedList.length)
              this.accessoriesList = addessoriesCheckedList;

            this.getCount();
          }
        }
      });
    },
    onSubmit() {},
    onSearch() {
      this.getList();
    },
    showMore(index) {
      this.accessoriesList[index].show = !this.accessoriesList[index].show;
    },
    chooseNum(index, index2) {
      this.accessoriesList[index].children[index2].show =
        !this.accessoriesList[index].children[index2].show;
      this.getCount();
    },
    // 获取选中的数量
    getCount() {
      let count = 0;
      this.accessoriesList.forEach((item) => {
        item.children.forEach((item2) => {
          if (item2.show) count += parseInt(item2.accOutNum);
        });
      });
      this.count = count;
    },
    toList() {
      this.show = !this.show;
    },
    doCancel() {
      this.price = "";
      this.show = false;
    },
  },
};
</script>
<style scoped lang="scss">
@import "../../../assets/stylus/theme.scss";
.my-container-add {
  background: $main-bgColor-gray;
  height: 100vh;
  .my-content {
    height: calc(100vh - 82px - 2rem);
    overflow-y: auto;
  }
}
/deep/ .van-overlay {
  height: calc(100vh - 82px);
}
/deep/ .van-action-sheet {
  margin-bottom: 82px;
}
/deep/ .van-popup--bottom.van-popup--round {
  border-radius: 0;
}
/deep/ .van-loading {
  height: calc(100vh - 2rem) !important;
}
.type-list {
  background: #fff;
  display: flex;
  flex-wrap: wrap;
  padding: 0px 16px 16px 16px;
  > div {
    background: #f4f4f4;
    border-radius: 10px;
    font-size: 15px;
    font-family: PingFangSC;
    font-weight: 400;
    color: #353535;
    padding: 10px 22px;
    margin: 12px 14px 0px 0;
  }
  .type-checked {
    background: #ebf9f9;
    color: #38c7c4;
  }
}

.van-search .van-cell {
  background-color: #fff;
  padding: 5px 8px 5px 5px;
}
.van-search--show-action {
  padding-right: 10px;
}
.search-btn {
  font-size: 17px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: $main-bgColor;
}
/deep/ .van-stepper--round .van-stepper__plus {
  background-color: #38c7c4;
}
/deep/ .van-stepper--round .van-stepper__minus {
  color: #fff;
  background-color: #d1d1d1;
  border: 1px solid #d1d1d1;
}
/deep/ .van-field__control--custom {
  justify-content: center;
}
/deep/ .van-field__error-message {
  text-align: right;
}

.my-bottom {
  background: #fff;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  border-top: 10px solid #f5f6fb;
}
.btm-btn {
  display: flex;
  align-items: center;
  .btn-cancel {
    font-size: 18px;
    font-family: PingFangSC;
    font-weight: 400;
    color: #38c7c4;
  }
  .btn-comfirm {
    width: 110px;
    height: 36px;
    line-height: 36px;
    background: #38c7c4;
    border-radius: 6px;
    font-size: 18px;
    font-family: PingFangSC;
    font-weight: 400;
    color: #ffffff;
    text-align: center;
    margin-left: 30px;
  }
}
.btm-num {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #efeff4;
  display: flex;
  justify-content: center;
  align-items: center;
  i {
    font-size: 26px;
    color: #38c7c4;
  }
}
/deep/ .van-badge {
  background-color: #f12c20;
  padding: 2px 5px;
}
/deep/ .van-badge--fixed {
  position: absolute;
  top: 5px;
  right: 6px;
}
.action-title {
  font-size: 16px;
  font-family: PingFangSC;
  font-weight: 400;
  color: #353535;
  padding: 0 0 16px 16px;
  position: relative;
}
.action-title::after {
  position: absolute;
  box-sizing: border-box;
  content: " ";
  pointer-events: none;
  right: 0;
  bottom: 0;
  left: 0;
  border-bottom: 1px solid #ebedf0;
  transform: scaleY(0.5);
}
/deep/ .van-field__label {
  font-size: 15px;
  font-family: PingFangSC;
  font-weight: 400;
  color: #353535;
}
/deep/ .van-field__label {
  width: 15.2em !important;
}
/deep/ .van-field__control {
  display: none;
}
</style>