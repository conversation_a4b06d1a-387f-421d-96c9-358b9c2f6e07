<template>
  <div class="feed-back">
    <Header :title="title" @backFun="backFnHeader"></Header>
    <page-loading v-show="pageLoading" />
    <div v-show="!pageLoading">
      <div class="feed-ctn">
        <div>
          <!-- <div class="list-item list-flex list-between">
            <van-field
              disabled
              v-model="hospitalInfo.officeName"
              label="问题科室"
            />
            <i class='iconfont iconfontScan' @click.stop='APPScan()'>&#xe665;</i>
          </div> -->
          <!-- <div class="list-item list-flex">
            <van-field
              disabled
              v-model="equipInfo.parentGridNames"
              label="问题地点"
            />
          </div> -->
          <div class="list-item list-flex list-between">
            <!-- <van-field disabled v-model="taskPointName" label="任务点" /> -->
            <div class="task_first">
              <div class="list-title_point">任务点</div>
              <div class="list-title_point_name">{{ taskPointName }}</div>
            </div>
            <div>
              <i
                class="iconfont iconfontScan"
                @click.stop="APPScan()"
                v-show="canScan"
                >&#xe665;</i
              >
            </div>
          </div>
          <div class="list-item">
            <div class="list-title">问题描述</div>
            <van-field
              @input="
                questionDescription = questionDescription.replace(regStr, '')
              "
              v-model="questionDescription"
              rows="2"
              autosize
              label=""
              type="textarea"
              maxlength="500"
              placeholder="请输入您要申报的内容描述、语音，字数在500字以内，语音限制60秒"
            />
            <sounds-recording
              @getRecord="getRecord"
              @getRecordFile="getRecordFile"
              ref="soundsRecording"
            />
          </div>
          <div class="list-item">
            <div class="list-flex list-between">
              <div class="list-title">上传附件</div>
              <div class="list-text">注：照片最多上传九张</div>
            </div>
            <div style="margin-top: 20px">
              <van-uploader
                :max-size="maxSize * 1024 * 1024"
                @oversize="onOversize"
                accept="image/*"
                :disabled="fileMessage == '上传中'"
                v-model="fileList"
                max-count="9"
                :after-read="afterImgRead"
              />
            </div>
          </div>
        </div>

        <div class="feed-btm">
          <div v-if="type == 2">
            <second-title title="耗材实用">
              <template #right>
                <div class="title-right" @click="toAccessories">
                  <span>请选择</span>
                  <i class="iconfont">&#xe684;</i>
                </div>
              </template>
            </second-title>
            <div>
              <div class="parts-container">
                <div v-for="(item, index) in addessoriesChecked" :key="index">
                  <div v-for="(item2, index2) in item.children" :key="index2">
                    <div v-if="item2.show" class="parts-ctn">
                      <div class="parts-title">{{ item2.name }}</div>
                      <van-stepper
                        v-model="item2.accOutNum"
                        theme="round"
                        button-size="18"
                        disable-input
                        @overlimit="overlimit($event, index, index2)"
                        @change="numChange($event, index, index2)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="my-info">
            <!-- <van-form @submit="toSubmit" >      -->
            <van-field
              @input="callerName = callerName.replace(regStr, '')"
              v-model="callerName"
              label="联系人"
              placeholder="请输入"
              :rules="[{ required: true, message: '请输入联系人' }]"
            />
            <van-field
              @input="sourcesPhone = sourcesPhone.replace(regStr, '')"
              v-model="sourcesPhone"
              label="联系方式"
              placeholder="请输入"
              :rules="[
                { validator: validateMobileNum, message: '请填写正确的手机号' },
                { required: true, message: '请输入联系方式' },
              ]"
            />
            <!-- </van-form> -->
          </div>
        </div>
      </div>

      <div class="feed-btn">
        <van-button
          size="large"
          @click="toSubmit"
          type="primary"
          native-type="submit"
          :loading="loading"
          loading-text="正在提交..."
          >提交</van-button
        >
      </div>
    </div>
    <!-- 维修班组弹框 -->
    <van-popup
      v-model="hospitalShow"
      position="bottom"
      :style="{ height: '40%' }"
      :close-on-click-overlay="false"
    >
      <van-picker
        title="选择科室"
        show-toolbar
        :columns="hospitalListName"
        @confirm="hospitalComfirm"
        @cancel="cancel"
        cancel-button-text=" "
      />
    </van-popup>
  </div>
</template>
  
<script>
import { validateMobile } from "@/common/utils/validate";
export default {
  name: "feedback",
  data: function () {
    return {
      stepper: "",

      maxSize: 5, //
      fileMessage: "",
      fileName: "",
      fileList: [],

      type: "1",
      title: "整改反馈",

      equipInfo: {},
      loginInfo: {},
      callerName: "",
      sourcesPhone: "",
      questionDescription: "",
      hospitalList: [],
      hospitalListName: [],
      hospitalShow: false,
      hospitalInfo: {},
      addessoriesChecked: [],
      loading: false,
      pageLoading: false,
      recordingInfo: "",
      recordingInfoFile: "",
      abnormalCtn: {},
      taskPointName: "",
      taskPointId: "",
      taskPointInfo: "",
      canScan: true,
      bookArr: [],
      from: "",
      spyScan: "",
      taskPointInfo: "",
      fromPage: "",
      projList: [],
      regStr:
        /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi,
    };
  },
  methods: {
    cancel() {},
    numChange(e, index, index2) {},
    overlimit(e, index, index2) {
      this.addessoriesChecked[index].children[index2].show = false;
    },

    getRecord(info) {
      this.recordingInfo = info;
    },
    getRecordFile(info) {
      this.recordingInfoFile = info;
    },
    toAccessories() {
      if (this.addessoriesChecked && this.addessoriesChecked.length)
        localStorage.setItem(
          "addessoriesChecked",
          JSON.stringify(this.addessoriesChecked)
        );
      // localStorage.setItem(
      //   "feekbackInfo",
      //   JSON.stringify({
      //     taskPointName: this.taskPointName,
      //     taskPointId: this.taskPointId,
      //     questionDescription: this.questionDescription,
      //     fileList: this.fileList,
      //     taskPointInfo: this.taskPointInfo,
      //     spyScan: this.spyScan,
      //   })
      // );
      this.$router.push({
        path: "/addAccessories",
      });
    },
    onOversize() {
      this.$toast.fail(`文件大小不能超过${this.maxSize}M`);
    },
    // 图片上传成功回调
    afterImgRead(file) {
      file.status = "uploading";
      file.message = "上传中...";
      this.fileMessage = "上传中";
      // this.fileName=file.file.name;
      this.handleUploadImg(file);
    },
    handleUploadImg(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostUrlOne("uploadFileToOSS", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.fileMessage = "上传成功";
          this.fileList[this.fileList.length - 1].url = data;
        }
      });
    },
    //APP扫码
    APPScan() {
      const YBS = this.utils;
      if (!YBS.hasPermission("storage")) {
        let pageParam = { 
          title: "存储权限使用说明",
          cont: "用于存储图片、视频等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["storage"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
              if (!YBS.hasPermission("camera")) {
                let pageParam = {
                  title: "摄像机权限使用说明",
                  cont: "用于扫描巡检码、空间码、拍照上传等场景"
                };
                YBS.openCustomDialog(pageParam, function () {
                  YBS.reqPermission(["camera"], function (ret) {
                    if (ret && ret.list.length > 0 && ret.list[0].granted) {
                    }
                  });
                })
                return;
              }
            }
          });
        });
        return;
      }
      if (!YBS.hasPermission("camera")) {
        let pageParam = {
          title: "摄像机权限使用说明",
          cont: "用于扫描巡检码、空间码、拍照上传等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["camera"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
            }
          });
        })
        return;
      }
      try {
        this.utils.scanCode().then(
          (item) => {
            if (item && item.length) {
              // this.pageLoading = true;
              this.getEquipmentByIdFn(item.join(","));
            } else {
              this.$toast.fail("未查找到相关设备");
            }
          },
          () => {
            this.$toast.fail("未查找到相关设备");
          }
        );
      } catch (e) {
        this.$toast.fail(e);
      }
    },
    getEquipmentByIdFn(gridCode) {
      let params = {
        // gridCode:1443508214420946944,
        typeValue: gridCode,
        flag: true,
      };
      this.axios.postContralHostBase("getSignTaskPoint", params, (res) => {
        const { code, data } = res;
        if (code == 200) {
          this.taskPointName = data.taskPointName;
          this.taskPointId = data.id;
        }
      });
      // this.axios.postContralHostBase("getGridInfo", params, (res) => {
      //   const { code, data, message } = res;
      //   if (code == 200) {
      //     this.equipInfo = data;
      //     if (data.parentGridName) {
      //       this.equipInfo.parentGridNames =
      //         data.parentGridName + ">" + data.gridName;
      //     }
      //     this.getHospital(data.id);
      //   } else {
      //     this.pageLoading = false;
      //   }
      // });
    },
    getHospital(gridId) {
      this.axios.postContralHostUrlOne(
        "hospitalOfficeByGridId",
        { gridId },
        (res) => {
          const { code, data, message } = res;
          this.pageLoading = false;
          if (code == 200) {
            if (data.length <= 0)
              return this.$toast.fail("该空间码暂无关联科室");
            if (data.length == 1) {
              this.hospitalInfo = data[0];
            } else {
              this.hospitalShow = true;
              this.hospitalList = data;
              this.hospitalListName = this.hospitalList.map((item) => {
                return item.officeName;
              });
            }
          }
        }
      );
    },
    hospitalComfirm(value, index) {
      this.hospitalCancel();
      this.hospitalInfo = this.hospitalList[index];
    },
    hospitalCancel() {
      this.hospitalShow = false;
    },
    toSubmit() {
      const urls = this.fileList.length
        ? this.fileList.map((item) => {
            return item.url;
          })
        : [];
      const {
        userId,
        name,
        type,
        phone,
        sysIdentity,
        companyCode,
        companyName,
        id,
        workTeamId,
      } = this.loginInfo;
      const {
        callerName,
        sourcesPhone,
        equipInfo,
        questionDescription,
        hospitalInfo,
        addessoriesChecked,
        recordingInfo,
        fileMessage,
        recordingInfoFile,
        taskPointId,
        taskPointName,
      } = this;
      // if (!hospitalInfo.id) return this.$toast.fail("请先选择科室");
      if (!taskPointId) return this.$toast.fail("请先扫描任务点");
      if (!callerName && this.type != 2)
        return this.$toast.fail("请输入联系人");
      if (!sourcesPhone && this.type != 2)
        return this.$toast.fail("请输入联系方式");
      if (fileMessage == "上传中")
        return this.$toast.fail("附件正在上传，请稍后...");
      if (!this.validateMobileNum(sourcesPhone))
        return this.$toast.fail("请输入合法的联系方式");
      // let parentGridName = equipInfo.parentGridName.replace(/>/g, "");
      if (recordingInfoFile && !recordingInfo)
        return this.$toast.fail("录音正在上传，请稍后...");

      if (!recordingInfo && !questionDescription)
        return this.$toast.fail("问题描述和录音必填一项");
      this.loading = true;

      let params = {
        // workTeamId,
        // createByCode:userId,
        // createByName:name,
        // createByJobnum:'',
        createByJob: sysIdentity,
        // createByPhone:phone,
        type,

        // workSources:'3',
        // sourcesDept: hospitalInfo.id,
        // sourcesDeptName: hospitalInfo.officeName,
        // location:equipInfo.parentGridId+','+equipInfo.id,
        // locationName:parentGridName+equipInfo.gridName,
        // locationPhone: hospitalInfo.officePhone,
        questionDescription,
        taskPointName,
        taskPointId,
      };
      console.log(params);
      if (this.type != 2) {
        // 有电话姓名时
        params.callerCode = userId;
        params.callerName = callerName;
        params.sourcesPhone = sourcesPhone;
      } else {
        // 有耗材时
        let consumableListJson = [];
        addessoriesChecked &&
          addessoriesChecked.length &&
          addessoriesChecked.map((item) => {
            item.children.map((item2) => {
              if (item2.show) {
                item2.depId = item2.id;
                item2.depName = item2.name;
                // item2.accUseDeptCode = hospitalInfo.id;
                // item2.accUseDeptName = hospitalInfo.officeName;
                item2.taskPointId = this.taskPointId;
                item2.totalPrice = item2.price * item2.accOutNum;
                consumableListJson.push(item2);
              }
            });
          });
        consumableListJson = JSON.stringify(consumableListJson);
        params.consumableListJson = consumableListJson;
      }
      if (this.type == 3) {
        // 报修时
        params.workTypeCode = "10";
        params.attachment = JSON.stringify(urls);
        params.callerTape = recordingInfo;
        params.callerCompanyCode = companyCode;
        params.callerCompanyName = companyName;

        params.phoneNumber = phone;
        params.realName = name;
        // params.localtion = equipInfo.parentGridId + "," + equipInfo.id;
        // params.localtionName = parentGridName + equipInfo.gridName;
        params.userId = userId;
        params.staffId = id;
        params.workSources = "1";
      } else {
        // 自修。整改。
        params.createByPhone = phone;
        params.createByName = name;
        params.createByCode = id;

        // params.location = equipInfo.parentGridId + "," + equipInfo.id;
        // params.locationName = parentGridName + equipInfo.gridName;

        params.callerTapeUrl = recordingInfo;
        params.attachmentUrl = urls.join(",");
      }
      // return;
      const obj = {
        1: "saveOrderAbarbeitung",
        2: "saveOrderFixByOwn",
        3: "saveTaskByWeChat",
      };
      const url = obj[this.type];
      this.toSubmitAjax(url, params);
    },
    // 提交
    toSubmitAjax(url, params) {
      this.axios.postContralHostUrlOne(url, params, (res) => {
        const { code, message, data } = res;
        if (code == 200) {
          this.submitToIcms(data, params);
          this.$toast.success(message);
          console.log("未跳转", this.abnormalCtn);
          if (JSON.stringify(this.abnormalCtn) == "{}") {
            this.loading = false;
            setTimeout(() => {
              this.backFn();
            }, 1000);
          } else {
            this.submissionDescribeFn(data.workNum);
          }
        } else {
          this.loading = false;
        }
      });
    },
    // 修改任务点状态并提交描述
    submissionDescribeFn(guaranteeCode) {
      const urls = this.fileList.length
        ? this.fileList.map((item) => {
            return item.url;
          })
        : [];

      const {
        abnormalCtn,
        questionDescription,
        type,
        loginInfo,
        recordingInfoFile,
      } = this;
      const { id, name } = loginInfo;
      const {
        taskId,
        selectdArr,
        planId,
        localtionCode,
        regionId,
        spyScan,
        workType,
      } = abnormalCtn;
      const stateObj = {
        1: "3",
        2: "4",
        3: "4",
      };
      // 0 合格
      // 1 不合格
      // 2 异常反馈

      let state = workType == 1 ? 3 : 4;
      let params = {
        // maintainId:taskId,
        details: questionDescription,
        state,
        guaranteeCode,
        // callerTape:'',
        maintainWorkIds: selectdArr,
        staffId: id,
        staffName: name,
        planId,
        roomCode: localtionCode,
        regionId,
        attachmentUrl: urls.join(","),
        spyScan,
        files: {
          callerTape: recordingInfoFile,
        },
      };
      this.axios.postContralHostBase("submissionDescribe", params, (res) => {
        this.loading = false;
        const { code, message } = res;
        if (code == 200) {
          setTimeout(() => {
            this.backFn();
          }, 1000);
        } else {
          this.$toast.fail(message);
        }
      });
    },
    // 校验手机号
    validateMobileNum(value) {
      return validateMobile(value);
    },
    //反馈信息提交到icms
    submitToIcms(data, params) {
      if (!this.taskPointInfo) return;
      let param = {};
      let arr = [];
      this.taskPointInfo.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach(
        (item) => {
          item.maintainProjectdetailsTermReleaseList.forEach((item2) => {
            if (item2.isNum == "3") {
              let obj = {
                id: item2.id,
                value: item2.radio,
              };
              arr.push(obj);
            } else if (item2.isNum != "1") {
              let obj = {
                id: item2.id,
                value: item2.value,
              };
              arr.push(obj);
            }
          });
        }
      );
      param.answerMapList = JSON.stringify(arr);
      param.attachmentUrl = params.attachmentUrl;
      param.callerTapeUrl = params.callerTapeUrl;
      param.state = "4";
      param.spyScan = this.spyScan;
      param.isBookEmpty = false;
      param.guaranteeCode = data.workNum;
      param.taskId = this.taskPointInfo.taskId;
      param.taskPointReleaseId =
        this.taskPointInfo.maintainProjectRelease.taskPointReleaseId;
      param.staffId = this.loginInfo.id;
      param.staffName = this.loginInfo.name;
      param.details = params.questionDescription;
      this.axios.postContralHostBase("inspectionSubmit", param, (res) => {});
    },
    // 返回
    backFn() {
      localStorage.removeItem("addessoriesChecked");
      localStorage.removeItem("feekbackInfo");
      let role = JSON.parse(localStorage.getItem("role"));
      if (this.from == "workerHome" || role != "manager") {
        this.$router.push({
          path: "workerHome",
        });
      } else if (role == "teamWork" || role == "teamLeader") {
        this.$router.push({
          path: "manageHome",
        });
      }
      // api.closeToWin({
      //   name: "home/project",
      // });
    },
    backFnHeader() {
      localStorage.removeItem("addessoriesChecked");
      localStorage.removeItem("feekbackInfo");
      // api.closeWin();
      if (this.fromPage == "bookSelect") {
        this.$router.push({
          path: "inspectionSelect",
          query: {
            taskPointInfo: this.taskPointInfo,
            spyScan: this.spyScan,
            type: this.type,
            projList: this.projList,
          },
        });
      } else {
        this.$router.go(-1);
      }
    },
    init() {
      // 获取app返回的信息
      apiready = () => {
        // 登录信息
        var userInfo = api.getPrefs({
          sync: true,
          key: "userInfo",
        });
        userInfo = JSON.parse(userInfo);
        this.loginInfo = userInfo;
        if (userInfo.id) {
          localStorage.setItem("loginInfo", JSON.stringify(userInfo));
          this.callerName = userInfo.name;
          this.sourcesPhone = userInfo.phone;
        }

        // 巡视信息
        var info = api.getPrefs({
          sync: true,
          key: "abnormalCtn",
        });
        if (info) {
          info = JSON.parse(info);
          this.abnormalCtn = info;
          this.questionDescription = info.selectdStr;
          let barCode = info.locationInfo.barCode;
          if (barCode.length) this.getEquipmentByIdFn(barCode[3]);
        }
      };
      const { type } = this.$route.query;
      this.type = type;
      const obj = {
        1: "整改反馈",
        2: "自修反馈",
        3: "报修反馈",
      };
      this.title = obj[type];
      if (this.type != 2) {
        let userInfo = localStorage.getItem("loginInfo");
        userInfo = JSON.parse(userInfo);
        console.log(userInfo);
        this.callerName = userInfo.name;
        this.sourcesPhone = userInfo.phone;
      } else {
        this.callerName = "";
        this.sourcesPhone = "";
      }
      // this.getEquipmentByIdFn('BJZKYX0401001001');
      let addessoriesChecked = localStorage.getItem("addessoriesChecked");
      // let feekbackInfo = localStorage.getItem("feekbackInfo");
      addessoriesChecked = JSON.parse(addessoriesChecked);
      // feekbackInfo = JSON.parse(feekbackInfo);
      if (addessoriesChecked) this.addessoriesChecked = addessoriesChecked;
      // if (feekbackInfo) {
      //   this.taskPointName = feekbackInfo.taskPointName;
      //   this.taskPointId = feekbackInfo.taskPointId;
      //   this.questionDescription = feekbackInfo.questionDescription;
      //   this.fileList = feekbackInfo.fileList;
      //   this.taskPointInfo = feekbackInfo.taskPointInfo;
      //   this.spyScan = feekbackInfo.spyScan;
      // }
    },
  },
  created() {
    this.taskPointInfo = this.$route.query.taskPointInfo || "";
    this.bookArr = this.$route.query.bookArr || "";
    this.from = this.$route.query.from || "";
    this.spyScan = this.$route.query.spyScan || "";
    this.fromPage = this.$route.query.fromPage || "";
    this.projList = this.$route.query.projList || "";
    if (this.bookArr.length > 0) {
      let arr = [];
      this.bookArr.forEach((item, index) => {
        if (index == 0) {
          arr.push(index + 1 + "." + item.detailName + "\n");
        } else {
          arr.push("\n" + (index + 1) + "." + item.detailName + "\n");
        }
        item.maintainProjectdetailsTermReleaseList.forEach((item2, index2) => {
          arr.push(
            index +
              1 +
              "-" +
              JSON.stringify(index2 + 1) +
              ".巡检要点:" +
              (item2.content||'')
          );
          if (item2.isNum == "3") {
            arr.push(",巡检内容:" + item2.radio + "\n");
          } else if (!item2.value) {
            arr.push(",巡检内容:无" + "\n");
          } else {
            arr.push(",巡检内容:" + item2.value + "\n");
          }
        });
      });
      this.questionDescription = arr.join("");
    }
    if (this.taskPointInfo.id) {
      this.taskPointName = this.taskPointInfo.taskPointName;
      this.taskPointId = this.taskPointInfo.id;
      this.canScan = false;
    }
  },
  mounted() {
    this.init();
    setTimeout(() => {
      this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    }, 1000);
  },
  activated() {
    console.log("111", this.$route.meta);
    if (!this.$route.meta.isUseCache) {
      this.taskPointName = "";
      this.questionDescription = "";
      this.fileList = [];
      this.$refs.soundsRecording.handleDelVoiceClick();
      this.taskPointInfo = this.$route.query.taskPointInfo || "";
      this.bookArr = this.$route.query.bookArr || "";
      this.from = this.$route.query.from || "";
      this.spyScan = this.$route.query.spyScan || "";
      this.fromPage = this.$route.query.fromPage || "";
      this.projList = this.$route.query.projList || "";
      if (this.bookArr.length > 0) {
        let arr = [];
        this.bookArr.forEach((item, index) => {
          if (index == 0) {
            arr.push(index + 1 + "." + item.detailName + "\n");
          } else {
            arr.push("\n" + (index + 1) + "." + item.detailName + "\n");
          }
          item.maintainProjectdetailsTermReleaseList.forEach(
            (item2, index2) => {
              arr.push(
                index +
                  1 +
                  "-" +
                  JSON.stringify(index2 + 1) +
                  ".巡检要点:" +
                  (item2.content||'')
              );
              if (item2.isNum == "3") {
                arr.push(",巡检内容:" + item2.radio + "\n");
              } else if (!item2.value) {
                arr.push(",巡检内容:无" + "\n");
              } else {
                arr.push(",巡检内容:" + item2.value + "\n");
              }
            }
          );
        });
        this.questionDescription = arr.join("");
      }
      if (this.taskPointInfo.id) {
        this.taskPointName = this.taskPointInfo.taskPointName;
        this.taskPointId = this.taskPointInfo.id;
        this.canScan = false;
      }
    }
    this.init();
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.name == "addAccessories") {
        vm.$route.meta.isUseCache = true;
      } else {
        vm.$route.meta.isUseCache = false;
      }
    });
  },
};
</script>
  
<style  lang="scss" scoped>
@import "../../../assets/stylus/theme.scss";
.feed-back {
  height: 100vh;
}
.feed-ctn {
  height: calc(100vh - 2rem - 71px);
  overflow: auto;
  > div {
    background: #fff;
  }
}
.list-item {
  padding: 0 14px;
  position: relative;
}
.list-item::after {
  position: absolute;
  box-sizing: border-box;
  content: " ";
  pointer-events: none;
  right: 0;
  bottom: 0;
  left: 0;
  border-bottom: 1px solid #ebedf0;
  transform: scaleY(0.5);
}
.list-flex {
  display: flex;
  align-items: center;
}
.list-between {
  justify-content: space-between;
}
.list-title_point {
  font-size: 15px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #353535;
  margin-right: 20px;
  width: 23vw;
  margin: 14px 0;
}
.list-title_point_name {
  font-size: 15px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #353535;
  margin-right: 20px;
  width: 62vw;
  margin: 14px 0;
}
.list-title {
  font-size: 15px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #353535;
  margin-right: 20px;
  width: 80px;
  margin: 14px 0;
}
.list-text {
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #888888;
}
.task_first {
  display: flex;
}
.iconfontScan {
  color: #00cac8;
  font-size: 30px;
}
.title-right {
  font-size: 13px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #38c7c4;
}
.feed-btm {
  // margin-top: 10px;
  border-top: 10px solid #f5f6fb;
  background: #fff;
}
.parts-container {
  margin: 10px 0;
  min-height: 50px;
  // background: #FFF;
}
.parts-container .parts-ctn {
  background: #f5f6fb;
  margin: 0 20px;
  padding: 10px 30px;
  display: flex;
  align-items: center;
  font-size: 15px;
  font-family: PingFangSC;
  font-weight: 400;
  color: #353535;
}
.parts-title {
  margin-right: 20px;
  max-width: 150px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  flex: 1;
}
.feed-btn {
  padding: 10px 16px;
  border-top: 1px solid #f5f6fb;
}
/deep/ .van-cell {
  padding: 14px 0;
}
.my-info {
  /deep/ .van-cell {
    padding: 14px;
  }
}

/deep/ .van-cell::after {
  border-bottom: none;
}
/deep/ .van-stepper--round .van-stepper__plus {
  background-color: $main-bgColor;
}
/deep/ .van-stepper--round .van-stepper__minus {
  color: #fff;
  background-color: #d1d1d1;
  border: 1px solid #d1d1d1;
}
/deep/ .van-button--primary {
  background-color: $main-bgColor;
  border: 1px solid $main-bgColor;
  border-radius: 8px;
}
/deep/ .van-field__label {
  font-size: 15px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #353535;
  margin-right: 20px;
  width: 80px;
}
/deep/ .van-loading {
  height: calc(100vh - 2rem) !important;
}
/deep/ .van-field__control:disabled {
  color: #323233;
  -webkit-text-fill-color: #323233;
}
/deep/ .van-field--disabled .van-field__label {
  color: #353535;
}
/deep/ .van-uploader__loading {
  height: 22px !important;
}
</style>
  