<template>
  <div class="complaint">
    <Header :title="title" @backFun="backFn"></Header>
    <van-form @submit="toSubmit" :show-error-message="false">   
      <div class="complaint-ctn">
        <div class="list-item1 list-flex list-between">
          <div class="list-title">身份隐匿</div>
          <van-radio-group v-model="radio">
            <van-radio v-for="(item,index) in identityList" :key="index" :name="item.dictValue">{{item.dictLabel}}</van-radio>
          </van-radio-group>
        </div> 
        <div class="list-item1 list-flex list-between">
          <div class="list-title">关联工单</div>
          <div class="list-text" style="color:#353535">{{workNum}}</div>
        </div>
        <div class="list-item2">
          <div class="list-title">投诉标题</div>
          <van-field
           @input="complaintTitle = complaintTitle.replace(regStr, '')"
            v-model="complaintTitle"
            rows="2"
            autosize
            label=""
            type="textarea"
            maxlength="20"
            placeholder="请输入投诉标题，字数在20字以内"
            :rules="[
              { required: true, message: '请输入投诉标题' }
            ]"
          />
        </div>
        <div class="list-item">
          <div class="list-title">问题描述</div>
          <van-field
           @input="questionDescription = questionDescription.replace(regStr, '')"
            v-model="questionDescription"
            rows="2"
            autosize
            label=""
            type="textarea"
            maxlength="120"
            :rules="[
              { required: true, message: '请输入投诉描述' }
            ]"
            placeholder="请输入投诉描述，字数在120字以内，或直接语音发布，限时60秒"
          />
          <sounds-recording @getRecord="getRecord" /> 
        </div>
        <div class="list-item">
          <div class="list-flex list-between">
            <div class="list-title">上传附件</div>
            <div class="list-text">注：照片最多上传三张</div>
          </div>
          <div style="margin-top:20px">
            <van-uploader 
            :max-size="maxSize *1024 * 1024"
            @oversize="onOversize" 
            accept="image/*" 
            :disabled="fileMessage=='上传中'" 
            v-model="fileList"
            max-count='3' 
            :after-read="afterImgRead"  />
          </div>
          
        </div>
      </div>
      <div class="complaint-btn">
        <van-button type="primary" size="large"  :loading="loading"  native-type="submit" loading-text="正在提交..." @submit="toSubmit" >提交</van-button>
      </div>
    </van-form>
    

  </div>
</template>
  
<script>

export default {
  data: function() {
    return {

      maxSize:5,//
      fileMessage:'',
      fileName:'',
      fileList:[],

      title:'后勤投诉',
      radio:'0',
      identityList:[],
      workNum:'',
      loginInfo:{},
      questionDescription:'',
      complaintTitle:'',
      recordingInfo:'',
      loading:false,
      regStr:/[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi

    };
  },
  methods: {
    numChange(){},

    getRecord(info){
      this.recordingInfo = info;
    },
    onOversize(){
      this.$toast.fail(`文件大小不能超过${this.maxSize}M`);
    },
    // 图片上传成功回调
    afterImgRead(file){
      file.status = 'uploading';
      file.message = '上传中...';
      this.fileMessage = '上传中';
      // this.fileName=file.file.name;
      this.handleUploadImg(file);
    },
    handleUploadImg(file){
      let params = {
        file:file.file,
      }
      this.axios.postContralHostUrlOne("uploadFileToOSS",params,res=>{
        const {code,data,message} = res;
        if(code==200){
          file.status = 'success';
          file.message = '上传成功';
          this.fileMessage = '上传成功';
          this.fileList[this.fileList.length-1].url=data;
        }
      })
    },
    // 提交
    toSubmit(){
      this.loading=true;
      const urls = this.fileList.length?this.fileList.map(item=>{return item.url}):[]
      const {workNum,loginInfo,radio,questionDescription,recordingInfo,fileList,complaintTitle} =this;
      const {officeCode,id,phone,name,officeName,type,sysIdentity,userId} = loginInfo;
      let params = {
          deptCode: officeCode,
          userId,
          phoneNumber: phone,
          realName: name,
          workTypeCode: "5",
          relevanceWorkNum: workNum,
          signatureFlag: radio,
          deptName: officeName,
          questionDescription,
          callerTape: recordingInfo,
          attachment: JSON.stringify(urls),
          type,
          complaintTitle,
          job: sysIdentity,
          staffId:id,
          workSources: "1"
      }
      //  params.files={
      //     callerTape:recordingInfo
      //   }
      this.axios.postContralHostUrlOne(
        'saveTaskByWeChatAPP',
        params,
        res => {
          this.loading=false;
          const {code,message}=res;
          if (code == 200) {
            this.$toast.success(message);
            setTimeout(()=>{this.backFn()});
          }
        }
      );
    },
    backFn(){
      this.$router.go(-1);
    },
    // 身份列表查询
    getPersonnelDictionaryFn(){
      let params ={
        type:'10',
        states:'1',
      }
      this.axios.postContralHostUrlOne("getPersonnelDictionary",params,res=>{
        const {code,data,message} = res;
          if(code==200){
            this.identityList=data;
          }
        })
    },
  },
  mounted() {
    const {id,workNum} = this.$route.query;
    this.workNum=workNum;
    let loginInfo= JSON.parse(localStorage.getItem("loginInfo"));
    this.loginInfo=loginInfo;
  },
  created(){
    this.getPersonnelDictionaryFn();
  },
  watch: {
    
  }
};
</script>
  
<style  lang="scss" scoped>
@import "../../../assets/stylus/theme.scss";
.complaint {
  height: 100vh;
  background: $main-bgColor-gray;
}
.complaint-ctn {
  height: calc(100vh - 2rem - 90px);
  overflow: auto;
}
.list-item1{
  padding: 14px;
  background: #FFF;
  margin-bottom: 10px;
}
.list-item2{
  padding: 14px 14px 0 14px;
  background: #FFF;
  margin-bottom: 10px;
  /deep/ .van-cell {
    padding: 20px 0 0 0;
  }
}
.list-item{
  padding: 14px;
  position: relative;
  background: #FFF;
}
.list-item::after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  right: 0;
  bottom: 0;
  left: 0;
  border-bottom: 1px solid #ebedf0;
  transform: scaleY(.5);
}
.list-flex {
  display: flex;
  align-items: center;
}
.list-between {
  justify-content: space-between; 

}
.list-title {
  font-size: 15px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #353535;
  margin-right: 20px;
  width: 80px;
}
.list-text {
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #888888;
}
.complaint-btn {
  background: #FFF;
  margin-top: 10px;
  padding: 15px;
  display: flex;
  flex-direction: row-reverse;
}
/deep/ .van-cell {
  padding: 50px 0;
}
/deep/ .van-cell::after{
  border-bottom:none;
}
/deep/ .van-button--primary {
  background-color: $main-bgColor;
  border: 1px solid $main-bgColor;
  border-radius: 8px;
}
/deep/ .van-radio-group {
  display: flex;
  justify-content: space-around;
  font-size: 14px;
}
/deep/ .van-radio__icon--checked .van-icon{
  background-color: $main-bgColor;
  border-color: $main-bgColor;
}
/deep/ .van-radio{
  margin-left: 20px;
}
</style>
  