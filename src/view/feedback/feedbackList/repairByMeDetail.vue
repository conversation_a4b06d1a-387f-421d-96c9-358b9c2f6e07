<template>
  <div class='repair-detail my-container'>
    <Header title="自修" @backFun="backFn"></Header>
    <page-loading v-if="loading" />
    <div class='my-content' v-else>
      <div class='list-item'>
        <div v-for="(item,index) in questionInfo" :key="index" >
          <div class='list-title'>{{item.title}}</div>
          <div class='list-value'>{{details[item.key]}}</div>
        </div>
        <div>
          <div>
            <div class='list-title'>问题内容</div>
            <div class='list-value' style="margin-top:10px">{{details.questionDescription}}</div>
          </div>
        </div>
        <div>
          <div class='list-title'>语音描述</div>
          <div class='list-value'>
            <sounds-playing :soundUrl="details.callerTapeUrl" />
          </div>
        </div>
        <div>
          <div class='list-title'>问题图片</div>
          <div class='list-value'>
            <van-image
                @click="lookImage(item)"
                v-for="(item,index) in details.attachmentUrlArr"
                :key="index"
                round
                width="5rem"
                height="5rem"
                :src="item"
              />
             <van-image-preview v-model="imagePreviewshow" :images="currentImage">
            </van-image-preview>
          </div>
        </div>
        <div>
          <div v-if="details.consumableList&&details.consumableList.length">
            <div class='list-title'>维修耗材</div>
            <div class='list-value' style="margin-top:10px">
              <div>
                <div class="list-value-item" v-for="(item,index) in details.consumableList" :key="index">
                  <span>{{item.depName}}</span>
                  <span>{{item.accOutNum}}</span>
                </div>
              </div>
             
            </div>
          </div>
        </div>
      </div>

  
     
    </div>
  </div>
</template>
<script>
export default {
  name: 'repairByMeDetail',
  data () {
    return {
      questionInfo:[
        {title:'反馈时间',key:'createDate',value:''},
        {title:'反馈人',key:'callerName',value:''},
        {title:'反馈电话',key:'sourcesPhone',value:''},
        {title:'任务点',key:'taskPointName',value:''},
      ],
      details:{},
      loading:true,
      currentImage:[],
      imagePreviewshow:false,
      type:0,
    }
  },
  mounted(){
    const {type} = this.$route.query;
    this.type=type;
  
    this.getInfo();
  },
  methods: {
    lookImage(item){
      this.currentImage=[item];
      this.imagePreviewshow=true;
    },
    // 获取详情
    getInfo(){
      const {id} = this.$route.query;
      this.axios.postContralHostUrlOne("getDetailsFixByOwn",{id},res=>{
        const {code,data,message} = res;
        this.loading=false;
        if(code==200){
          this.details=data;
          let arr =[];
          if(this.details.attachmentUrl){
            arr =this.details.attachmentUrl.split(',');
          }
          let attachmentUrlArr=[];
          if(arr.length){
            arr.forEach(item=>{
              attachmentUrlArr.push(`${__PATH.MINIOURL}${item}`)
              // attachmentUrlArr.push(`${__PATH.OSSURL}${item}`)
              // console.log('repairByMeDetail', `${__PATH.MINIOURL}${item}`);
            })
          }
          this.details.attachmentUrlArr=attachmentUrlArr;
        }
      })
    },

    // 返回
    backFn(){
      this.$router.go(-1)
    },
  },
}
</script>
<style scoped lang="scss">
@import "../../../assets/stylus/theme.scss";
.repair-detail {
  background: $main-bgColor-gray;
}
.my-container {
  height: 100vh;
  .my-content {
    height: calc(100vh - 2rem);
    overflow-y: auto;
  }
}

/deep/ .van-loading {
  height: calc(100vh - 2rem)!important;
}
.list-item {
  background: #FFF;
  >div {
    padding: 17px 16px;
    position: relative;
    display: flex;
    align-items: center;
    font-size: 15px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    .list-title {  
      color: #333333;
      flex: 1;
    }
    .list-value {      
      color: #47515F;
      flex: 3;
      word-break: break-all;
      display: flex;
      overflow-x: auto;
      .list-value-item {
        display: flex;
        span {
          margin-right: 40px;
        }
      }
    }
  }
  >div::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: 0;
    bottom: 0;
    left: 0;
    border-bottom: 1px solid #ebedf0;
    transform: scaleY(.5);
  }
}
.footer-btns {
  display: flex;
  justify-content: space-around;
  background: #FFF;
  padding: 10px 0 20px 0;
  >div {
    cursor: pointer;
    width: 42%;
    font-size: 17px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    border-radius: 2px;
    text-align: center;
    padding: 10px 0;
  }
  .btn-1 {
    color: #2D4A74;
    background: #FFFFFF;
    border: 1px solid #D8DEE7;
  }
  .btn-2 {
    color: #FFFFFF;
    background: #2D4A74;
  }
  .btn-3 {
    width: 84%;
  }
}
.sounds-info {
  width: 160px;
  height: 40px;
  border-radius: 40px;
  background: $main-bgColor;
  display: flex;
  align-items: center;
  color: #FFF;
  img {
    width: 12.8px;
    margin: 0 14px;
  }
}
/deep/ .van-image--round {
  border-radius: 2%;
  flex-shrink: 0;
  margin-right: 10px;
}
/deep/ .van-cell{
  // padding: 10px 10px;
  background-color: #F5F6FB;
  border-radius: 6px;
  margin-top: 10px;
}
.submit-info{
  background: #FFF;
}
.submit-ctn {
  margin: 0 17px;
}
/deep/ .van-radio-group {
  display: flex;
  justify-content: space-around;
  margin: 20px 0;
}
/deep/ .van-radio__icon--checked .van-icon{
  background-color: $main-bgColor;
  border-color: $main-bgColor;
}
.sounds-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  >span {
    font-size: 15px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #353535;
  }
  .sounds-all {
    width: 55%;
  }
}
.upload-text {
  font-size: 15px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #353535;
  margin: 10px 0;
}
.button-btm {
  background: #FFF;
  position: relative;
  padding: 10px 0;
}
.button-btm::after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  right: 0;
  top: 0;
  left: 0;
  border-top: 1px solid #ebedf0;
  transform: scaleY(.5);
}
/deep/ .van-button--info{
  background-color: $main-bgColor;
  border: 1px solid $main-bgColor;
}
/deep/ .van-button{
  width: 90%;
  margin-left: 5%;
}
</style>
