<template>
  <div class="repair-detail my-container"> 

    <Header title="报修" @backFun="backFn"></Header>

    <page-loading v-if="loading" />

    <div class='my-content' v-else>
      <van-steps direction="vertical" :active="0" active-icon="clock" inactive-icon="clock">
        <van-step v-for="(item,index) in detailArr" :key="index">
          <div class="step-title"  @click="showMore(index)">
            <div>
              <span class="step-statue">{{item.operationType}}</span>
              <span class="step-time">{{item.createDate}}</span>
            </div>
            <i class='iconfont'>{{item.show?'&#xe686;':'&#xe66f;'}}</i>
          </div>
          <component
            v-if='item.show'
            :is="componentObj[item.operationCode]"
            :detail="item"
            :baseInfo="createObj"
          />
        </van-step>
      </van-steps>
      <div class="repair-btn">
        <van-button round type="info" @click="toComplaint">投诉</van-button>
      </div>
    </div>

  </div>
</template>
<script>
import BaseInfo from "./components/baseInfo";
import Accepted from "./components/accepted";
import Dispatch from "./components/dispatch";
import PendingDetails from "./components/pendingDetails";
import EvaluationInfo from "./components/evaluationInfo";
import ChangeOrder from "./components/changeOrder";
import ReturnVisit from "./components/returnVisit";
import Changed from "./components/changed";
import Reply from "./components/reply";
import CancelPage from "./components/canclePage";
  export default {
      name: 'repairDetail',
      components:{BaseInfo,Accepted,Dispatch,PendingDetails,EvaluationInfo,ChangeOrder,ReturnVisit,Changed,Reply,CancelPage},
      data() {
          return {
            first:true,
            loading:true,
            componentObj:{
              '1':'base-info',//创建工单
              '2':'Accepted',//已受理
              '3':'dispatch',//已派工
              '4':'pending-details',//已挂单
              '5':'return-visit',//已回访
              '6':'evaluation-info',//已完工
              '7':'cancel-page',//已取消
              '8':'return-visit',//督促
              '9':'change-order',//已转单
              '10':'changed',//已变更
              '11':'change-order',//已转派
              '13':'reply',//回复
            },
            detailArr:[],
            createObj:{},
          }
      },
    
      mounted(){
        this.getInfo();
      },
      methods: {
        // 获取详情
        getInfo(){
          const {id} = this.$route.query;
          this.axios.postContralHostUrlOne("getTaskDetail",{taskId:id},res=>{
            const {code,data,message} = res;
            this.loading=false;
            if(code==200){
              const obj = {
                '0':'呼叫中心',
                '1':'app',
                '2':'web',
                '3':'巡检',
              }
              data.forEach(item=>{
                item.show=true;
                item.createDate=this.utils.formatDateTime(item.createDate);
                if(item.operationCode==1){
                  // 创建工单
                  this.createObj=item;
                  item.repairWorkName=item.repairWork==2?'是':'否';
                  item.callerJobNumName=item.signatureFlag==1?'***':item.callerJobNum;
                  item.appointmentDateNew=item.appointmentDate==0?'立刻':item.appointmentDate;
                  item.workSourcesName=item.workSources?obj[item.workSources]:'';
                } else if(item.operationCode==2){
                  item.localtion=item.itemType[0].localtion;
                } else if(item.operationCode==4){//挂单
                  if(item.disPlanSolutionTime==0){
                    item.disPlanSolutionTime='';
                  } else {
                    item.disPlanSolutionTime=this.utils.formatDateTime(item.disPlanSolutionTime);
                  }
                  
                } else if(item.operationCode==6){
                  item.disDegree=parseInt(item.disDegree);
                }
              })

              this.detailArr=data;
            }
          })
        },
        showMore(index){
          this.detailArr[index].show=!this.detailArr[index].show;
        },
        toComplaint(){
          const {workNum,id} = this.createObj;
          this.$router.push({
            path:'/complaint',
            query:{
              workNum,
              id,
            }
          })
        },
        // 返回
        backFn(){
          this.$router.go(-1)
        },
      },
  }
</script>
<style scoped lang="scss">
@import "../../../assets/stylus/theme.scss";
.my-container {
  height: 100vh;
  .my-content {
    height: calc(100vh - 2rem);
    overflow-y: auto;
  }
}
.step-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  >div {
    background: #ECEEF8;
    border-radius: 50px;
    padding: 8px 12px;
  }
  .step-statue {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #252525;
    margin-right: 20px;
  }
  .step-statue::before{
    width: 4px;
    height: 4px;
    background: #38C7C4;
    content: "";
    display: inline-block;
    margin: 0 4px 2px 0;
  }
  .step-time {
    font-size: 15px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #4F87FB;
    margin-right: 10px;
  }
  .iconfont {
    color: #e5e5e5;
  }
}
.list-item {
  background: #FFF;
  >div {
    padding: 17px 16px;
    position: relative;
    display: flex;
    align-items: center;
    font-size: 15px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    .list-title {  
      color: #333333;
      flex: 1;
    }
    .list-value {      
      color: #47515F;
      flex: 3;
      word-break: break-all;
      display: flex;
      overflow-x: auto;
    }
    .list-value2 {
      color: #47515F;
      padding: 20px;
      width: 70%;
      >div {
        display: flex;
        justify-content: space-between;
      }
    }
  }
  .list-column {
    flex-direction: column;
    align-items: flex-start;
    .list-value {
      margin-top: 10px;
    }
  }
  >div::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: 0;
    bottom: 0;
    left: 0;
    border-bottom: 1px solid #ebedf0;
    transform: scaleY(.5);
  }
}
.repair-detail {
  background: $main-bgColor-gray;
}
.repair-btn {
  background: #FFF;
  margin-top: 10px;
  padding: 15px;
  display: flex;
  flex-direction: row-reverse;
}
/deep/ .van-step__icon--active {
  color: $main-bgColor;
}
/deep/.van-step{
  color: $main-bgColor;
}
/deep/ .van-icon{
  font-size: 18px;
}
/deep/ .van-icon-clock::before {
  margin-top: 18px;
}
/deep/ .van-rate__icon--disabled {
  color: #FFB034;
}
/deep/ .van-button--info{
  background-color: $main-bgColor;
  border: 1px solid $main-bgColor;
}
/deep/ .van-button--round {
  border-radius: 9px;
}
/deep/ .van-button {
  height: 32px;
  padding: 0 30px;
}
</style>