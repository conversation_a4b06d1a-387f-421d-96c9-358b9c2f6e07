<template>
  <div class='repair-detail my-container'>
    <Header :title="title" @backFun="backFn"></Header>
    <page-loading v-if="loading" />
    <div class='my-content' v-else>
      <second-title title="问题反馈" style="margin-top:10px" :hasBorder="true" />
      <div class='list-item'>
        <div v-for="(item,index) in questionInfo" :key="index" >
          <div class='list-title'>{{item.title}}</div>
          <div class='list-value'>{{questionInfoObj[item.key]}}</div>
        </div>
        <div>
          <div>
            <div class='list-title'>问题描述</div>
            <div class='list-value' style="margin-top:10px">{{questionInfoObj.questionDescription}}</div>
          </div>
        </div>
        <div>
          <div class='list-title'>语音描述</div>
          <div class='list-value'>
            <sounds-playing :soundUrl="questionInfoObj.callerTapeUrl" />
          </div>
        </div>
        <div>
          <div>
            <div class='list-title'>问题图片</div>
            <div class='list-value'  style="margin-top:14px">
              <van-image
                  @click="lookImage(item)"
                  v-for="(item,index) in questionInfoObj.attachmentUrlArr"
                  :key="index"
                  round
                  width="5rem"
                  height="5rem"
                  :src="item"
                />
              <van-image-preview v-model="imagePreviewshow" :images="currentImage">
              </van-image-preview>
            </div>
          </div>
        </div>
      </div>

  
      <!-- 挂单信息 -->
      <div v-if="JSON.stringify(noticeInfoObj)!='{}'">
        <second-title title="一站式处理-已通知" style="margin-top:10px" :hasBorder="true" />
        <div class='list-item'>
          <div v-for="(item,index) in noticeInfo" :key="index" >
            <div class='list-title'>{{item.title}}</div>
            <div class='list-value'>{{noticeInfoObj[item.key]}}</div>
          </div>
          <div>
          <div style="width:100%">
            <div class='list-title'>通知说明</div>
            <!-- <div class='list-value'> -->
              <!-- <van-field
                :disabled="type!=0"
                v-model="noticeInfoObj.questionDescription"
                rows="3"
                autosize
                type="textarea"
                maxlength="200"
                placeholder="请输入"
                show-word-limit
              /> -->
            <!-- </div> -->
            <div class='list-value' style="margin-top:10px">{{noticeInfoObj.questionDescription}}</div>
          </div>
          
        </div>
        </div>
      </div>


      <div class="submit-info" v-if="type==0">
        <second-title title="整改验收" style="margin-top:10px" :hasBorder="true" />
        <van-radio-group v-model="radio">
          <van-radio name="1">合格</van-radio>
          <van-radio name="0">不合格</van-radio>
        </van-radio-group>
        <div class="submit-ctn">
          <van-field
            v-model="questionDescription"
            rows="3"
            autosize
            type="textarea"
            maxlength="200"
            placeholder="请输入"
            show-word-limit
          />
          <div class="sounds-item">
            <span>语音输入</span>
            <sounds-recording @getRecord="getRecord" /> 
          </div>
          <div>
            <div class="upload-text">上传图片</div>
            <van-uploader 
            :max-size="maxSize *1024 * 1024"
            @oversize="onOversize" 
            accept="image/*" 
            :disabled="fileMessage=='上传中'" 
            v-model="fileList"
            max-count='3' 
            :after-read="afterImgRead"  />
          </div>
        </div>
      </div>

      <div v-else-if="JSON.stringify(checkInfoObj)!='{}'">
        <second-title :title="subtitle" style="margin-top:10px" :hasBorder="true" />
        <div class='list-item'>
          <div v-for="(item,index) in checkInfo" :key="index" >
            <div class='list-title'>{{item.title}}</div>
            <div class='list-value'>{{checkInfoObj[item.key]}}</div>
          </div>
          <div>
            <div>
              <div class='list-title'>问题描述</div>
              <div class='list-value' style="margin-top:10px">{{checkInfoObj.questionDescription}}</div>
            </div>
          </div>
          <div>
            <div class='list-title'>语音描述</div>
            <div class='list-value'>
              <sounds-playing :soundUrl="checkInfoObj.acceptanceVoiceUrl" />
            </div>
          </div>
          <div>
            <div class='list-title'>问题图片</div>
            <div class='list-value'>
              <van-image
                  @click="lookImage(item)"
                  v-for="(item,index) in checkInfoObj.acceptancePictureUrlArr"
                  :key="index"
                  round
                  width="5rem"
                  height="5rem"
                  :src="item"
                />
              <van-image-preview v-model="imagePreviewshow" :images="currentImage">
              </van-image-preview>
            </div>
          </div>
        </div>
      </div>

      <div class="button-btm" v-if="type==0">
        <van-button :loading="buttonLoading" type="info" loading-text="正在提交..." @click="onSubmit">提交</van-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'reformDetail',
  data () {
    return {
      questionInfo:[
        {title:'反馈时间',key:'createDate',value:'2021-03-18 12:00:00'},
        {title:'反馈人',key:'createByName',value:'王小明'},
        {title:'反馈电话',key:'createByPhone',value:'13777777777'},
        {title:'联系人',key:'callerName',value:'王小明'},
        {title:'联系电话',key:'sourcesPhone',value:'13777777777'},
        // {title:'问题科室',key:'sourcesDeptName',value:'急诊101'},
        // {title:'问题地点',key:'locationName',value:'呼吸科907室'},
        // {title:'地点电话',key:'locationPhone',value:'907室'},
      ],
      questionInfoObj:{},
      noticeInfo:[
        {title:'通知时间',key:'createDate',value:'2021-03-18 12:00:00'},
        {title:'调度员',key:'createByName',value:'2021-03-18 12:00:00'},
        {title:'职工工号',key:'createByJobnum',value:'2021-03-18 12:00:00'},
      ],
      noticeInfoObj:{},
      checkInfo:[
        {title:'验收时间',key:'createDate',value:'2021-03-18 12:00:00'},
        {title:'验收人',key:'createByName',value:'2021-03-18 12:00:00'},
        {title:'验收电话',key:'createByPhone',value:'2021-03-18 12:00:00'},
      ],
      checkInfoObj:{},
      details:{},
      loading:true,
      currentImage:[],
      imagePreviewshow:false,
      questionDescription:'',
      radio:'1',
      maxSize:5,//
      fileMessage:'',
      fileName:'',
      fileList:[],
      buttonLoading:false,
      title:'',
      subtitle:'',
      type:0,
      recordingInfo:''
    }
  },
  mounted(){
    const {type} = this.$route.query;
    this.type=type;
    const obj = {
      '0':'待验收',
      '1':'合格',
      '2':'不合格',
    }
    this.title="整改-"+obj[type];
    this.subtitle="整改验收-"+obj[type];
    let loginInfo= JSON.parse(localStorage.getItem("loginInfo"));
    this.loginInfo=loginInfo;
    this.getInfo();
  },
  methods: {
    getRecord(info){
      this.recordingInfo = info;
    },
    lookImage(item){
      this.currentImage=[item];
      this.imagePreviewshow=true;
    },
    // 获取详情
    getInfo(){
      const {id} = this.$route.query;
      this.axios.postContralHostUrlOne("getDetailsAbarbeitung",{id},res=>{
        const {code,data,message} = res;
        this.loading=false;
        if(code==200){
          const {records} = data;
          records.forEach(item=>{
            if(item.operationCode=='14'){
              item.callerName=data.callerName;
              item.sourcesPhone=data.sourcesPhone;
              this.questionInfoObj = item;
            } else if (item.operationCode=='16'||item.operationCode=='17'){
              this.checkInfoObj=item;
            } else if(item.operationCode=='15'){
              this.noticeInfoObj=item;
            }
          })

          let arr =[];
          if(this.questionInfoObj.attachmentUrl){
            arr =this.questionInfoObj.attachmentUrl.split(',');
          }
          let attachmentUrlArr=[];
          if(arr.length){
            arr.forEach(item=>{
              attachmentUrlArr.push(`${__PATH.MINIOURL}${item}`)
              console.log('reformDetail', `${__PATH.MINIOURL}${item}`);
              // attachmentUrlArr.push(`${__PATH.OSSURL}${item}`)
            })
          }
          this.questionInfoObj.attachmentUrlArr=attachmentUrlArr;

          let arr2 =[];
          if(this.checkInfoObj.acceptancePictureUrl){
            arr2 =this.checkInfoObj.acceptancePictureUrl.split(',');
          }
          let acceptancePictureUrlArr=[];
          if(arr2.length){
            arr2.forEach(item=>{
              acceptancePictureUrlArr.push(`${__PATH.MINIOURL}${item}`)
              console.log('reformDetail', `${__PATH.MINIOURL}${item}`);
              // acceptancePictureUrlArr.push(`${__PATH.OSSURL}${item}`)
            })
          }
          this.checkInfoObj.acceptancePictureUrlArr=acceptancePictureUrlArr;
        }
      })
    },
    onSubmit(){
      const urls = this.fileList.length?this.fileList.map(item=>{return item.url}):[]
      const {id} = this.$route.query;
      const {radio,questionDescription,recordingInfo,fileMessage} =this;
      const {userId,name,phone,sysIdentity} = this.loginInfo;
      if(fileMessage=='上传中')return this.$toast.fail('附件正在上传，请稍后...');
      let params = {
        createByCode:userId,
        createByName:name,
        createByJobnum:'',
        createByJob:sysIdentity,
        createByPhone:phone,
        
        id,
        isQualified:radio,
        acceptanceVoiceUrl:recordingInfo,
        acceptancePictureUrl:urls.join(','),
        questionDescription,
      };
        // params.files={
        //   acceptanceVoiceUrl:recordingInfo
        // }
      this.axios.postContralHostUrlOne("abarbeitungAcceptanceCheck",params,res=>{
        const {code,data,message} = res;
        this.loading=false;
        if(code==200){
          this.$toast.success(message);
          this.$router.go(-1);
        }
      })
    },
    onOversize(){
      this.$toast.fail(`文件大小不能超过${this.maxSize}M`);
    },
    // 图片上传成功回调
    afterImgRead(file){
      file.status = 'uploading';
      file.message = '上传中...';
      this.fileMessage = '上传中';
      this.handleUploadImg(file);
    },
    handleUploadImg(file){
      let params = {
        file:file.file,
      }
      
      this.axios.postContralHostBase("uploadFile",params,res=>{
      // this.axios.postContralHostUrlOne("uploadFileToOSS",params,res=>{
        const {code,data,message} = res;
        if(code==200){
          file.status = 'success';
          file.message = '上传成功';
          this.fileMessage = '上传成功';
          this.fileList[this.fileList.length-1].url=data.fileKey;
          
        }
      })
    },
    // 取消
    doCancel(){
      this.backFn();
    },
    // 返回
    backFn(){
      this.$router.go(-1)
    },
  },
}
</script>
<style scoped lang="scss">
@import "../../../assets/stylus/theme.scss";
.repair-detail {
  background: $main-bgColor-gray;
}
.my-container {
  height: 100vh;
  .my-content {
    height: calc(100vh - 2rem);
    overflow-y: auto;
  }
}

/deep/ .van-loading {
  height: calc(100vh - 2rem)!important;
}

.list-item {
  background: #FFF;
  >div {
    padding: 17px 16px;
    position: relative;
    display: flex;
    align-items: center;
    font-size: 15px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    .list-title {  
      color: #333333;
      flex: 1;
    }
    .list-value {      
      color: #47515F;
      flex: 3;
      word-break: break-all;
      display: flex;
      // overflow-x: auto;
      flex-wrap:wrap;
    }
    .list-value2 {
      color: #47515F;
      flex: 3;
      >div{
        display: flex;
        justify-content: space-between;
      }
      .dept-name {
        display: inline-block;
        width:100px;
        vertical-align: bottom;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  >div::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: 0;
    bottom: 0;
    left: 0;
    border-bottom: 1px solid #ebedf0;
    transform: scaleY(.5);
  }
}
.footer-btns {
  display: flex;
  justify-content: space-around;
  background: #FFF;
  padding: 10px 0 20px 0;
  >div {
    cursor: pointer;
    width: 42%;
    font-size: 17px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    border-radius: 2px;
    text-align: center;
    padding: 10px 0;
  }
  .btn-1 {
    color: #2D4A74;
    background: #FFFFFF;
    border: 1px solid #D8DEE7;
  }
  .btn-2 {
    color: #FFFFFF;
    background: #2D4A74;
  }
  .btn-3 {
    width: 84%;
  }
}
.sounds-info {
  width: 160px;
  height: 40px;
  border-radius: 40px;
  background: $main-bgColor;
  display: flex;
  align-items: center;
  color: #FFF;
  img {
    width: 12.8px;
    margin: 0 14px;
  }
}
/deep/ .van-image--round {
  border-radius: 2%;
  flex-shrink: 0;
  margin:10px 10px 0 0;
}
/deep/ .van-cell{
  // padding: 10px 10px;
  background-color: #F5F6FB;
  border-radius: 6px;
  margin-top: 10px;
}
.submit-info{
  background: #FFF;
}
.submit-ctn {
  margin: 0 17px;
}
/deep/ .van-radio-group {
  display: flex;
  justify-content: space-around;
  margin: 20px 0;
}
/deep/ .van-radio__icon--checked .van-icon{
  background-color: $main-bgColor;
  border-color: $main-bgColor;
}
.sounds-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  >span {
    font-size: 15px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #353535;
  }
  .sounds-all {
    width: 60%;
  }
}
.upload-text {
  font-size: 15px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #353535;
  margin: 10px 0;
}
.button-btm {
  background: #FFF;
  position: relative;
  padding: 10px 0;
}
.button-btm::after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  right: 0;
  top: 0;
  left: 0;
  border-top: 1px solid #ebedf0;
  transform: scaleY(.5);
}
/deep/ .van-button--info{
  background-color: $main-bgColor;
  border: 1px solid $main-bgColor;
}
/deep/ .van-button{
  width: 90%;
  margin-left: 5%;
}
/deep/ .van-uploader__loading{
  height: 22px !important;
}
</style>
