<template>
  <div class='team-list main-bg'>
    <Header title="反馈记录" @backFun="backFn"></Header>
    <van-tabs v-model="active" type="card">
      <van-tab title="整改" >
        <ItemList type='1' v-if='active==0'  />
      </van-tab>
      <van-tab title="自修">
        <ItemList type='2' v-if='active==1' />
      </van-tab>
      <van-tab title="报修">
        <ItemList type='3' v-if='active==2' />
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import ItemList from './itemList'
export default {
  name: 'team-list',
  components:{
    ItemList
  },
  data () {
    return {
      active:0,
    }
  },
  mounted(){
  },
  methods: {
    backFn(){
      // api.closeWin();
      this.$router.go(-1)
    },
  },
}
</script>
<style scoped lang="scss">
@import "../../../assets/stylus/theme";

.team-list {
  background: $main-bgColor-gray;
}

/deep/ .van-tabs__nav--card{
  border: 1px solid $main-bgColor !important;
  margin: 10px 6.5%;
  border-radius:20px;
  height: 34px;
  overflow-x:hidden;
}
/deep/ .van-tabs__nav--card .van-tab.van-tab--active{
  background-color: $main-bgColor;
  color: #FFF;
}
/deep/ .van-tabs__nav--card .van-tab{
  color: $main-bgColor;
  border-right: 1px solid $main-bgColor;
}
/deep/ .van-tabs__nav--card .van-tab:last-child{
  border:none;
}
/deep/ .van-tabs--card>.van-tabs__wrap {
  height: 60px;
  background: #FFF;
}
/deep/ .van-tabs {
  // height: calc(100vh - 2rem)
}
/deep/ .van-tabs__content {
  // height: calc(100vh - 2rem - 60px);
  // overflow-y: hidden;
}
</style>
