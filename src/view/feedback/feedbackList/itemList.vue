<template>
  <div>
    <div class="item-top">
      <div class="item-top-left">
        <span>筛选</span>
        <!-- <i class='iconfont'>&#xe628;</i> -->
      </div>
      <div class="item-top-right" @click="toCalendar">
        <div>{{start}}</div>
        <span></span>
        <div>{{end}}</div>
      </div>
    </div>

    <div :class="[{'currentTab2':type==2},'item-bottom']">
      <van-tabs v-model="active" @click="tabClick" >
        <van-tab :title="item.title" v-for="(item,index) in dataList" :key="index" >
          <div class="list-auto">
            <page-loading v-if="loading&&paginationData.currentPage==1" />

            <div v-else-if="!item.list||!item.list.length">
              <van-empty :image="noDataImg" description="暂无数据" />
            </div>  

            <van-pull-refresh v-model="refreshing" @refresh="onRefresh" v-else>
              <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" :immediate-check="false" :offset="5">
                <div v-for="(item2,index2) in item.list" :key="index2" class="pull-list" @click="toDetail(item2,index)">
                  <div class="pull-list-first">
                    <div class="list-name">
                      <div class="list-title">反馈时间&emsp;</div>
                      <div class="list-text">{{item2.createDate}}</div>
                    </div>
                    <span class="list-status">{{index==0&&type==1?'待验收':item2.flowType}}</span>
                  </div>
                  <!-- <div>
                    <div class="list-title">问题区域</div>
                    <div class="list-text">{{item2.locationArea}}</div>
                  </div> -->
                  <!-- <div>
                    <div class="list-title">问题地点</div>
                    <div class="list-text">{{item2.locationName}}</div>
                  </div> -->
                   <div>
                    <div class="list-title_task">任务点名称</div>
                    <div class="list-text-task">{{item2.taskPointName}}</div>
                  </div>
                </div>
              </van-list>
            </van-pull-refresh>
          </div>

        </van-tab>
      </van-tabs>
    </div>


    <van-calendar :min-date="minDate" v-model="show" type="range" @confirm="onConfirm" />
  </div>
</template>

<script>
export default {
  name: 'Item-list',
  props:['type'],
  components:{
  },
  data () {
    return {
      active:0,
      show:false,
      start:'2021-01-01',
      end:this.utils.formatDate(new Date()),
      minDate:new Date(2000,0,1),
      dataList:[
        {title:'待验收',labelType:'wait',list:[]},
        {title:'合格',labelType:'qualified',list:[]},
        {title:'不合格',labelType:'unqualified',list:[]},
      ],
      dataList2:[
        {title:'未派工',labelType:'noDispatching',list:[]},
        {title:'已派工',labelType:'dispatching',list:[]},
        {title:'已挂单',labelType:'pending',list:[]},
        {title:'已结束',labelType:'finished',list:[]},
      ],
      refreshing:false,
      loading:true,
      finished:false,
      paginationData: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      noDataImg: require("@/assets/images/empty.png"), //暂无数据图片
    }
  },

  mounted(){
    // APP登录信息存储.
    apiready =  () =>{
      var userInfo = api.getPrefs({
        sync: true,
        key: 'userInfo'
      });

      userInfo = JSON.parse(userInfo)
      if (userInfo.id) {
        localStorage.setItem('loginInfo', JSON.stringify(userInfo));
      }
    }
    setTimeout(()=>{
      this.getList()
    },200)
    if(this.type==3){
      this.dataList=this.dataList2;
    }else if (this.type==2){
      this.dataList=[{title:'',labelType:'',list:[]}];
    }
    
  },
  methods: {
    toCalendar(){
      this.show = true;
    },
    toDetail(item2,type){
      const obj ={
        '1':'/reformDetail',
        '2':'/repairByMeDetail',
        '3':'/repairDetail',
      }
      this.$router.push({
        path:obj[this.type],
        query: {
          id:item2.id,
          type
        }
      });
    },
    onConfirm(date){
      const [start, end] = date;
      this.show = false;
      this.start = this.utils.formatDate(start);
      this.end = this.utils.formatDate(end);
      this.onRefresh();
    },
    // 加载更多
    onLoad(){
      this.paginationData.currentPage++;
      this.getList();
    },
    // 下拉刷新
    onRefresh(){
      this.paginationData.currentPage = 1;
      // 清空列表数据
      this.finished = false;
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.getList();
    },
    getList(){
      const {type,paginationData,start,end,active} =this;
      const {pageSize,currentPage} = paginationData;
      const obj= {
        '1':'9',
        '2':'8',
        '3':'10',
      }
      // staffRole: 3(组长) 0（工人)
      // teamId  staffId
      let loginInfo = localStorage.getItem("loginInfo");
      loginInfo=loginInfo?JSON.parse(loginInfo):'';
      const {workTeamId,userId,id,staffRole} = loginInfo;
      
      let params = {
        workTypeCode:obj[type],
        beginTime:start,
        endTime:end,
        labelType:this.dataList[active].labelType,
        pageSize,
        currentPage,
        unionSel:'',
        userId,
        staffRole,
        type:loginInfo.type,
        staffId:id
      }
      
      if(staffRole=='3'){//组长
        params.teamId=workTeamId;
      } 
      this.axios.postContralHostUrlOne("getFeedbackList",params,res=>{
        const {code,data,message} = res;
        this.loading=false;
        if(code==200){
          // data.list&&data.list.length&&data.list.forEach(item=>{
          //   let arr = item.locationName.split(',');
          //   if(!arr.length)return;
          //   item.locationArea=arr[0];
          //   item.locationPoint=arr[1];
          // })
          if(this.paginationData.currentPage == 1)this.dataList[active].list=[];
          const list = this.dataList[active].list;
          this.dataList[active].list=[...list,...data.list];
          this.refreshing = false; 
          if (this.dataList[active].list.length >= data.total) {
            this.finished = true;
          }
        }
      })
    },
    tabClick(){
      this.paginationData.currentPage=1;
      this.getList()
    },
  },
}
</script>
<style scoped lang="scss">
@import "../../../assets/stylus/theme";
.item-top {
  border-top: 1px solid #ebedf0;
  display: flex;
  padding: 17px 15px;
  justify-content: space-between;
  background: #FFF;
}
.item-top-left {
  font-size: 15px;
  font-family: PingFangSC;
  font-weight: 400;
  color: #999999;
  span {
    padding-right: 10px;
    position: relative;
    // border-right: 2px solid #999999;
  }
  span::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: 0;
    top: -1px;
    height: 120%;
    // left: 0;
    border-right: 1px solid #999999;
    transform: scaleY(.5);
  }
  // i {
  //   font-size: 11px;
  // }
}
.item-top-right {
  display: flex;
  align-items: center;
  >div {
    font-size: 17px;
    font-family: PingFangSC;
    font-weight: 400;
    color: #333333;
  }
  span {
    margin: 0px 20px;
    display: inline-block;
    width: 10px;
    height: 1px;
    font-size: 34px;
    font-family: PingFangSC;
    font-weight: 400;
    background: #999999;
    line-height: 70px;
  }
}
.item-bottom {
  // height: calc(100vh - 10px - 58px - 60px);
  margin-top: 10px;
  background: #FFF;
}
.pull-list {
  padding-top: 10px;
  >div {
    padding: 0 18px;
    display: flex;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 500;
    line-height: 40px;
    .list-title {
      color: #353535;
      margin-right: 20px;
      width: 32%;
    }
    .list-title_task {
      color: #353535;
      margin-right: 20px;
      width: 66%;
    }
    .list-text {
      color: #888888;
    }
    .list-text-task {
      color: #888888;
      width: 176%;
    }
  }
  position: relative;
}
.pull-list-first {
  justify-content: space-between;
  >div {
    display: flex;
  }
}
.list-status {
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #38C7C4;
  width: 20%;
}
.list-name{
  width: 80%;
}
.pull-list::after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  right: 0;
  top: 0;
  left: 0;
  border-top: 1px solid #ebedf0;
  transform: scaleY(.5);
}
/deep/ .van-tabs__line {
  background-color: $main-bgColor;
}
/deep/ .van-tab--active {
  color: $main-bgColor;
}
 /deep/ .van-calendar__day--start, .van-calendar__day--end, .van-calendar__day--end, .van-calendar__day--multiple-middle, .van-calendar__day--multiple-selected, .van-calendar__day--start, .van-calendar__day--start-end {
    background-color: $main-bgColor;
}
/deep/ .van-calendar__day--middle {
    color: $main-bgColor;
}
/deep/ .van-calendar__day--end {
    background-color: $main-bgColor;
}
/deep/ .van-button--danger     {
  background-color: $main-bgColor;
  border: 1px solid $main-bgColor;
}
/deep/ .van-tabs{
  // height: calc(100vh - 10px - 58px - 60px);
}
// /deep/ .van-tabs__content {
.list-auto {
  height: calc(100vh - 54px - 58px - 60px - 40px);
  overflow-y: auto;
} 
/deep/ .van-tab__pane {
  height: 100%;
}
.currentTab2 /deep/ .van-tabs--line .van-tabs__wrap {
  height: 0 !important;
}
</style>
