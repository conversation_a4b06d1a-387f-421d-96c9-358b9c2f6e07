<template>
  <div class="replay-info">
    {{detail.feedbackExplain}}
  </div>
    
</template>

<script>
export default {
  name: 'pending-details',
  props:['detail',"baseInfo"],
  components:{
  },
  data () {
    return {
     
    }
  },
  computed:{
  
  },
  mounted(){
    this.init();
  },
  methods: {
    init(){
     
      
    }
  },
}
</script>
<style scoped lang="scss">
@import "../../../../assets/stylus/theme";
.replay-info {
  padding: 17px 16px;
  align-items: center;
  font-size: 15px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
}
</style>
