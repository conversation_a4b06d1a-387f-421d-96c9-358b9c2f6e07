<template>
  <div class='list-item'>
    <div v-for="(item,index) in createList" :key="index" >
      <div class='list-title'>{{item.title}}</div>
      <div class='list-value'>{{detail[item.key]||''}}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'base-info',
  props:['detail'],
  components:{
  },
  data () {
    return {
      createList:[
        {title:'取消理由',key:'cancelReasonId'},
        {title:'取消说明',key:'cancelExplain'},
      ],
    }
  },
  mounted(){
   
  },
  methods: {

  },
}
</script>
<style scoped lang="scss">
@import "../../../../assets/stylus/theme";
.list-item {
  background: #FFF;
  >div {
    padding: 17px 16px;
    position: relative;
    display: flex;
    align-items: center;
    font-size: 15px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    .list-title {  
      color: #333333;
      flex: 1;
    }
    .list-value {      
      color: #47515F;
      flex: 3;
      word-break: break-all;
      display: flex;
      overflow-x: auto;
    }
    .list-value2 {
      color: #47515F;
      padding: 20px;
      width: 70%;
      >div {
        display: flex;
        justify-content: space-between;
      }
    }
  }
  .list-column {
    flex-direction: column;
    align-items: flex-start;
    .list-value {
      margin-top: 10px;
    }
  }
  >div::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: 0;
    bottom: 0;
    left: 0;
    border-bottom: 1px solid #ebedf0;
    transform: scaleY(.5);
  }
}
</style>
