<template>  
  <div class='list-item'>
    <div class="list-column" >
      <div class='list-title'>完工说明</div>
      <div class='list-value'>{{detail.disFinishRemark}}</div>
    </div>
    <div class="list-column" >
      <div class='list-title'>耗材实际使用</div>
      <div class='list-value2'>
        <div v-for="(item,index) in detail.actual" :key="index">
          <span>{{item.depThreeTypeName}}</span>
          <span>{{item.num}}</span>
        </div>
      </div>
    </div>
    <div v-if="detail.taskMalfunctionList.length"  class="list-other-all">
      <div v-for="(item,index) in detail.taskMalfunctionList" :key="index">
        <div class="list-other">
          <div class='list-title'>故障原因</div>
          <div class='list-value'>{{item.reasonName}}</div>
        </div>
        <div class="list-other list-other2">
          <div class='list-title'>处理方法</div>
          <div class='list-value'>{{item.methodName}}</div>
        </div>
      </div>
    </div>
    
   
    <div class="list-normal">
      <div class='list-title'>总服务费</div>
      <div class='list-value'>{{detail.completePrice}}</div>
    </div>
    <div class="list-column" >
      <div class='list-title'>工单附件</div>
      <div class="list-value">
         <van-image
          @click="lookImage(item)"
          v-for="(item,index) in detail.disAttachmentUrl"
          :key="index"
          round
          width="4.4rem"
          height="4.4rem"
          :src="item"
        />
        <van-image-preview v-model="imagePreviewshow" :images="currentImage">
        </van-image-preview>
      </div>
     
    </div>
    <div class="list-normal">
      <div class='list-title'>满意度评价</div>
      <div class="list-degree">
        <van-rate v-model="detail.disDegree" disabled />
        <span>{{detail.disDegreeText}}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'evaluation-info',
  props:['detail'],
  components:{
  },
  data () {
    return {
      dispatchList:[
        {title:'服务人员',key:'designatePersonName',value:'2021-03-18 12:00:00'},
        {title:'人员电话',key:'designatePersonPhone',value:'2021-03-18 12:00:00'},
      ],
      textObj:{
        1:'非常差',
        2:'差',
        3:'一般',
        4:'满意',
        5:'非常满意',
      },
      currentImage:[],
      imagePreviewshow:false,
    }
  },
  mounted(){
    this.detail.disDegreeText=this.textObj[this.detail.disDegree];
  },
  methods: {
    lookImage(item){
      this.currentImage=[item];
      this.imagePreviewshow=true;
    },
  },
}
</script>
<style scoped lang="scss">
@import "../../../../assets/stylus/theme";
.list-item {
  background: #FFF;

  >div,.list-other {
    padding: 17px 16px;
    position: relative;
    display: flex;
    align-items: center;
    font-size: 15px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    .list-title {  
      color: #333333;
      flex: 1;
    }
    .list-value {      
      color: #47515F;
      flex: 3;
      word-break: break-all;
      display: flex;
      // overflow-x: auto;
      flex-wrap:wrap;
      
    }
    .list-value2 {
      color: #47515F;
      padding: 0px 20px;
      width: 70%;
      >div {
        display: flex;
        justify-content: space-between;
        line-height: 30px;
      }
    }
  }
  .list-other-all {
    display: block;
    .list-other,.list-other2 {
      padding: 6px 16px;
    }
  }
  .list-other2 {
    margin-left: 20px;
  }
  .list-column {
    flex-direction: column;
    align-items: flex-start;
    .list-value {
      margin-top: 10px;
    }
  }
  >div::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: 0;
    bottom: 0;
    left: 0;
    border-bottom: 1px solid #ebedf0;
    transform: scaleY(.5);
  }
}
.list-degree {
  display: flex;
  align-items: center;
  span {
    color: #47515F;
    margin-left: 6px;
  }
}
/deep/ .van-image--round {
  border-radius: 2%;
  margin:10px 10px 0 0;
}
</style>
