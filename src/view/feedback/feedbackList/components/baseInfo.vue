<template>
  <div class='list-item'>
    <div v-for="(item,index) in createList" :key="index" :class="{'list-column':index==8}" >
      <div class='list-title'>{{item.title}}</div>
      <div class='list-value'>{{detail[item.key]||''}}</div>
    </div>
     <div>
        <div class='list-title'>语音描述</div>
        <div class='list-value'>
          <sounds-playing :soundUrl="detail.callerTapeUrl" />
        </div>
      </div>
    <div class="list-column" >
      <div class='list-title'>工单附件</div>
      <div class="list-value">
         <van-image
          @click="lookImage(item)"
          v-for="(item,index) in detail.attachment"
          :key="index"
          round
          width="4.4rem"
          height="4.4rem"
          :src="item"
        />
        <van-image-preview v-model="imagePreviewshow" :images="currentImage">
        </van-image-preview>
      </div>
     
    </div>
  </div>
</template>

<script>
export default {
  name: 'base-info',
  props:['detail'],
  components:{
  },
  data () {
    return {
      createList:[
        {title:'工单号',key:'workNum'},
        {title:'工单类型',key:'workTypeName'},
        {title:'所属科室',key:'sourcesDeptName'},
        {title:'申报来源',key:'workSourcesName'},
        {title:'紧急程度',key:'urgencyDegree'},
        {title:'联系人',key:'callerName'},
        {title:'电话',key:'sourcesPhone'},
        {title:'职工工号',key:'callerJobNumName'},
        {title:'问题描述',key:'questionDescription'},
        {title:'服务时间',key:'appointmentDateNew'},
        {title:'来电号码',key:'needPhone'},
        {title:'返修工单',key:'repairWorkName'},
        {title:'申报属性',key:'typeSources'},
      ],
      currentImage:[],
      imagePreviewshow:false,
    }
  },
  mounted(){
   
  },
  methods: {
    lookImage(item){
      this.currentImage=[item];
      this.imagePreviewshow=true;
    },
  },
}
</script>
<style scoped lang="scss">
@import "../../../../assets/stylus/theme";
.list-item {
  background: #FFF;
  >div {
    padding: 17px 16px;
    position: relative;
    display: flex;
    align-items: center;
    font-size: 15px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    .list-title {  
      color: #333333;
      flex: 1;
    }
    .list-value {      
      color: #47515F;
      flex: 3;
      word-break: break-all;
      display: flex;
      overflow-x: auto;
      flex-wrap:wrap;
    }
    .list-value2 {
      color: #47515F;
      padding: 20px;
      width: 70%;
      >div {
        display: flex;
        justify-content: space-between;
      }
    }
  }
  .list-column {
    flex-direction: column;
    align-items: flex-start;
    .list-value {
      margin-top: 10px;
    }
  }
  >div::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: 0;
    bottom: 0;
    left: 0;
    border-bottom: 1px solid #ebedf0;
    transform: scaleY(.5);
  }
}
/deep/ .van-image--round {
  border-radius: 2%;
  margin:10px 10px 0 0;
}
</style>
