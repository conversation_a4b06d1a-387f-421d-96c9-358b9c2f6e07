<template>
  <div>
    <div class='list-item' v-if="baseInfo.workTypeCode == 3 || baseInfo.template == 3">
      <div v-for="(item,index) in list1" :key="index" :class="{'list-column':index==6}" >
        <div class='list-title'>{{item.title}}</div>
        <div class='list-value'>{{detail[item.key]}}</div>
      </div>
    </div>
    <div class='list-item' v-else>
      <div v-for="(item,index) in list2" :key="index" :class="{'list-column':index==5}" >
        <div class='list-title'>{{item.title}}</div>
        <div class='list-value'>{{detail[item.key]}}</div>
      </div>
    </div>
  </div>
    
</template>

<script>
export default {
  name: 'pending-details',
  props:['detail',"baseInfo"],
  components:{
  },
  data () {
    return {
      list1:[
        {title:'服务起点',key:'transportStartLocal'},
        {title:'起点科室',key:'transportStartLocalOffice'},
        {title:'服务终点',key:'transportEndLocal'},
        {title:'终点科室',key:'transportEndLocalOffice'},
        {title:'服务部门',key:'designateDeptName'},
        {title:'服务事项',key:'transportName'},
        {title:'申报描述',key:'questionDescription'},
      ],
      list2:[
        {title:'服务地点',key:'localtion'},
        {title:'服务科室',key:'sourcesDeptName'},
        {title:'服务部门',key:'designateDeptName'},
        {title:'服务事项',key:'transportNameNew'},
        {title:'数量',key:'customtransportNum'},
        {title:'申报描述',key:'questionDescription'},
      
      ],
    }
  },
  computed:{
    serviceMatters() {
      let matter = "";
      let itemType = this.detail.itemType&&this.detail.itemType[0];
      if (itemType.itemServiceCode) {
        matter =
          itemType.itemTypeName +
          "-" +
          itemType.itemDetailName +
          "-" +
          itemType.itemServiceName;
      } else if (itemType.itemDetailCode) {
        matter = itemType.itemTypeName + "-" + itemType.itemDetailName;
      } else {
        matter = itemType.itemTypeName;
      }
      return matter;
    }
  },
  mounted(){
    this.init();
  },
  methods: {
    init(){
      const {detail,baseInfo} = this;
      const {workTypeCode}=baseInfo;
      this.detail.localtion=detail.itemType&&detail.itemType[0].localtion;
      if(workTypeCode == 1 || workTypeCode == 2 || workTypeCode.length>2){
        this.detail.transportNameNew=this.serviceMatters();
      } else {
        this.detail.transportNameNew=detail.transportName;
      }
      
    }
  },
}
</script>
<style scoped lang="scss">
@import "../../../../assets/stylus/theme";
.list-item {
  background: #FFF;
  >div {
    padding: 17px 16px;
    position: relative;
    display: flex;
    align-items: center;
    font-size: 15px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    .list-title {  
      color: #333333;
      flex: 1;
    }
    .list-value {      
      color: #47515F;
      flex: 3;
      word-break: break-all;
      display: flex;
      overflow-x: auto;
    }
    .list-value2 {
      color: #47515F;
      padding: 20px;
      width: 70%;
      >div {
        display: flex;
        justify-content: space-between;
      }
    }
  }
  .list-column {
    flex-direction: column;
    align-items: flex-start;
    .list-value {
      margin-top: 10px;
    }
  }
  >div::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: 0;
    bottom: 0;
    left: 0;
    border-bottom: 1px solid #ebedf0;
    transform: scaleY(.5);
  }
}
</style>
