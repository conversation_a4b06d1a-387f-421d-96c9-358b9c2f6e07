<template>
  <div class="container">
    <Header title="巡检内容" @backFun="goBack"> </Header>
    <!-- <top-nav title="巡检内容"></top-nav> -->
    <div class="header">
      <div class="item">
        <span>任务名称：</span>
        <span>{{ taskData.taskName }}</span>
      </div>
      <div class="item">
        <span>任务点名称：</span>
        <span v-if="from == 'taskdetail'">{{
          taskData.taskPointRelease.taskPointName
        }}</span>
        <span v-else>{{ taskData.taskPointRelease.taskPointName }}</span>
      </div>
      <div class="item">
        <span>当前任务时间：</span>
        <span
          >{{ taskData.taskStartTime | dateTrans(taskData.cycleType) }}至{{
            taskData.taskEndTime | dateTrans(taskData.cycleType)
          }}</span
        >
      </div>
    </div>
    <div class="split-line"></div>
    <div class="main">
      <div class="title">
        <span>巡检任务书</span>
      </div>
      <van-collapse v-model="activeNames">
        <van-collapse-item
          title=""
          :name="index"
          v-for="(item, index) in projList.maintainProjectdetailsReleaseList"
          :key="item.id"
        >
          <template #title>
            <van-icon name="notes-o" color="#29bebc" size="16" />
            <span class="project">巡检项目</span>
            <span>{{ item.detailName }}</span>
          </template>
          <div
            v-for="(item2, index) in item.maintainProjectdetailsTermReleaseList"
            :key="item2.id"
          >
            <div class="gist type1" v-if="item2.isNum == '1'">
              <span
                >{{ index + 1 }}. 巡检要点：{{
                  item2.content ? item2.content : ""
                }}</span
              >
            </div>
            <div class="gist type2" v-if="item2.isNum == '2'">
              <span
                >{{ index + 1 }}. 巡检要点：{{
                  item2.content ? item2.content : ""
                }}</span
              >
              <van-field
                v-model="item2.value"
                label=""
                rows="1"
                type="textarea"
                placeholder="请输入"
                @blur="checkField(item2)"
                :error-message="item2.error"
                maxlength="50"
                autosize
              />
            </div>
            <div class="gist type3" v-if="item2.isNum == '0'">
              <span
                >{{ index + 1 }}. 巡检要点：{{
                  item2.content
                    ? item2.content +
                      "(" +
                      item2.rangeStart +
                      "-" +
                      item2.rangeEnd +
                      item2.einheitName +
                      ")"
                    : ""
                }}</span
              >
              <van-field
                v-model="item2.value"
                label=""
                placeholder="请输入"
                @blur="checkField(item2)"
                :error-message="item2.error"
                type="number"
                maxlength="20"
              >
                <div slot="extra" v-if="item2.outRange" style="color:red">输入范围异常</div>
              </van-field>
            </div>
            <div class="gist type4" v-if="item2.isNum == '3'">
              <span
                >{{ index + 1 }}. 巡检要点：{{
                  item2.content ? item2.content : ""
                }}</span
              >
              <van-radio-group v-model="item2.radio">
                <van-radio
                  v-for="item3 in item2.termJson"
                  :key="item3.conText"
                  :name="item3.contText"
                  checked-color="#29bebc"
                  icon-size="16px"
                  @click="tapRadio(item2)"
                  >{{ item3.contText }}</van-radio
                >
              </van-radio-group>
            </div>
          </div>
        </van-collapse-item>
      </van-collapse>
    </div>
    <div class="foot-box" v-show="isFootShow">
      <div class="foot">
        <van-button
          type="primary"
          size="small"
          plain
          color="#29BEBC"
          @click="passDialogShow = true"
          >合格</van-button
        >
        <van-button
          type="primary"
          size="small"
          plain
          color="#29BEBC"
          @click="nopassDialogShow = true"
          >不合格</van-button
        >
        <van-button
          type="primary"
          size="small"
          color="#29BEBC"
          @click="openPopup"
          >异常反馈</van-button
        >
      </div>
    </div>
    <van-dialog v-model="passDialogShow" title="提示" show-cancel-button>
      <p class="dialog-info">确定提交吗？提交后无法进行修改</p>
      <div class="dialog-foot">
        <span @click="noDescSubmit('2')">不描述提交</span>
        <span @click="describeSubmit('pass', '2')">合格描述</span>
        <span @click="passDialogShow = false">取消</span>
      </div>
    </van-dialog>
    <van-dialog v-model="nopassDialogShow" title="提示" show-cancel-button>
      <p class="dialog-info">确定提交吗？提交后无法进行修改</p>
      <div class="dialog-foot">
        <span @click="noDescSubmit('3')">不描述提交</span>
        <span @click="describeSubmit('nopass', '3')">不合格描述</span>
        <span @click="nopassDialogShow = false">取消</span>
      </div>
    </van-dialog>
    <van-popup
      v-model="bottomShow"
      round
      position="bottom"
      :style="{ height: '30%' }"
    >
      <van-cell @click="goFeedback('1')">
        <template>
          <span class="popup-item">整改</span>
        </template>
      </van-cell>
      <van-cell @click="goFeedback('2')">
        <template>
          <span class="popup-item">自修</span>
        </template>
      </van-cell>
      <van-cell @click="goFeedback('3')">
        <template>
          <span class="popup-item">报修</span>
        </template>
      </van-cell>
      <van-cell to="" class="cell-cancel" @click="bottomShow = false">
        <template>
          <span class="popup-cancel">取消</span>
        </template>
      </van-cell>
    </van-popup>
  </div>
</template>

<script>
/* 
数值填空 0
无 1
文本填空 2
单选 3
*/
import topNav from "../components/topNav.vue";
export default {
  components: {
    topNav,
  },
  data() {
    return {
      taskData: {
        taskStartTime: "",
        taskEndTime: "",
        taskPointRelease: {
          taskPointName: "",
        },
      },
      projList: [],
      activeNames: [],
      value: "",
      radio: "",
      passDialogShow: false,
      nopassDialogShow: false,
      bottomShow: false, // 异常反馈的弹出框显示与隐藏
      from: "",
      id: "",
      canSubmit: true,
      active: "",
      taskInfo: "",
      role: "",
      scanFlag: "",
      pointId: "",
      source: "",
      typeValue: "",
      isLocation: "",
      giveUpLocation: "",
      ibeaconArr: "",
      docmHeight:
        document.documentElement.clientHeight || document.body.clientHeight,
      showHeight:
        document.documentElement.clientHeight || document.body.clientHeight,
      isFootShow: true, //显示或者隐藏footer
    };
  },
  watch: {
    //监听显示高度
    showHeight: function () {
      if (this.docmHeight > this.showHeight) {
        //隐藏
        this.isFootShow = false;
      } else {
        //显示
        this.isFootShow = true;
      }
    },
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.from = this.$route.query.from || "";
    this.id = this.$route.query.id || "";
    this.active = this.$route.query.active || "";
    this.taskInfo = this.$route.query.taskInfo || "";
    this.role = this.$route.query.role || "";
    this.scanFlag = this.$route.query.scanFlag || "";
    this.pointId = this.$route.query.pointId || "";
    this.source = this.$route.query.source || "";
    this.typeValue = this.$route.query.typeValue || "";
    this.isLocation = this.$route.query.isLocation || "";
    console.log("query", this.$route.query);
    console.log("thisid", this.id);
    
    if (this.from == "taskdetail") {
      this.getOneData();
    } else {
      this.taskData = this.$route.query.taskData || "";
      this.projList = this.taskData.taskPointRelease.maintainProjectRelease;
      //大扫码判断isLocation
      if(this.source == 'bigScanOneData') {
        this.axios.postContralHostBase("getTaskPointDetail", {taskId: this.taskData.id}, (res) => {
          if (res.code == "200") {
            let data = res.data.filter(e=>e.id == this.taskData.taskPointRelease.id)
            this.ibeaconArr = JSON.parse(sessionStorage.getItem("ibeaconArr")) || [];
            console.log(this.ibeaconArr, data,'ib&data');
            if (this.taskData.locationFlag == "0" && data[0].locationPointReleaseList.length != 0) {
              data[0].locationPointReleaseList.forEach((item) => {
                this.ibeaconArr.forEach((item2) => {
                  if (item.deviceMinor == item2.minor) {
                    this.isLocation = true;
                  }
                });
              });
            }
          }
        });
      }
      console.log('isLocation',this.isLocation);
      this.radioData(this.projList.maintainProjectdetailsReleaseList);
    }
  },
  mounted() {
    this.giveUpLocation = sessionStorage.getItem("giveUpLocation") || "false";
    this.ibeaconArr = JSON.parse(sessionStorage.getItem("ibeaconArr")) || [];
    window.onresize = () => {
      return (() => {
        this.showHeight =
          document.documentElement.clientHeight || document.body.clientHeight;
        console.log(this.showHeight);
      })();
    };
  },
  methods: {
    radioData(data) {
      data.forEach((item) => {
        item.maintainProjectdetailsTermReleaseList.forEach((item2) => {
          if (item2.isNum == "3") {
            item2.termJson.forEach((item3) => {
              if (item3.isDefault == "0") {
                item2.radio = item3.contText;
              }
            });
          }
        });
      });
      let arr = [];
      this.projList.maintainProjectdetailsReleaseList.forEach((item, index) => {
        arr.push(index);
      });
      this.activeNames = arr;
    },
    openPopup() {
      let arr = [];
      this.taskData.taskPointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach(
        (item) => {
          item.maintainProjectdetailsTermReleaseList.forEach((item2) => {
            if (item2.isNum == "3") {
              let obj = {
                id: item2.id,
                value: item2.radio,
              };
              arr.push(obj);
            } else if (item2.isNum != "1") {
              let obj = {
                id: item2.id,
                value: item2.value,
              };
              arr.push(obj);
            }
          });
        }
      );
      let canSubmitFlag = true;
      arr.forEach((item) => {
        if (!item.value) {
          canSubmitFlag = false;
        }
      });
      this.canSubmit = canSubmitFlag;
      if (!this.canSubmit) return this.$toast.fail("请输入内容");
      this.bottomShow = true;
    },
    describeSubmit(val, val2) {
      let spyScan = "";
      if (this.isLocation) {
        spyScan = "2";
      } else if (this.giveUpLocation == "true") {
        spyScan = "1";
      } else {
        spyScan = "3";
      }
      if (this.taskData.locationFlag == "1") {
        spyScan = "1";
      }
      let params = {
        taskPointReleaseId: this.taskData.taskPointRelease.id,
        state: val2,
        taskId: this.taskData.id,
        staffId: this.loginInfo.id,
        staffName: this.loginInfo.name,
        spyScan: spyScan,
      };
      let arr = [];
      this.taskData.taskPointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach(
        (item) => {
          item.maintainProjectdetailsTermReleaseList.forEach((item2) => {
            if (item2.isNum == "3") {
              let obj = {
                id: item2.id,
                value: item2.radio,
              };
              arr.push(obj);
            } else if (item2.isNum != "1") {
              let obj = {
                id: item2.id,
                value: item2.value,
              };
              arr.push(obj);
            }
          });
        }
      );
      params.answerMapList = arr;
      console.log(params);
      let canSubmitFlag = true;
      params.answerMapList.forEach((item) => {
        if (!item.value) {
          canSubmitFlag = false;
        }
      });
      this.canSubmit = canSubmitFlag;
      this.passDialogShow = false;
      this.nopassDialogShow = false;
      if (!this.canSubmit) return this.$toast.fail("请输入内容");
      console.log("next");
      params.answerMapList = JSON.stringify(params.answerMapList);
      this.$router.push({
        path: "/describe",
        query: {
          type: val,
          params,
          taskInfo: this.taskInfo,
          role: this.role,
          scanFlag: this.scanFlag,
          source: this.source,
          typeValue: this.typeValue,
          active: this.active,
          taskData: this.taskData,
        },
      });
    },
    checkField(item2) {
      if (!item2.value || item2.value.length == 0) {
        item2.error = "内容不能为空";
      } else {
        item2.error = "";
      }
      if (item2.value&&
        item2.value < parseInt(item2.rangeStart) ||
        item2.value > parseInt(item2.rangeEnd)
      ){
        item2.outRange = true
        // this.$toast('输入范围异常')
      }else {
        item2.outRange = false
      }
        this.$forceUpdate();
    },
    noDescSubmit(val) {
      let spyScan = "";
      console.log(this.isLocation,this.giveUpLocation,'submit424');
      if (this.isLocation) {
        spyScan = "2";
      } else if (this.giveUpLocation == "true") {
        spyScan = "1";
      } else {
        spyScan = "3";
      }
      if (this.taskData.locationFlag == "1") {
        spyScan = "1";
      }
      let params = {
        taskPointReleaseId: this.taskData.taskPointRelease.id,
        state: val,
        taskId: this.taskData.id,
        staffId: this.loginInfo.id,
        staffName: this.loginInfo.name,
        spyScan: spyScan,
      };
      let arr = [];
      this.taskData.taskPointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach(
        (item) => {
          item.maintainProjectdetailsTermReleaseList.forEach((item2) => {
            if (item2.isNum == "3") {
              let obj = {
                id: item2.id,
                value: item2.radio,
              };
              arr.push(obj);
            } else if (item2.isNum != "1") {
              let obj = {
                id: item2.id,
                value: item2.value,
              };
              arr.push(obj);
            }
          });
        }
      );
      params.answerMapList = arr;
      console.log(params);
      let canSubmitFlag = true;
      params.answerMapList.forEach((item) => {
        if (!item.value) {
          canSubmitFlag = false;
        }
      });
      this.canSubmit = canSubmitFlag;
      this.passDialogShow = false;
      this.nopassDialogShow = false;
      if (!this.canSubmit) return this.$toast.fail("请输入内容");
      console.log("next");
      params.answerMapList = JSON.stringify(params.answerMapList);
      this.axios.postContralHostBase("inspectionSubmit", params, (res) => {
        if (res.code == "200") {
          let that = this;
          this.$toast.success({
            message: "提交成功!",
            duration: 1000,
            onClose() {
              if (that.source == "bigScan") {
                that.$router.push({
                  path: "tasks",
                  query: {
                    active: that.active,
                    taskData: that.taskData,
                    from: "bigScan",
                    fromPage: "inspectionContent",
                    typeValue: that.typeValue,
                  },
                });
              } else if (that.source == "bigScanOneData") {
                that.$router.go(-1);
              } else {
                that.$router.push({
                  path: "taskDetail",
                  query: {
                    active: that.active,
                    taskInfo: that.taskInfo,
                    role: that.role,
                    scanFlag: that.scanFlag,
                  },
                });
              }
            },
          });
        }
      });
    },
    goFeedback(type) {
      console.log(type);
      let spyScan = "";
      if (this.isLocation) {
        spyScan = "2";
      } else if (this.giveUpLocation == "true") {
        spyScan = "1";
      } else {
        spyScan = "3";
      }
      if (this.taskData.locationFlag == "1") {
        spyScan = "1";
      }
      this.$router.push({
        path: "inspectionSelect",
        query: {
          type,
          taskPointInfo: this.taskData.taskPointRelease,
          projList: this.projList,
          spyScan: spyScan,
          id:this.id,
          from:this.from,
          active:this.active,
          taskInfo:this.taskInfo,
          role:this.role,
          scanFlag:this.scanFlag
        },
      });
    },
    getOneData() {
      let params = {};
      if (this.source == "bigScan") {
        params.pointId = this.pointId;
        params.taskId = this.id;
        params.id = this.taskInfo.taskPointRelease.id;
      } else {
        params.id = this.id;
      }
      console.log("id", params.id);
      this.axios.postContralHostBase(
        "getInspectionContentData",
        params,
        (res) => {
          if (res.code == "200") {
            this.taskData = res.data;
            this.projList = res.data.taskPointRelease.maintainProjectRelease;
            this.radioData(this.projList.maintainProjectdetailsReleaseList);
          }
        }
      );
    },
    tapRadio(item) {
      this.$forceUpdate();
      console.log(item);
    },
    asyncValidator(val) {
      console.log("nb", val);
    },
    goBack() {
      if (this.source == "bigScan") {
        this.$router.push({
          path: "tasks",
          query: {
            active: this.active,
            taskData: this.taskData,
            from: "bigScan",
            fromPage: "inspectionContent",
            typeValue: this.typeValue,
          },
        });
      } else if (this.source == "bigScanOneData") {
        this.$router.go(-1);
      } else {
        console.log("你看看");
        this.$router.push({
          path: "taskDetail",
          query: {
            active: this.active,
            taskInfo: this.taskInfo,
            role: this.role,
            scanFlag: this.scanFlag,
          },
        });
      }
    },
  },
  filters: {
    dateTrans(value, type) {
      console.log("a", value);
      let arr = value.split(" ");
      if (type == 6) {
        return arr[1];
      } else {
        return arr[0];
      }
    },
  },
};
</script>

<style scoped>
#app {
  overflow: hidden;
}
.container {
  width: 100vw;
  background-color: #fff;
}
.header {
  background-color: #fff;
}
.item {
  min-height: 48px;
  /* line-height: 48px; */
  border-bottom: 1px solid #ebedf0;
  padding-left: 16px;
  display: flex;
  align-items: center;
}
.item span:nth-child(1) {
  display: inline-block;
  font-size: 15px;
  color: #353535;
  min-width: 110px;
}
.item span:nth-child(2) {
  font-size: 14px;
  color: #888888;
}
>>> .van-collapse {
  height: 48vh;
  overflow: auto;
}
.main .title {
  background-color: #fff;
  height: 54px;
  display: flex;
  align-items: center;
  padding: 0 16px;
}
.main .title span {
  font-size: 16px;
  font-weight: 700;
  border-left: 4px solid #29bebc;
  padding-left: 8px;
}
>>> .van-cell__title {
  display: flex;
  align-items: center;
}
.project {
  min-width: 58px;
  margin: 0 12px 0 5px;
}
>>> .van-field {
  border-bottom: 1px solid #dddddd;
}
.gist {
  color: #353535;
  margin-bottom: 10px;
}
.van-radio-group {
  padding-left: 8%;
  margin-top: 8px;
}
>>> .van-cell::after {
  border-bottom: none;
}
>>> .van-collapse-item--border::after {
  display: none;
}
>>> .van-hairline--top-bottom::after {
  border-bottom: none;
}
.van-radio {
  margin-bottom: 8px;
}
.foot-box {
  position: fixed;
  width: 100%;
  bottom: 24px;
}
.foot {
  display: flex;
  background-color: #fff;
  justify-content: space-around;
}
>>> .van-button {
  border-radius: 5px;
  width: 20vw;
}
.split-line {
  width: 100vw;
  height: 10px;
  background-color: #f5f6fb;
}
>>> .van-dialog__footer {
  display: none;
}
.dialog-foot {
  height: 48px;
  display: flex;
  border-top: 1px solid #ebedf0;
}
.dialog-foot span {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  color: #353535;
}
.dialog-foot span:nth-child(2) {
  color: #29bebc;
  border-left: 1px solid #ebedf0;
  border-right: 1px solid #ebedf0;
}
.dialog-info {
  color: #353535;
  text-align: center;
}
.popup-item {
  font-size: 18px;
  text-align: center;
  color: #353535;
}
.popup-cancel {
  font-size: 17px;
  text-align: center;
  color: #888888;
}
.van-cell__value {
  text-align: center;
}
.van-popup {
  padding-top: 18px;
  /* box-sizing: border-box; */
}
.van-cell {
  padding: 12px 16px;
}
.van-cell::after {
  border: none;
}
.cell-cancel {
  /* margin-top: 18px; */
  border-top: 10px solid #f5f6fb;
}
</style>