<template>
  <div class="container">
    <van-overlay :show="overlayShow">
      <div class="wrapper">
        <div class="block">
          <van-loading size="70px">加载中...</van-loading>
        </div>
      </div>
    </van-overlay>
    <Header title="巡检任务" @backFun="goBack"> </Header>
    <div v-if="from != 'scanCode'" style="height: calc(100% - 60px);">
      <!-- <van-tabs
        v-model="active"
        sticky
        offset-top="60px"
        color="#29BEBC"
        line-width="50"
        title-active-color="#29BEBC"
        @change="onChange"
      >
        <van-tab v-for="item in tasksList" :title="item.dictName" :key="item.id">
          <van-empty description="暂无数据" v-if="!item.details" />
          <van-pull-refresh
            v-model="refreshing"
            @refresh="onRefresh"
            success-text="刷新成功"
            v-else
          >
            <van-list
              v-model="loading"
              :finished="listFinished"
              finished-text="没有更多了"
              @load="onLoad"
            >
            <div class="list" v-for="(i, index) in item.details" :key="index" @click="goTaskDetail(i)">
              <span class="title">
                <span class="taskType">{{ cycleTypeFn(i.cycleType) }}</span>
                <span style="margin: 0 8px 0 5px">任务名称:</span>
                <span class="task-name">{{ i.taskName }}</span>
              </span>
              <div class="finish">
                <span style="color: #1D2129;">巡检点:</span>
                <span>{{ i.planCount }}</span>
              </div>
              <div class="time">
                <span>巡检时间:</span>
                <span style="color: #1D2129;"
                  >{{ i.taskStartTime }}至
                  <br>
                  {{ i.taskEndTime }}</span
                >
              </div>
            </div>
            </van-list>
          </van-pull-refresh>
        </van-tab>
      </van-tabs> -->

      <van-tabs
        v-model="active"
        sticky
        offset-top="60px"
        color="#29BEBC"
        line-width="50"
        title-active-color="#29BEBC"
        @change="onChange"
      >
        <van-tab v-for="item in tasksList" :title="item.dictName" :key="item.id"></van-tab>
      </van-tabs>
      <van-pull-refresh
        v-model="refreshing"
        @refresh="onRefresh"
      >
        <van-list
          v-model="loading"
          :finished="listFinished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div class="list" v-for="(i, index) in listData" :key="index" @click="goTaskDetail(i)">
            <span class="title">
              <span class="taskType">{{ cycleTypeFn(i.cycleType) }}</span>
              <span style="margin: 0 8px 0 5px">任务名称:</span>
              <span class="task-name">{{ i.taskName }}</span>
            </span>
            <div class="finish">
              <span style="color: #1D2129;">巡检点:</span>
              <span>{{ i.planCount }}</span>
            </div>
            <div class="time">
              <span>巡检时间:</span>
              <span style="color: #1D2129;"
                >{{ i.taskStartTime }}至
                <br>
                {{ i.taskEndTime }}</span
              >
            </div>
          </div>
          <template #loading v-if="refreshing"><div></div></template>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else>
      <div class="list" v-for="i in scanData" :key="i.id" @click="goTaskDetail(i)">
        <span class="title">
          <span class="taskType">{{ cycleTypeFn(i.cycleType) }}</span>
          <span style="margin: 0 8px 0 5px">任务名称:</span>
          <span>{{ i.taskName }}</span>
        </span>
        <div class="finish">
          <span style="color: #1D2129;">巡检点:</span>
          <span>{{ i.planCount }}</span>
        </div>
        <div class="time">
          <span>巡检时间:</span>
          <span style="color: #1D2129;"
            >{{ i.taskStartTime }}至
            <br>
            {{ i.taskEndTime }}</span
          >
        </div>
        <div v-if="i.taskPointRelease.carryOutFlag == '1'" class="isFinish">
          <img width="100%" src="../../assets/images/lable <EMAIL>" alt="">
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import topNav from "../components/topNav.vue";
import axios from 'axios'
export default {
  components: {
    topNav,
  },
  data() {
    return {
      active: 0,
      refreshing: false,
      cycleTypeList: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        },
      ], 
      tasksList: [
        {
          id: 0,
          dictName: "待巡检",
          details: []
        },
        {
          id: 1,
          dictName: "已巡检",
          details: []
        },
        {
          id: 2,
          dictName: "已超期",
          details: []
        }
      ],
      scanData: [],
      overlayShow: false,
      scanFlag: "",
      from: "",
      taskData: "",
      loginInfo: "",
      typeValue: "",
      giveUpLocation: "",
      ibeaconArr: [],
      locationData: "",
      isLocation: false,
      finished: true,
      params : {
        accomplishType: 0, //0 待巡检，1 已巡检，2已超期
        pageNo: 1,
        pageSize: 10,
        planId: this.$route.query.planId || ''
      },
      timer: null,
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
      timerTow: '',
      loading: false,
      listFinished: false,
      listData: []
    };
  },
  created() {
    this.getData()
    apiready = () => {
      var userInfo = api.getPrefs({
        sync: true,
        key: "userInfo"
      });
      userInfo = JSON.parse(userInfo);
      userInfo.hospitalCode = api.pageParam.hospitalCode
      userInfo.hospitalName = api.pageParam.hospitalName
      if (userInfo.id) {
        const virtualToken = encodeURIComponent(userInfo.hospitalName);
        localStorage.setItem('token', virtualToken);
        localStorage.setItem("loginInfo", JSON.stringify(userInfo));
      }
      this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
      this.active = parseInt(this.$route.query.active) || 0;
      this.params.accomplishType = this.active || 0
      this.from = this.$route.query.from || "";
      this.taskData = this.$route.query.taskData || "";
      this.role = JSON.parse(localStorage.getItem("role"));
      this.typeValue = this.$route.query.typeValue || "";
      if (this.from == "scanCode") {
        this.getScanList()
      }
      this.sysClickBack()
    };
    if (JSON.parse(localStorage.getItem("loginInfo")).isHospital == 0) {
      var userInfo = api.getPrefs({
        sync: true,
        key: "userInfo"
      });
      userInfo = JSON.parse(userInfo);
      userInfo.hospitalCode = api.pageParam.hospitalCode
      userInfo.hospitalName = api.pageParam.hospitalName
      if (userInfo.id) {
        const virtualToken = encodeURIComponent(userInfo.hospitalName);
        localStorage.setItem('token', virtualToken);
        localStorage.setItem("loginInfo", JSON.stringify(userInfo));
      }
    }
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.active = parseInt(this.$route.query.active) || 0;
    this.params.accomplishType = this.active || 0
    this.from = this.$route.query.from || "";
    this.taskData = this.$route.query.taskData || "";
    this.role = JSON.parse(localStorage.getItem("role"));
    this.typeValue = this.$route.query.typeValue || "";
    if (this.from == "scanCode") {
      this.getScanList()
    }
  },
  mounted() {
    this.giveUpLocation = sessionStorage.getItem("giveUpLocation") || "false";
    this.ibeaconArr = JSON.parse(sessionStorage.getItem("ibeaconArr")) || [];
    this.sysClickBack()
  },
  methods: {
    onLoad() {
      this.params.pageNo++
      this.listFinished = false
      this.loading = true
      this.getData()
    },
    onRefresh() {
      this.params.pageNo = 1
      this.listFinished = false
      this.loading = true
      this.listData = []
      this.getData()
    },
    onChange(name, title) {
      this.params.pageNo = 1
      this.params.accomplishType = name
      this.active = name
      this.loading = true
      this.listData = []
      this.getData()
    },
    // 点击按钮返回
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    async goTaskDetail(data) {
      if (this.from == 'scanCode') {
        this.overlayShow = true
        // 已执行任务
        if (data.taskPointRelease.carryOutFlag == "1") {
          // return this.$toast("该巡检点已完成，无需再次进行操作。");
          let type = null
          if (data.taskPointRelease.maintainProjectRelease && data.taskPointRelease.maintainProjectRelease.equipmentTypeName == '日常巡检') {
            type = 0
          } else if (data.taskPointRelease.maintainProjectRelease && data.taskPointRelease.maintainProjectRelease.equipmentTypeName == '专业巡检') {
            type = 1
          } else {
            type = 2
          }
          this.$router.push({
            path: "recordDetail",
            query: {
              id: data.taskPointRelease.id,
              type,
              from: 'scanCodeMoreTask'
            }
          })
        } else {
          const item = {
            taskPointRelease: {
            id: data.taskPointRelease.id
          },
          id: data.taskPointRelease.taskId
          }
          if (data.locationFlag == "0") { // 定位
            this.getLocation(item, data.taskPointRelease.maintainProjectRelease, false,data.taskPointRelease.locationPointReleaseList)
          } else { // 不定位
            if (data.taskPointRelease.maintainProjectRelease) { // 有任务书
              let type = null
              if (data.taskPointRelease.maintainProjectRelease.equipmentTypeName == '日常巡检') {
                type = 0
              } else if (data.taskPointRelease.maintainProjectRelease.equipmentTypeName == '专业巡检') {
                type = 1
              }
              this.overlayShow = false
              this.$router.push({
                path: "checkDetail",
                query: {
                  id: data.taskPointRelease.id,
                  type,
                  from: this.from
                },
              });
            } else { // 无任务书
              this.overlayShow = false
              this.noBookSubmit(item, 1)
            }
          }
        }
      } else {
        sessionStorage.setItem('taskInfo',JSON.stringify(data))
        this.$router.push({
          path: 'taskDetail',
          query: { 
            taskInfo: data,
            active: this.active,
            from: this.from == '' ? 'tasks' : this.from,
            sortFlag: data.sortFlag
          }
        })
      }
    },
    // 定位
    getLocation(taskData, maintainProjectRelease, scanFlag, location) {
      let that = this;
      let whetherLocation = sessionStorage.getItem("whetherLocation") || "";
      let giveUpLocation = sessionStorage.getItem("giveUpLocation") || "";
      let ibeaconArr = JSON.parse(sessionStorage.getItem("ibeaconArr")) || "";
      this.ibeacon = api.require('brightBeacon');
      if (
        whetherLocation == "true" ||
        giveUpLocation == "true" ||
        ibeaconArr.length > 0
      ) {
      } else {
        // setTimeout(() => {
          // that.show = true;
        // }, 500);
        setTimeout(() => {
          that.show = false;
        }, 2000);
      }
      this.ibeacon.startRanging(
        {
          uuids: ['E2C56DB5-DFFB-48D2-B060-D0F5A71096E0','FDA50693-A4E2-4FB1-AFCF-C6EB07647825']
        }, (res, err) => {
          this.ibeaconArr = res.list.map(i => "_" + i.minor)
          if (this.ibeaconArr.length == 0) {
            this.ibeacon.stopRanging();
            this.$toast('无法获取定位信息，请开启蓝牙或重新扫码')
            sessionStorage.setItem("whetherLocation", 3);
          } else {
            sessionStorage.setItem("ibeaconArr", JSON.stringify(this.ibeaconArr));
            this.ibeaconArr.forEach(i => {
              whetherLocation = location.filter(item => item.deviceUuid + "_" + item.deviceMinor != i)
            })
            //whetherLocation 1：未开启定位 2:定位成功 3：定位失败
            if (whetherLocation.length > 0) {
              sessionStorage.setItem("whetherLocation", 2);
            }
          }
          this.show = false
          this.ibeacon.stopRanging();
          if (!scanFlag) { // 不扫码
            sessionStorage.setItem("whetherLocation", 1)
            if (maintainProjectRelease) { // 有任务书
              let type = null
              if (maintainProjectRelease.equipmentTypeName == '日常巡检') {
                type = 0
              } else if (maintainProjectRelease.equipmentTypeName == '专业巡检') {
                type = 1
              }
              this.$router.push({
                path: "checkDetail",
                query: {
                  id: taskData.taskPointRelease.id,
                  type,
                  from: this.from
                },
              });
            } else { // 无任务书
              this.noBookSubmit(taskData, 1)
            }
          }
        }
      );
    },
    // 无任务书提交
    noBookSubmit(taskData, location) {
      let params = {
        taskPointReleaseId: taskData.taskPointRelease.id,
        state: "2",
        taskId: taskData.id,
        staffId: this.loginInfo.id,
        staffName: this.loginInfo.name,
        spyScan: location,
        isBookEmpty: true,
      };
      this.axios.postContralHostBase("inspectionSubmit", params, (res) => {
        if (res.code == "200") {
          let that = this;
          this.$toast.success({
            message: "执行成功!",
            duration: 1000,
            onClose() {
              that.getScanList()
            },
          });
        }
        this.overlayShow = false
      });
    },
    getData() {
      this.params.accomplishType = this.active || 0
      this.axios.postContralHostBase("getInspectionTaskList", this.params, (res) => {
        if (res.code == "200") {
          this.$nextTick(() => {
            this.listData = this.listData.concat(res.data)
            if (res.data.length < 10) {
              this.listFinished = true
            } else {
              this.listFinished = false
            }
            this.refreshing = false
            this.loading = false
          })
        }
      });
      this.$forceUpdate();
    },
    getScanList() {
      const params = JSON.parse(sessionStorage.getItem('scanCodeData'))
      params.unitCode = this.loginInfo.unitCode
      params.hospitalCode = this.loginInfo.hospitalCode
      
      this.overlayShow = true
      this.axios.postContralHostBase('getPerformTask', params, res => {
        if (res.code == '200') {
          this.scanData = res.data
          if(this.scanData.length == 0) {
            this.$toast("暂无可执行任务");
            api.closeFrame({});
          }
        } else {
          setTimeout(() => {
            this.goBack()
          }, 3000)
        }
        this.overlayShow = false
      })
    },
    goBack() {
      api.closeFrame({});
    },
    cycleTypeFn(row) {
      const item = this.cycleTypeList.find(i => i.cycleType == row)
      return item.label
    }
  },
  filters: {
    transDate(val) {
      return val.split(" ")[0];
    },
  },
  beforeDestroy() {
    window.onscroll = null
  }
};
</script>

<style lang="scss" scoped>
.container {
  width: 100vw;
  height: 100vh;
  background-color: #fff;
}
.van-list {
  background-color: #fff;
}
/deep/ .van-tabs__content {
  display: none;
}
.van-pull-refresh {
  height: calc(100% - 44px);
  overflow: auto;
  .van-pull-refresh__track {
    overflow: auto;
  }
}
.list {
  border-bottom: 10px solid #F2F4F9;
  font-size: 16px;
  position: relative;
  background-color: #fff;
}
.list > .title {
  /* display: inline-block; */
  display: flex;
  align-items: center;
  color: #353535;
  font-weight: 600;
  width: 100%;
  margin: 0 0 12px 0;
  padding: 14px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border-bottom: 1px solid #e6eaf0 ;
}
.taskType {
  display: inline-block;
  width: 45px;
  height: 19px;
  /* background-color: #F2F2F2; */
  background-color: #e6eaf0;
  border-radius: 10px;
  font-size: 14px;
  -webkit-transform: scale(0.8);
  color: #688DFF;
  text-align: center;
  line-height: 19px;
  font-weight: normal;
}
.list > div {
  margin-bottom: 12px;
  padding: 0 0 0 25px;
  display: flex;
}
.list > div span:nth-child(1) {
  width: 70px;
  font-weight: 400;
  font-size: 15px;
}
.list > div span:nth-child(2) {
  font-weight: 400;
  font-size: 14px;
  color: #888888;
  padding-left: 5px;
}
.current-time span:nth-child(1) {
  line-height: 16px;
}
.current-time span:nth-child(2) {
  display: flex;
  align-items: center;
}
.cycle {
  position: absolute;
  right: 16px;
  top: 14px;
  font-size: 14px;
  color: #29bebc;
}
.finish {
  align-items: center;
}
.time {
  color: #4E5969;
}
.isFinish {
  max-width: 75px;
  position: absolute;
  right: 0;
  bottom: 2%;
  height: 50%;
}
.van-overlay {
  z-index: 3;
}
.van-empty {
  width: 100vw;
  height: 88vh;
}
.bottom-tips {
  font-size: 12px;
  text-align: center;
  color: #969799;
}
.task-name {
  text-overflow:ellipsis;
  overflow:hidden;
  white-space:nowrap;
}
/deep/ .van-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  .block {
    padding: 10px 0 0 0;
    width: 120px;
    height: 110px;
    border-radius: 10px;
    background-color: #ddd;
    text-align: center;
  }
}
</style>