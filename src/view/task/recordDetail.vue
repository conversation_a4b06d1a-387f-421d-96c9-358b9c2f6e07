<template>
  <div class="container">
    <van-overlay :show="loading">
      <div class="wrapper">
        <div class="block">
          <van-loading size="70px">加载中...</van-loading>
        </div>
      </div>
    </van-overlay>
    <Header title="巡检详情" @backFun="goBack"> </Header>
    <div v-if="type == 0">
      <div class="header">
        <div class="item">
          <span class="header-title">巡检点名称：</span>
          <span>{{
            pointRelease.taskPointName
          }}</span>
        </div>
      </div>
      <div class="split-line"></div>
      <div class="main">
        <div class="title">
          <span>巡检任务书</span>
        </div>
        <van-collapse v-model="activeNames" :border="false">
          <van-collapse-item
           :is-link="false"
            title=""
            :name="index"
            v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList"
            :key="item.id"
          >
            <template #title>
              <van-icon name="notes-o" color="#29bebc" size="16" />
              <span class="project">巡检内容</span>
              <div style="display: flex;align-items: center;">
                <span class="taskProject-item">{{ item.detailName }}</span>
                <div>
                  <van-radio-group v-model="item.maintainProjectdetailsTermReleaseList[0].normal" class="radio" disabled>
                    <van-radio icon-size="12px" name="0">正常</van-radio>
                    <van-radio icon-size="12px" name="1">异常</van-radio>
                  </van-radio-group>
                </div>
              </div>
              
            </template>
            <div class="daily-items">标准项：<span>{{item.maintainProjectdetails.standardRequirements}}</span></div>
            <div class="daily-items">参考依据：<span>{{item.maintainProjectdetails.inspectionBasis}}</span></div>
          </van-collapse-item>
        </van-collapse>
      </div>
      <div class="instructions">
        <div class="explain">
          <span>巡检执行</span>
        </div>
        <div class="explan-item">
          <span>巡检情况:</span>
          <span class="labelVal">{{excute.carryOutFlag=='0'?'未巡':excute.carryOutFlag=='1'?'已巡':''}}</span>
        </div>
        <div class="explan-item">
          <span>巡检人员:</span>
          <span class="labelVal">{{ excute.implementPersonName }}</span>
        </div>
      </div>
      <div class="instructions">
        <div class="explain">
          <span>巡检情况</span>
        </div>
        <div class="explan-item">
          <span>巡检结果:</span>
          <span class="labelVal">{{result.state=='2'?'合格':result.state=='3'?'不合格':''}}</span>
        </div>
        <div class="explan-item">
          <span class="instructions-text">情况说明:</span>
          <span class="labelVal">{{ result.desc }}</span>
        </div>
        <div class="explan-item">
          <span>定位状态:</span>
          <span class="labelVal">{{ excute.spyScan }}</span>
        </div>
        <div class="explan-item">
          <span style="flex: 1 1 30%">图片:</span>
          <div class="resultImgBox" v-if="result.attachmentUrlList!=='[]'" >
            <div v-for="(item,index) in result.attachmentUrlList" :key="index"  @click="showImgUrl(result.attachmentUrlList,index)">
              <img width="30%" :src="item" />
            </div>
          </div>
          <div v-else>暂无</div>
        </div>
      </div>
    </div>
    <div v-if="type == 1">
      <div class="header">
        <div class="item">
          <span class="header-title">巡检点名称：</span>
          <span>{{
            pointRelease.taskPointName
          }}</span>
        </div>
      </div>
      <div class="split-line"></div>
      <div class="main">
        <div class="title">
          <span>巡检任务书</span>
        </div>
        <van-collapse v-model="activeNames">
          <van-collapse-item
            title=""
            :name="index"
            v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList"
            :key="item.id"
          >
            <template #title>
              <van-icon name="notes-o" color="#29bebc" size="16" />
              <span class="project">巡检项目</span>
              <span>{{ item.detailName }}</span>
            </template>
            <div
              v-for="(item2, index) in item.maintainProjectdetailsTermReleaseList"
              :key="item2.id"
            >
              <div class="gist type1" v-if="item2.isNum == '1'">
                <span
                  >{{ index + 1 }}. 巡检要点：{{
                    item2.content ? item2.content : ""
                  }}</span
                >
              </div>
              <div class="gist type2" v-if="item2.isNum == '2'">
                <span
                  >{{ index + 1 }}. 巡检要点：{{
                    item2.content ? item2.content : ""
                  }}</span
                >
                <van-field
                  v-model="item2.contentStandard"
                  label=""
                  rows="1"
                  type="textarea"
                  readonly
                  autosize
                />
              </div>
              <div class="gist type3" v-if="item2.isNum == '0'">
                <span
                  >{{ index + 1 }}. 巡检要点：{{
                    item2.content
                      ? item2.content +
                        "(" +
                        item2.rangeStart +
                        "-" +
                        item2.rangeEnd +
                        (item2.einheitName ? item2.einheitName : '') +
                        ")"
                      : ""
                  }}</span
                >
                <van-field
                  v-model="item2.contentStandard"
                  label=""
                  placeholder="请输入"
                  @blur="checkField(item2)"
                  :error-message="item2.error"
                  type="number"
                  readonly
                  autosize
                >
                  <div slot="extra" v-if="item2.outRange" style="color:red">输入范围异常</div>
                </van-field>
              </div>
              <div class="gist type4" v-if="item2.isNum == '3'">
                <span
                  >{{ index + 1 }}. 巡检要点：{{
                    item2.content ? item2.content : ""
                  }}</span
                >
                <van-radio-group v-model="item2.contentStandard">
                  <van-radio
                    v-for="item3 in item2.termJson"
                    :key="item3.conText"
                    :name="item3.contText"
                    checked-color="#29bebc"
                    icon-size="16px"
                    disabled
                    >{{ item3.contText }}</van-radio
                  >
                </van-radio-group>
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
      </div>
      <div class="instructions">
        <div class="explain">
          <span>巡检执行</span>
        </div>
        <div class="explan-item">
          <span>巡检情况:</span>
          <span class="labelVal">{{excute.carryOutFlag=='0'?'未巡':excute.carryOutFlag=='1'?'已巡':''}}</span>
        </div>
        <div class="explan-item">
          <span>巡检人员:</span>
          <span class="labelVal">{{ excute.implementPersonName }}</span>
        </div>
      </div>
      <div class="instructions">
        <div class="explain">
          <span>巡检情况</span>
        </div>
        <div class="explan-item">
          <span>巡检结果:</span>
          <span class="labelVal">{{result.state=='2'?'合格':result.state=='3'?'不合格':''}}</span>
        </div>
        <div class="explan-item">
          <span class="instructions-text">情况说明:</span>
          <span class="labelVal">{{ result.desc }}</span>
        </div>
        <div class="explan-item">
          <span>定位状态:</span>
          <span class="labelVal">{{ excute.spyScan }}</span>
        </div>
        <div class="explan-item">
          <span style="flex: 1 1 30%">图片:</span>
          <div class="resultImgBox" v-if="result.attachmentUrlList!=='[]'" >
            <div v-for="(item,index) in result.attachmentUrlList" :key="index"  @click="showImgUrl(result.attachmentUrlList,index)">
              <img width="30%" :src="item" />
            </div>
          </div>
          <div v-else>暂无</div>
        </div>
      </div>
    </div>
    <div v-if="type == 2">
      <div class="header">
        <div class="item">
          <span class="header-title">巡检点名称：</span>
          <span>{{
            pointRelease.taskPointName
          }}</span>
        </div>
      </div>
      <div class="split-line"></div>
      <div class="main">
        <div class="title">
          <span>巡检任务书</span>
        </div>
        <van-empty description="暂无数据"/>
      </div>
      <div class="instructions">
        <div class="explain">
          <span>巡检执行</span>
        </div>
        <div class="explan-item">
          <span>巡检情况:</span>
          <span class="labelVal">{{excute.carryOutFlag=='0'?'未巡':excute.carryOutFlag=='1'?'已巡':''}}</span>
        </div>
        <div class="explan-item">
          <span>巡检人员:</span>
          <span class="labelVal">{{ excute.implementPersonName }}</span>
        </div>
      </div>
      <div class="instructions">
        <div class="explain">
          <span>巡检情况</span>
        </div>
        <div class="explan-item">
          <span>巡检结果:</span>
          <span class="labelVal">{{result.state=='2'?'合格':result.state=='3'?'不合格':''}}</span>
        </div>
        <div class="explan-item">
          <span class="instructions-text">情况说明:</span>
          <span class="labelVal">{{ result.desc }}</span>
        </div>
        <div class="explan-item">
          <span>定位状态:</span>
          <span class="labelVal">{{ excute.spyScan }}</span>
        </div>
        <div class="explan-item">
          <span style="flex: 1 1 30%">图片:</span>
          <div class="resultImgBox" v-if="result.attachmentUrlList!=='[]'" >
            <div v-for="(item,index) in result.attachmentUrlList" :key="index"  @click="showImgUrl(result.attachmentUrlList,index)">
              <img width="30%" :src="item" />
            </div>
          </div>
          <div v-else>暂无</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
import { ImagePreview } from "vant";
export default {
  components: {
    topNav,
  },
  data() {
    return {
      loading: false,
      type: null,
      id: "",
      excute: "",
      result: "",
      pointRelease: {
        maintainProjectRelease: {
          maintainProjectdetailsReleaseList: [],
        },
      },
      activeNames: [],
      active:'',
      inputSytle:{
        'minHeight': 90,
        'minWidth': 300
      },
      files: [],
      radio:'',
      explainText:'',// 情况说明
      errMsg: '',
      from: '',
      checked: false,
      attachmentUrl: [], //图片地址
      bigImgUrl: '',
      dialogTableVisible: false,
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
    };
  },
  created() {
    this.id = this.$route.query.id;
    this.active = this.$route.query.active || '';
    this.type = this.$route.query.type;
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.from = this.$route.query.from || "";
    this.getData();
  },
  mounted() {
    this.sysClickBack()
  },
  methods: {
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    getData() {
      this.loading = true
      let params = {
        id: this.id,
        hospitalCode: this.loginInfo.hospitalCode || '',
        unitCode: this.loginInfo.unitCode || ''
      };
      this.axios.postContralHostBase("getCheckDetail", params, (res) => {
        if (res.code == "200") {
          const { excute, result, pointRelease } = res.data;
          this.excute = excute;
          this.result = result;
          this.pointRelease = pointRelease;
          if (pointRelease.maintainProjectRelease && pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList) {
            pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i, index) => this.activeNames.push(index))
          }
          this.loading = false
        }
      });
    },
    goBack() {
      if (this.from == 'scanCodeOneTask') {
        api.closeFrame({});
      } else if (this.from == 'scanCodeMoreTask' || this.$route.query.isFireOperation) {
        this.$router.go(-1)
      } else {
        this.$router.push({
          path:'taskDetail',
          query:{
            active:this.active,
            id: this.pointRelease.taskId,
            from: this.from
          }
        })
      }
    },
    formatter(val, e) {
      if(Number(val) >= Number(e.rangeStart) && Number(val) <= Number(e.rangeEnd)) {
        console.log('合格', val)
        this.errMsg = ''
      } else if (Number(val) < Number(e.rangeStart) || Number(val) > Number(e.rangeEnd)) {
        console.log('不合格')
        this.errMsg = "输入范围异常"
      }
      return val
    },
    showImgUrl(item,index) {
      ImagePreview({
        images: item,
        showIndex: true,
        loop: false,
        startPosition: index,
        swipeDuration: 50
      });
    }
  }
};
</script>

<style lang="scss" scoped>
#app {
  overflow: hidden;
}
.container {
  width: 100vw;
  background-color: #fff;
  padding-bottom: 15vh;
}
.header {
  background-color: #fff;
  border-bottom: 4px solid #ebedf0;
}
.item {
  min-height: 48px;
  padding-left: 15px;
  display: flex;
  align-items: center;
}
.item span:nth-child(1) {
  display: inline-block;
  padding-left: 8px;
  font-size: 15px;
  font-weight: 700;
  border-left: 4px solid #29bebc;
  color: #353535;
  min-width: 110px;
}
.item span:nth-child(2) {
  font-size: 15px;
  color: #888888;
}
>>> .van-collapse {
  /* height: 48vh; */
  overflow: auto;
}
.main {
  border-bottom: 4px solid #ebedf0;
}
.main .title {
  background-color: #fff;
  min-height: 48px;
  display: flex;
  align-items: center;
  padding: 0 15px;
}
.main .title span {
  font-size: 15px;
  font-weight: 700;
  border-left: 4px solid #29bebc;
  padding-left: 8px;
}
>>> .van-cell__title {
  display: flex;
  align-items: center;
}
.project {
  min-width: 58px;
  margin: 0 12px 0 5px;
}
>>> .van-field {
  border-bottom: 1px solid #dddddd;
}
.gist {
  color: #353535;
  margin-bottom: 10px;
}
.van-radio-group {
  padding-left: 8%;
  margin-top: 8px;
}
>>> .van-cell::after {
  border-bottom: none;
}
>>> .van-collapse-item--border::after {
  display: none;
}
>>> .van-hairline--top-bottom::after {
  border-bottom: none;
}
.van-radio {
  margin-bottom: 8px;
}
.foot-box {
  position: fixed;
  width: 100%;
  bottom: 24px;
}
.foot {
  display: flex;
  background-color: #fff;
  justify-content: space-around;
}
>>> .van-button {
  border-radius: 5px;
  width: 20vw;
}
.split-line {
  width: 100vw;
  height: 10px;
  background-color: #f5f6fb;
}
>>> .van-dialog__footer {
  display: none;
}
.dialog-foot {
  height: 48px;
  display: flex;
  border-top: 1px solid #ebedf0;
}
.dialog-foot span {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  color: #353535;
}
.dialog-foot span:nth-child(2) {
  color: #29bebc;
  border-left: 1px solid #ebedf0;
  border-right: 1px solid #ebedf0;
}
.dialog-info {
  color: #353535;
  text-align: center;
}
.popup-item {
  font-size: 18px;
  text-align: center;
  color: #353535;
}
.popup-cancel {
  font-size: 17px;
  text-align: center;
  color: #888888;
}
.van-cell__value {
  text-align: center;
}
.van-popup {
  padding-top: 18px;
  /* box-sizing: border-box; */
}
.van-cell {
  padding: 12px 16px;
}
.van-cell::after {
  border: none;
}
.cell-cancel {
  /* margin-top: 18px; */
  border-top: 10px solid #f5f6fb;
}
.inputText {
  padding: 10px 30px;
}
.upload {
  background-color: #fff;
}
.upload > span {
  display: inline-block;
}
.upload-title {
  display: block;
  display: flex;
  justify-content: space-between;
}
.upload-img {
  padding: 0 16px 0 0;
  display: inline-block;
  font-size: 13px;
  color: #888888;
}
.dailySub {
  position: absolute;
  width: 140px;
  height: 30px;
  bottom: 5px;
  left: calc(50% - 70px);
  border-radius:4px ;
}
.btnWrap {
  position: fixed;
  width: 100%;
  bottom: 0;
}
.foot {
  display: flex;
  height: 50px;
  background-color: #fff;
  justify-content: space-around;
}
.professionalSub {
  width: 140px;
  height: 30px;
  border-radius:4px ;
}
.radio {
  width: 100px;
  display: flex;
  justify-content: space-between;
  height: 20px;
  /* position: absolute; */
  /* top: 8px; */
  /* right: 10px; */
}
>>>.van-collapse-item__content {
  padding: 8px 15px 8px 30px;
}
.instructions .explain {
  background-color: #fff;
  min-height: 48px;
  display: flex;
  align-items: center;
  padding: 0 15px;
}
.instructions .explain span {
  font-size: 15px;
  font-weight: 700;
  border-left: 4px solid #29bebc;
  padding-left: 8px;
}
.instructions:last-child {
  border: none;
}
.explan-item {
  padding: 5px 0 5px 10vw;
  display: flex;
  font-size: 14px;
  color: #323233;
}
.labelVal {
  color: #909399;
  margin-left: 2vw;
  flex: 4;
}
.resultImgBox img {
  float: left;
}
.instructions-text {
  flex: 1;
}
.taskProject-item {
  max-height: 70px;
  overflow-y: scroll;
}
.daily-items {
  margin: 10px 0;
  max-height: 80px;
  overflow-y: scroll;
}
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  .block {
    padding: 10px 0 0 0;
    width: 120px;
    height: 110px;
    border-radius: 10px;
    background-color: #ddd;
    text-align: center;
  }
}
</style>