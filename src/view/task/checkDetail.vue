<template>
  <div class="container">
    <van-overlay :show="loading">
      <div class="wrapper">
        <div class="block">
          <van-loading size="70px">加载中...</van-loading>
        </div>
      </div>
    </van-overlay>
    <Header title="巡检详情" @backFun="goBack"> </Header>
    <div v-if="type == 0">
      <div class="header">
        <div class="item">
          <span class="header-title">巡检点名称：</span>
          <span>{{
            pointRelease.taskPointName
          }}</span>
        </div>
      </div>
      <div class="split-line"></div>
      <div class="main">
        <div class="title">
          <span>巡检任务书</span>
        </div>
        <van-collapse v-model="activeNames" :border="false">
          <van-collapse-item :is-link="false" title="" label="" :name="index"
            v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList"
            :key="item.id">

            <template #title>
              <van-icon name="notes-o" color="#29bebc" size="16" />
              <span class="project">巡检内容</span>
              <span style="display: flex;align-items: center;">
                <span class="taskProject-item">{{ item.detailName }}</span>
                <div>
                  <van-radio-group @click.stop.native="clickRadio" v-model="item.status" :name="index" class="radio">
                    <van-radio icon-size="12px" name="0">正常</van-radio>
                    <van-radio icon-size="12px" name="1">异常</van-radio>
                  </van-radio-group>
                </div>
              </span>
            </template>
            <div class="daily-items">标准项：<span>{{item.maintainProjectdetails.standardRequirements}}</span></div>
            <div class="daily-items">参考依据：<span>{{item.maintainProjectdetails.inspectionBasis}}</span></div>
          </van-collapse-item>
        </van-collapse>
      </div>
      <div class="instructions">
        <div class="explain">
          <span>巡检情况说明</span>
        </div>
        <div>
          <van-field @focus="handleFocus" class="inputText" v-model="explainText" type="textarea" :autosize="inputSytle"
            placeholder="请输入巡检情况说明" :clearable="true" :border="false" />
        </div>
      </div>
      <div class="instructions" style="border: none;">
        <div class="explain" style="justify-content:space-between">
          <span><i style="color:red;margin-right:2px" v-if="executivePhotoIf==1">*</i>现场图片</span>
          <p class="upload-img">支持三张图片></p>
        </div>
        <div style="width: 92%;margin: 0 auto;">
          <van-uploader ref="uplodImg" v-model="files" multiple :after-read="afterRead" @delete="deleteImg"
            :max-count="3" />
        </div>
      </div>
      <div class="btnWrap">
        <div class="foot">
          <van-button :disabled="newStatus" class="professionalSub" color="#00cac8" size="large"
            @click="toSubmit('normal')" type="primary" native-type="submit" :loading="subLoading"
            loading-text="正在提交...">提交</van-button>
          <van-button :disabled="!newStatus" class="professionalSub" color="#00cac8" size="large"
            @click="toSubmit('unqualified')" type="primary" native-type="submit" loading-text="正在提交...">不合格提交
          </van-button>
        </div>
      </div>
    </div>
    <div v-if="type == 1">
      <div class="header">
        <div class="item">
          <span class="header-title">巡检点名称：</span>
          <span>{{
            pointRelease.taskPointName
          }}</span>
        </div>
      </div>
      <div class="split-line"></div>
      <div class="main">
        <div class="title">
          <span>巡检任务书</span>
        </div>
        <van-collapse v-model="activeNames">
          <van-collapse-item title="" :name="index"
            v-for="(item, index) in pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList"
            :key="item.id">
            <template #title>
              <van-icon name="notes-o" color="#29bebc" size="16" />
              <span class="project">巡检项目</span>
              <span>{{ item.detailName }}</span>
            </template>
            <div v-for="(item2, inde) in item.maintainProjectdetailsTermReleaseList" :key="item2.id">
              <div class="gist type1" v-if="item2.isNum == '1'">
                <span>{{ inde + 1 }}. 巡检要点：{{
                    item2.content ? item2.content : ""
                  }}</span>
              </div>
              <div class="gist type2" v-if="item2.isNum == '2'">
                <span>{{ inde + 1 }}. 巡检要点：{{
                    item2.content ? item2.content : ""
                  }}</span>
                <van-field @focus="handleFocus" v-model="item2.value" label="" rows="1" type="textarea"
                  placeholder="请输入" @blur="checkField(item2)" :error-message="item2.error" maxlength="50" autosize />
              </div>
              <div class="gist type3" v-if="item2.isNum == '0'">
                <span>{{ inde + 1 }}. 巡检要点：{{
                    item2.content
                      ? item2.content +
                        "(" +
                        item2.rangeStart +
                        "-" +
                        item2.rangeEnd +
                        (item2.einheitName ? item2.einheitName : '') +
                        ")"
                      : ""
                  }}</span>
                <van-field v-model="item2.value" label="" placeholder="请输入" @blur="checkField(item2)"
                  @focus="handleFocus" @input="fieldInput(item2,activeChecked)" :error-message="item2.error" type="number"
                  maxlength="20">
                  <div slot="extra" v-if="item2.outRange" style="color:red">输入范围异常</div>
                </van-field>
              </div>
              <div class="gist type4" v-if="item2.isNum == '3'|| item2.isNum == '4'">
                <span>{{ inde + 1 }}. 巡检要点：{{
                    item2.content ? item2.content : ""
                  }}</span>
                <van-radio-group v-model="activeChecked[index][inde]">
                  <van-radio v-for="item3 in item2.termJson" :key="item3.conText" :name="item3.contText"
                    checked-color="#29bebc" icon-size="16px" @click="tapRadio(item3.contText,item2,activeChecked)">
                    {{ item3.contText }}</van-radio>
                </van-radio-group>
              </div>
            </div>
          </van-collapse-item>
        </van-collapse>
      </div>
      <div class="instructions">
        <div class="explain">
          <span>巡检情况说明</span>
        </div>
        <div>
          <van-field class="inputText" v-model="explainText" type="textarea" :autosize="inputSytle"
            placeholder="请输入巡检情况说明" :clearable="true" :border="false" />
        </div>
      </div>
      <div class="instructions" style="border: none;">
        <div class="explain" style="justify-content:space-between">

          <span><i style="color:red;margin-right:2px" v-if="executivePhotoIf==1">*</i>现场图片</span>
          <p class="upload-img">支持三张图片></p>
        </div>
        <div style="width: 92%;margin: 0 auto;">
          <van-uploader ref="uplodImg" v-model="files" multiple :after-read="afterRead" @delete="deleteImg"
            :max-count="3" />
        </div>
      </div>
      <div class="btnWrap">
        <div class="foot">
          <van-button :disabled="outRangeNew || outRangeNewInput" class="professionalSub" color="#00cac8" size="large"
            @click="toSubmit('normal')" type="primary" native-type="submit" :loading="subLoading"
            loading-text="正在提交...">合格提交</van-button>
          <van-button :disabled="!outRangeNew && !outRangeNewInput" class="professionalSub" color="#00cac8" size="large"
            @click="toSubmit('unqualified')" type="primary">不合格提交</van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import topNav from "../components/topNav.vue";
  import ImageCompressor from 'image-compressor.js'
  import axios from 'axios'
  export default {
    components: {
      topNav,
    },
    data() {
      return {
        loading: false,
        subLoading: false,
        type: null,
        id: "",
        excute: "",
        result: "",
        pointRelease: {
          maintainProjectRelease: {
            maintainProjectdetailsReleaseList: [],
          },
        },
        loginInfo: '',
        activeNames: [],
        active: '',
        inputSytle: {
          'minHeight': 90,
          'minWidth': 300
        },
        files: [],
        radio: '',
        explainText: '', // 情况说明
        errMsg: '',
        from: '',
        checked: false,
        isLocation: '',
        attachmentUrl: [], //图片地址
        activeChecked: [],
        outRangeNew: false,
        newStatus: false,
        executivePhotoIf: '',
        outRangeNewInput:false
      };
    },
    created() {
      this.id = this.$route.query.id;
      this.active = this.$route.query.active;
      this.type = this.$route.query.type;
      this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
      this.isLocation = sessionStorage.getItem('whetherLocation') || 1
      this.from = this.$route.query.from || "";
      this.getData();
    },
    mounted() {
      this.sysClickBack()
      api.addEventListener({
        name: 'gotasks'
      }, (ret, err) => {
        if (this.from == 'scanCodeSub') {
          api.closeFrame({});
        } else {
          this.$router.go(-1)
        }
      })
    },
    methods: {
      handleFocus(event) {
        const input = event.target;
        // 将输入框滚动到可视区域
        input.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'nearest'
        });
      },
      // 查询是否可以选择相册内的图片
      inquireIsPhotograph() {
        const url = __PATH.BASEURL + '/hospital/configuration/hospitalConfiguration/getIsPhotograph'
        const params = {
          unitCode: this.loginInfo.unitCode,
          hospitalCode: this.loginInfo.hospitalCode,
          name: 'executivePhoto'
        }
        return axios.post(url, params)
      },
      sysClickBack() {
        api.addEventListener({
          name: 'keyback666'
        }, (ret, err) => {
          this.goBack()
        })
      },
      async getData() {
        const isPhotoAlbum = await this.inquireIsPhotograph()
        this.executivePhotoIf = isPhotoAlbum.data.data.value
        this.loading = true
        let params = {
          id: this.id,
        };
        this.axios.postContralHostBase("getCheckDetail", params, (res) => {
          if (res.code == "200") {
            const {
              excute,
              result,
              pointRelease
            } = res.data;
            this.excute = excute;
            this.result = result;
            this.pointRelease = pointRelease;
            // 日常巡检增加默认选项
            if (pointRelease.maintainProjectRelease.equipmentTypeId == '0') {
              pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach(i => {
                i.status = '0'
              })
            }
            pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i, index) => {
              this.activeNames.push(index)
              const iii = []
              i.maintainProjectdetailsTermReleaseList.forEach(j => {
                if (j.isNum == '3') {
                  j.termJson.forEach(k => {
                    if (k.isDefault == '0') {
                      iii.push(k.contText)
                    }
                  })
                } else if (j.isNum == '4') {
                  j.termJson.forEach(k => {
                    if (k.isDefault == '0') {
                      this.outRangeNew = true
                      iii.push(k.contText)
                    } else {
                      this.outRangeNew = false
                    }
                  })
                } else {
                  iii.push('')
                }
              })
              this.activeChecked.push(iii)
              console.log(this.activeChecked);
            })
            this.loading = false
          }
        });
      },
      goBack() {
        if (this.from == 'scanCodeSub') {
          api.closeFrame({});
        } else if (this.from == 'scanCode') {
          this.$router.go(-1)
        } else {
          this.$router.push({
            path: 'taskDetail',
            query: {
              active: this.active,
              id: this.pointRelease.taskId,
              from: this.from
            }
          })
        }
      },
      // 图片压缩
      compressImage(file) {
        return new Promise((resolve, reject) => {
          new ImageCompressor(file.file, {
            quality: 0.6,
            checkOrientation: false,
            success(res) {
              let file = new window.File([res], res.name, {
                type: res.type
              })
              resolve(file)
            },
            error(e) {
              reject();
            }
          })
        })
      },
      // 图片选择
      async afterRead(files) {
        this.files.forEach(i => {
          return i.status = 'uploading'
        })
        if (files.length) {
          let formData = new FormData()
          for (let j = 0; j < files.length; j++) {
            const subFile = await this.compressImage(files[j])
            formData.append('file', subFile)
          }
          formData.append("hospitalCode", this.loginInfo.hospitalCode);
          axios.post(__PATH.BASEURL + 'file/upload', formData).then((res) => {
            if (res.data.code == 200) {
              this.files.forEach(i => {
                return i.status = 'done'
              })
              const imgUrl = res.data.data.fileKey.split(',')
              imgUrl.forEach((i, index) => {
                const item = {
                  name: files[index].file.name,
                  fileKey: i
                }
                this.attachmentUrl.push(item)
              })
            }
          }).catch(() => {
            this.files.forEach(i => {
              return i.status = 'failed'
            })
            this.$toast.fail('上传失败')
          })
        } else {
          const subFile = await this.compressImage(files)
          this.subImg({
            file: subFile
          })
        }
      },
      subImg(params) {
        this.axios.postContralHostBase('uploadImg', params, res => {


          if (res.code == '200') {
            console.log(params, 'params');
            console.log(res, 'resres');
            const item = {
              name: params.file.name,
              fileKey: res.data.fileKey
            }
            this.attachmentUrl.push(item)
            this.files.forEach(i => {
              return i.status = 'done'
            })
          }
        })
      },
      //删除图片
      deleteImg(e) {
        console.log(e, 'eeee');

        this.attachmentUrl = this.attachmentUrl.filter(i => i.name != e.file.name)
        console.log(this.attachmentUrl, ' this.attachmentUrl');

      },

      toSubmit(type) {
        // 定位标识
        const arr = []
        this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach((i, ind) => {
          if (this.type == 0) { // 日常
            const item = {}
            i.maintainProjectdetailsTermReleaseList.forEach(j => {
              item.id = j.id
            })
            if (i.status) {
              item.normal = i.status
            }
            arr.push(item)
          } else {
            i.maintainProjectdetailsTermReleaseList.forEach((item2, index) => {
              if (item2.isNum == "3" || item2.isNum == "4") {
                let obj = {
                  id: item2.id,
                  value: this.activeChecked[ind][index],
                  normal: ''
                };
                arr.push(obj);
              } else if (item2.isNum != "1") {
                let obj = {
                  id: item2.id,
                  value: item2.value,
                  normal: ''
                };
                arr.push(obj);
              }
            })
          }
        })
        const attachmentUrl = []
        this.attachmentUrl.forEach(i => {
          attachmentUrl.push(i.fileKey)
        })
        if (this.attachmentUrl.length == 0 && this.executivePhotoIf == 1) {
          this.$toast.fail('请上传现场图片')
          return
        } else {
          const params = {
            taskPointReleaseId: this.pointRelease.maintainProjectRelease.taskPointReleaseId,
            state: '2',
            taskId: this.pointRelease.taskId,
            spyScan: this.isLocation,
            answerMapList: JSON.stringify(arr),
            details: this.explainText,
            staffName: this.loginInfo.name,
            attachmentUrl: this.attachmentUrl.length > 0 ? attachmentUrl.join(',') : ''
          }
          const checked = JSON.parse(params.answerMapList).every(i => i.normal)
          //  console.log(checked,'checkedchecked');
          
          if (this.type == '0') {
           this.subMit(params)
          } else {
            this.checked = JSON.parse(params.answerMapList).every(i => i.value)
            if (this.checked) {
              if (type == 'normal') {
                // this.subLoading = true
                this.subMit(params)
              } else if (type == 'unqualified') {
                // this.subLoading = true
                params.state = '3'
                this.subMit(params)
                // this.errSubmit()
              }
            } else {
              this.$toast.fail('请先完成任务书！')
            }
          }
        }

      },
      subMit(params) {
        params.submitLocation = api.getPrefs({
          sync: true,
          key: 'locationInfo'
        }) || ''
        const normal = JSON.parse(params.answerMapList)
        if (this.type == 0 && normal.some(i => i.normal == '1')) { // 日常任务提交且有异常
          params.state = '3'
        }
        // 设置提交等待
        this.subLoading = true
        try {
          this.axios.postContralHostBase("inspectionSubmit", params, (res) => {
            if (res.code == "200") {
              let that = this;
              this.$toast.success({
                message: "执行成功!",
                duration: 1500,
                onClose() {
                  if (that.type == 0 && normal.some(i => i.normal == '1')) { // 日常任务提交且有异常
                    that.errSubmit()
                  } else if (that.type == 1 && params.state == '3') {
                    that.errSubmit()
                  } else {
                    that.goBack()
                  }
                },
              });
            }
            this.subLoading = false
          })
        } catch (err) {
          this.subLoading = false
          this.$toast.fail('执行失败，请重试！')
        }
      },
      errSubmit() {
        api.openWin({
          name: 'imas/snapshot',
          url: 'widget://html/common_window.html',
          bgColor: 'rgba(250, 250, 250, 0)',
          hideHomeIndicator: true,
          bounces: false,
          scrollEnabled: false,
          useWKWebView: true,
          pageParam: {
            title: '隐患上报',
            unqualifiedId: this.pointRelease.id,
            fromPage: 'inspection'
          }
        });
      },
      formatter(val, e) {

        if (Number(val) >= Number(e.rangeStart) && Number(val) <= Number(e.rangeEnd)) {
          this.errMsg = ''
        } else if (Number(val) < Number(e.rangeStart) || Number(val) > Number(e.rangeEnd)) {
          this.errMsg = "输入范围异常"
        }
        return val
      },
      fieldInput(item2) {
        if (item2.value &&
         ( item2.value < parseInt(item2.rangeStart) ||
          item2.value > parseInt(item2.rangeEnd)) 
        ) {
          this.outRangeNewInput = true
        } else {
          this.outRangeNewInput = false
        }
      },
      checkField(item2) {
        if (item2.value &&
          item2.value < parseInt(item2.rangeStart) ||
          item2.value > parseInt(item2.rangeEnd)
        ) {
          item2.outRange = true
        } else {
          item2.outRange = false
        }
        if (!item2.value || item2.value.length == 0) {
          item2.error = "内容不能为空";
        } else {
          item2.error = "";
        }
        console.log(item2.outRange, 'item2.outRangeitem2.outRangeitem2.outRange');

        this.$forceUpdate();
      },
      tapRadio(content, item, obj) {
        console.log(obj,'itemitem');
        
        if (item.isNum == 4) {
          const hasDefaultZero = obj.some(subArray => {
            return subArray.some(obj1 => obj1 == '异常');
          });
          if (hasDefaultZero) {
            this.outRangeNew = true
          } else {
            this.outRangeNew = false
          }

        }

        this.$forceUpdate();
      },
      clickRadio() {
        this.newStatus = this.pointRelease.maintainProjectRelease.maintainProjectdetailsReleaseList.some(item => item
          .status == 1)
        this.$forceUpdate()

      }
    },
    filters: {
      transResult(val) {
        if (val == "2") return "合格";
        if (val == "3") return "不合格";
        if (val == "4") return "异常报修";
      },
    }
  };

</script>

<style lang="scss" scoped>
  #app {
    overflow: hidden;
  }

  .container {
    width: 100vw;
    background-color: #fff;
    padding-bottom: 15vh;
  }

  .header {
    background-color: #fff;
    border-bottom: 4px solid #ebedf0;
  }

  .item {
    min-height: 48px;
    padding-left: 15px;
    display: flex;
    align-items: center;
  }

  .item span:nth-child(1) {
    display: inline-block;
    padding-left: 8px;
    font-size: 15px;
    font-weight: 700;
    border-left: 4px solid #29bebc;
    color: #353535;
    min-width: 110px;
  }

  .item span:nth-child(2) {
    font-size: 15px;
    color: #888888;
  }

  >>>.van-collapse {
    overflow: auto;
  }

  .main {
    background-color: #fff;
    border-bottom: 4px solid #ebedf0;
  }

  .main .title {
    height: 54px;
    display: flex;
    align-items: center;
    padding: 0 15px;
  }

  .main .title span {
    font-size: 15px;
    font-weight: 700;
    border-left: 4px solid #29bebc;
    padding-left: 8px;
  }

  >>>.van-cell__title {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .project {
    min-width: 58px;
    margin: 0 12px 0 5px;
  }

  >>>.van-field {
    border-bottom: 1px solid #dddddd;
  }

  .gist {
    color: #353535;
    margin-bottom: 10px;
  }

  .van-radio-group {
    padding-left: 8%;
    margin-top: 8px;
  }

  >>>.van-cell {
    align-items: center;
  }

  >>>.van-cell::after {
    border-bottom: none;
  }

  >>>.van-collapse-item--border::after {
    display: none;
  }

  >>>.van-hairline--top-bottom::after {
    border-bottom: none;
  }

  .van-radio {
    margin-bottom: 8px;
  }

  .foot-box {
    position: fixed;
    width: 100%;
    bottom: 24px;
  }

  .foot {
    display: flex;
    background-color: #fff;
    justify-content: space-around;
  }

  >>>.van-button {
    border-radius: 5px;
    width: 20vw;
  }

  .split-line {
    width: 100vw;
    height: 10px;
    background-color: #f5f6fb;
  }

  >>>.van-dialog__footer {
    display: none;
  }

  .dialog-foot {
    height: 48px;
    display: flex;
    border-top: 1px solid #ebedf0;
  }

  .dialog-foot span {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    color: #353535;
  }

  .dialog-foot span:nth-child(2) {
    color: #29bebc;
    border-left: 1px solid #ebedf0;
    border-right: 1px solid #ebedf0;
  }

  .dialog-info {
    color: #353535;
    text-align: center;
  }

  .popup-item {
    font-size: 18px;
    text-align: center;
    color: #353535;
  }

  .popup-cancel {
    font-size: 17px;
    text-align: center;
    color: #888888;
  }

  .van-cell__value {
    text-align: center;
  }

  .van-popup {
    padding-top: 18px;
  }

  .van-cell {
    padding: 12px 16px;
  }

  .van-cell::after {
    border: none;
  }

  .cell-cancel {
    border-top: 10px solid #f5f6fb;
  }

  .instructions {
    border-bottom: 4px solid #ebedf0;
  }

  .instructions .explain {
    background-color: #fff;
    min-height: 48px;
    display: flex;
    align-items: center;
    padding: 0 15px;
  }

  .instructions .explain span {
    font-size: 15px;
    font-weight: 700;
    border-left: 4px solid #29bebc;
    padding-left: 8px;
  }

  .inputText {
    width: 100%;
    padding: 10px 30px;
  }

  .upload {
    background-color: #fff;
  }

  .upload>span {
    display: inline-block;
  }

  .upload-title {
    display: block;
    display: flex;
    justify-content: space-between;
  }

  .upload-img {
    padding: 0 16px 0 0;
    display: inline-block;
    font-size: 13px;
    color: #888888;
  }

  .dailySub {
    position: absolute;
    width: 140px;
    height: 30px;
    bottom: 5px;
    left: calc(50% - 70px);
    border-radius: 4px;
  }

  .btnWrap {
    position: fixed;
    width: 100%;
    bottom: 0;
  }

  .foot {
    display: flex;
    height: 50px;
    background-color: #fff;
    justify-content: space-around;
  }

  .professionalSub {
    width: 140px;
    height: 30px;
    border-radius: 4px;
  }

  .daily-items {
    margin: 10px 0;
    max-height: 80px;
    overflow-y: scroll;
  }

  .radio {
    width: 100px;
    display: flex;
    justify-content: space-between;
    height: 20px;
    /* position: absolute;
  top: 8px;
  right: 10px; */
  }

  >>>.van-collapse-item__content {
    padding: 8px 15px 8px 30px;
  }

  .taskProject-item {
    max-height: 70px;
    overflow-y: scroll;
  }

  .wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    .block {
      padding: 10px 0 0 0;
      width: 120px;
      height: 110px;
      border-radius: 10px;
      background-color: #ddd;
      text-align: center;
    }
  }

</style>
