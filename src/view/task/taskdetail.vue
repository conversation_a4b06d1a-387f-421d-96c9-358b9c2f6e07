<template>
  <div class="container">
    <van-overlay :show="overlayShow">
      <div class="wrapper">
        <div class="block">
          <van-loading size="70px">加载中...</van-loading>
        </div>
      </div>
    </van-overlay>
    <Header title="任务详情" @backFun="goBack"> </Header>
    <van-notify v-model="show" background="black" type="success">
      定位初始化中,请稍后，请检查是否开启蓝牙
      </van-notify>
    <div class="task-wrap">
      <div class="task-box task-name">
        <span style="min-width: 65px">任务名称:</span>
        <span>{{ from == 'inform' ? $route.query.taskName : taskInfo.taskName }}</span>
      </div>
      <div class="task-box task-time">
        <span>巡检时间:</span>
        <!-- <span
          >{{ taskInfo.taskStartTime | transDate }}至{{
            taskInfo.taskEndTime | transDate
          }}</span
        > -->
        <span>{{ from == 'inform' ? $route.query.taskStartTime : taskInfo.taskStartTime }}
          至 {{ from == 'inform' ? $route.query.taskEndTime : taskInfo.taskEndTime}}</span>
      </div>
      <div class="task-box task-time">
        <span>周期类型:</span>
        <span>{{ cycleTypeFn(from == 'inform' ? $route.query.cycleType : taskInfo.cycleType) }}</span>
      </div>
    </div>
    <step-line :data="data"></step-line>
  </div>
</template>

<script>
import topNav from "../components/topNav.vue";
import stepLine from "../components/stepLine.vue";
import moment from "moment"
import axios from 'axios'
export default {
  components: {
    topNav,
    stepLine,
  },
  data() {
    return {
      taskInfo: "",
      active: 0,
      data: [],
      cycleTypeArr:[
        {
          cycleType:8,
          label:'单次'
        },
        {
          cycleType:6,
          label:'每日'
        },
        {
          cycleType:0,
          label:'每周'
        },
        {
          cycleType:2,
          label:'每月'
        },
        {
          cycleType:3,
          label:'季度'
        },
        {
          cycleType:5,
          label:'全年'
        },
      ],
      role: "",
      scanFlag: "", // 0开启扫码 1关闭扫码
      scanInfo: "",
      ibeacon: "",
      ibeaconArr: [],
      isLocation: false,
      isScaned: false,
      loginInfo: "",
      from: "",
      giveUpLocation: "",
      show: false,
      scanData: [],
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
      overlayShow: false,
      photoAlbum: '1' // 1：是 2：否
    };
  },
  async created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    this.taskInfo = JSON.parse(sessionStorage.getItem("taskInfo"))
    // this.ibeacon = api.require("brightBeacon");
    // this.taskInfo = this.$route.query.taskInfo || "";
    this.active = this.$route.query.active || 0;
    this.role = this.$route.query.role || "";
    this.scanFlag = this.$route.query.scanFlag || "";
    this.from = this.$route.query.from || "";
    this.getData();
    this.getAuth()
  },
  mounted() {
    this.giveUpLocation = sessionStorage.getItem("giveUpLocation") || "false";
    this.ibeaconArr = JSON.parse(sessionStorage.getItem("ibeaconArr")) || [];
  },
  activated() {
    console.log('activated')
    this.taskInfo = JSON.parse(sessionStorage.getItem("taskInfo"))
    this.getData()
    // // this.taskInfo = this.$route.query.taskInfo || "";
    this.active = this.$route.query.active || 0;
    // // this.role = this.$route.query.role || "";
    this.scanFlag = this.$route.query.scanFlag || "";
    // // this.isScaned = false;
    // // this.isLocation = false;
    this.from = this.$route.query.from || "";
    this.sysClickBack()
    // this.scanData = this.$route.query.scanData
  },
  methods: {
    sysClickBack() {
      api.addEventListener({
        name:'keyback666'
      },(ret,err) => {
        this.goBack()
      })
    },
    getData() {
      this.overlayShow = true
      let params = {
        taskId: ''
      }
      if (this.from == 'inform') {
        params.taskId = this.$route.query.taskId
      } else {
        params.taskId = this.taskInfo.id
      }
      //巡检点
      this.axios.postContralHostBase("getTaskPointDetail", params, (res) => {
        if (res.code == "200") {
          this.data = res.data;
          this.overlayShow = false
        }
      });
    },
    goBack() {
      if (this.$route.query.isFireOperation) {
        this.$router.go(-1)
      } else {
        if (this.from == 'tasks') {
          this.$router.push({
            path: "tasks",
            query: {
              active: this.active
            },
          });
        } else if (this.from == 'inspectionRecord') { //记录
          this.$router.push({
            path: "inspectionRecord",
            query: {
              active: this.active,
            },
          });
        } else if (this.from == 'scanCode') { //扫码多任务跳转
          this.$router.push({
            path: "tasks",
            query: {
              scanData: this.active,
              from: 'scanCode'
            },
          });
        } else { //扫码单任务跳转
          api.closeFrame({});
        }
      }
    },
    async goDetail(
        taskId,
        id,
        ibeacon,
        engineerCode,
        carryOutFlag,
        roomCode,
        executeOrder,
        maintainProjectRelease,
        taskPointId,
        sourceId,
        location
      ) {
      if (this.from == 'inspectionRecord') {
        let type = null
        if (maintainProjectRelease && maintainProjectRelease.equipmentTypeName == '日常巡检') {
          type = 0
        } else if (maintainProjectRelease && maintainProjectRelease.equipmentTypeName == '专业巡检') {
          type = 1
        } else {
          type = 2
        }
        const queryData = {
          id,
          active: this.active,
          type,
          from: this.from,
        }
        if (this.$route.query.isFireOperation) {
          queryData.isFireOperation = true
        }

        this.$router.push({
          path: "recordDetail",
          query: queryData
        });
      } else if (this.from == 'tasks' || this.from == 'scanCode' || this.from == 'taskDetailScanCode' || this.from == 'inform') {
        // 已执行任务
        if (carryOutFlag != "0") {
          return this.$toast("该巡检点已完成，无需再次进行操作。");
        }
        // if (this.taskInfo.status == '2') {
        if (this.taskInfo.taskStatus == 1 && moment(this.taskInfo.taskEndTime).unix() < moment().unix() ) {
          return this.$toast("已超期，无法执行！");
        }
        let dialogConfirm = "";
        // 非顺序执行任务
        if (executeOrder != "start" && this.taskInfo.sortFlag == '0') {
          dialogConfirm = await this.$dialog
            .confirm({
              title: "提示",
              message:
                "该任务需要按顺序执行，请返回上一任务点执行工作。如果继续执行请点击继续执行。",
              confirmButtonColor: "#00c5c0",
              confirmButtonText: "继续执行",
              cancelButtonText: "忽略",
            })
            .then(() => {
              return "confirm";
            })
            .catch(() => {
              return "cancel";
            });
        }
        if (dialogConfirm == "cancel") return;
          const item = {
            taskPointRelease: {
            id: id
          },
          id: taskId
        }
        if (this.taskInfo.scanFlag == "1") { // 不扫码
          if (this.taskInfo.locationFlag == "0") { // 定位
            this.getLocation(item, maintainProjectRelease, false,location)
          } else { // 不扫码不定位
            if (maintainProjectRelease) { // 有任务书
              let type = null
              if (maintainProjectRelease.equipmentTypeName == '日常巡检') {
                type = 0
              } else if (maintainProjectRelease.equipmentTypeName == '专业巡检') {
                type = 1
              }
              this.$router.push({
                path: "checkDetail",
                query: {
                  id,
                  active: this.active,
                  type,
                  from: this.from
                },
              });
            } else { // 无任务书
              this.noBookSubmit(item, 1)
            }
          }
        } else if (this.taskInfo.scanFlag == "0") { //扫码
          if (this.taskInfo.locationFlag == "0") { // 扫码且定位
            if (sourceId == sessionStorage.getItem('source')) {
              this.getLocation(item, maintainProjectRelease, false,location)
            } else {
              this.APPScan(id, taskPointId, ibeacon, engineerCode, roomCode, true);
              this.getLocation(item, maintainProjectRelease, true,location)
            }
          } else { // 只扫码不定位
            if (sourceId == sessionStorage.getItem('source')) {
              if (maintainProjectRelease) { // 有任务书
                let type = null
                if (maintainProjectRelease.equipmentTypeName == '日常巡检') {
                  type = 0
                } else if (maintainProjectRelease.equipmentTypeName == '专业巡检') {
                  type = 1
                }
                this.$router.push({
                  path: "checkDetail",
                  query: {
                    id,
                    active: this.active,
                    type,
                    from: this.from
                  },
                });
              } else { // 无任务书
                this.noBookSubmit(item, 1)
              }
            } else {
              this.APPScan(id, taskPointId, ibeacon, engineerCode, roomCode, false);
            }
          }
        }
      }
    },
    getScanData(id, flag) {
      if (this.scanInfo == "") return;
      let params = {
        typeValue: this.scanInfo,
        taskId: this.taskInfo.id,
      };
      this.axios.postContralHostBase("getPerformTask", params, (res) => {
        if (res.code != 200) return;
        if (res.data.length == 1) {
          if (!res.data[0].taskPointRelease.maintainProjectRelease) {
            console.log('无任务书')
            if (flag) {
              const whetherLocation = sessionStorage.getItem('whetherLocation')
              this.noBookSubmit(res.data[0], whetherLocation)
            } else {
              this.noBookSubmit(res.data[0], 1);
            }
          } else {
            console.log('有任务书')
            let type = null
            if (res.data[0].taskPointRelease.maintainProjectRelease.equipmentTypeName == '日常巡检') {
              type = 0
            } else if (res.data[0].taskPointRelease.maintainProjectRelease.equipmentTypeName == '专业巡检') {
              type = 1
            }
            this.$toast.success('扫码成功')
            this.$router.push({
              path: "checkDetail",
              query: {
                id,
                active: this.active,
                type,
                from: this.from
              },
            });
          }
        } else if (res.data.length > 1) {
          this.$toast("该二维码关联了" + res.data.length + "个任务");
        } else {
          this.$toast("该二维码无关联任务");
        }
      });
    },
    // 无任务书提交
    noBookSubmit(taskData, location) {
      let params = {
        taskPointReleaseId: taskData.taskPointRelease.id,
        state: "2",
        taskId: taskData.id,
        staffId: this.loginInfo.id,
        staffName: this.loginInfo.name,
        spyScan: location,
        isBookEmpty: true,
      };
      this.axios.postContralHostBase("inspectionSubmit", params, (res) => {
        console.log('响应', res)
        if (res.code == "200") {
          let that = this;
          this.$toast.success({
            message: "执行成功!",
            duration: 1000,
            onClose() {
              that.getData();
            },
          });
        }
      });
    },
    // 查询是否可以选择相册内的图片
    inquireIsPhotograph() {
      const url = __PATH.BASEURL +'/hospital/configuration/hospitalConfiguration/getIsPhotograph'
      const params = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        name:"isPhotograph"
      }
      return axios.post(url,params)
    },
    //APP扫码
    async APPScan(id, taskPointId, ibeacon, engineerCode, roomCode, flag) {
      console.log('扫码-----')
      if (this.taskInfo.locationFlag == "0" && ibeacon.length != 0) {
        ibeacon.forEach((item) => {
          this.ibeaconArr.forEach((item2) => {
            if (item.deviceMinor == item2.minor) {
              this.isLocation = true;
            }
          });
        });
      }
      const YBS = this.utils;
      if (!YBS.hasPermission("storage")) {
        let pageParam = {
          title: "存储权限使用说明",
          cont: "用于存储图片、视频等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["storage"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
              if (!YBS.hasPermission("camera")) {
              YBS.reqPermission(["camera"], function (ret) {
                if (ret && ret.list.length > 0 && ret.list[0].granted) {
                }
              });
              return;
              }
            }
          });
        })
        return;
      }
      if (!YBS.hasPermission("camera")) {
        let pageParam = {
          title: "摄像机权限使用说明",
          cont: "用于扫描巡检码、空间码、拍照上传等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["camera"], function (ret) {
          if (ret && ret.list.length > 0 && ret.list[0].granted) {
          }
        });
        })
        return;
      }
      try {
        const isPhotoAlbum = await this.inquireIsPhotograph()
        // 校验是否配置可从相册选择
        if (isPhotoAlbum.data.code == '200') {
          this.photoAlbum = isPhotoAlbum.data.data.value
        }
        this.utils.scanCode(this.photoAlbum).then(
          (item) => {
            if (item && item.length) {
              // this.pageLoading = true;
              // this.getEquipmentByIdFn(item[3]);
              this.scanInfo = item.join(",");
              if (
                taskPointId == item[3] ||
                engineerCode == item[3] ||
                roomCode == item[3]
              ) {
                this.getScanData(id, flag);
              } else {
                this.$toast.fail("二维码不正确");
              }
            } else {
              this.$toast.fail("未查找到相关设备");
            }
          },
          () => {
            this.$toast.fail("无效的二维码,请检查二维码");
          }
        );
      } catch (e) {
        this.$toast.fail(e);
      }
    },
    // 定位
    getLocation(taskData, maintainProjectRelease, scanFlag, location) {
      let that = this;
      let whetherLocation = sessionStorage.getItem("whetherLocation") || "";
      let giveUpLocation = sessionStorage.getItem("giveUpLocation") || "";
      let ibeaconArr = JSON.parse(sessionStorage.getItem("ibeaconArr")) || "";
      this.ibeacon = api.require('brightBeacon');
      if (
        whetherLocation == "true" ||
        giveUpLocation == "true" ||
        ibeaconArr.length > 0
      ) {
      } else {
        setTimeout(() => {
          that.show = false;
        }, 2000);
      }
      if (this.ibeacon) {
        this.ibeacon.startRanging(
          {
            uuids: ['E2C56DB5-DFFB-48D2-B060-D0F5A71096E0','FDA50693-A4E2-4FB1-AFCF-C6EB07647825']
          }, (res, err) => {
            this.ibeaconArr = res.list.map(i => "_" + i.minor)
            if (this.ibeaconArr.length == 0) {
              this.ibeacon.stopRanging();
              this.$toast('无法获取定位信息，请开启蓝牙或重新扫码')
              sessionStorage.setItem("whetherLocation", 3);
            } else {
              sessionStorage.setItem("ibeaconArr", JSON.stringify(this.ibeaconArr));
              this.ibeaconArr.forEach(i => {
                whetherLocation = location.filter(item => item.deviceUuid + "_" + item.deviceMinor != i)
              })
              //whetherLocation 1：未开启定位 2:定位成功 3：定位失败
              if (whetherLocation.length > 0) {
                sessionStorage.setItem("whetherLocation", 2);
              }
            }
            this.show = false
            this.ibeacon.stopRanging();
            if (!scanFlag) { // 不扫码
              sessionStorage.setItem("whetherLocation", 1)
              if (maintainProjectRelease) { // 有任务书
                let type = null
                if (maintainProjectRelease.equipmentTypeName == '日常巡检') {
                  type = 0
                } else if (maintainProjectRelease.equipmentTypeName == '专业巡检') {
                  type = 1
                }
                this.$router.push({
                  path: "checkDetail",
                  query: {
                    id: taskData.taskPointRelease.id,
                    active: this.active,
                    type,
                    from: this.from
                  },
                });
              } else { // 无任务书
                this.noBookSubmit(taskData, 1)
              }
            }
          }
        );
      } else {
        this.show = false
        sessionStorage.setItem("whetherLocation", 3);
        if (!scanFlag) { // 不扫码
          sessionStorage.setItem("whetherLocation", 1)
          if (maintainProjectRelease) { // 有任务书
            let type = null
            if (maintainProjectRelease.equipmentTypeName == '日常巡检') {
              type = 0
            } else if (maintainProjectRelease.equipmentTypeName == '专业巡检') {
              type = 1
            }
            this.$router.push({
              path: "checkDetail",
              query: {
                id: taskData.taskPointRelease.id,
                active: this.active,
                type,
                from: this.from
              },
            });
          } else { // 无任务书
            this.noBookSubmit(taskData, 1)
          }
        }
      }
      
    },
    // 格式化周期类型
    cycleTypeFn(row) {
      const item = this.cycleTypeArr.filter(i => i.cycleType == row)
      return item[0].label
    },
    // 权限获取
    getAuth() {
      const YBS = this.utils;
      if (!YBS.hasPermission("storage")) {
        let pageParam = {
          title: "存储权限使用说明",
          cont: "用于存储图片、视频等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["storage"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
              if (!YBS.hasPermission("camera")) {
              YBS.reqPermission(["camera"], function (ret) {
                if (ret && ret.list.length > 0 && ret.list[0].granted) {
                }
              });
              return;
            }
          }
        });
        })
        return;
      }
      if (!YBS.hasPermission("camera")) {
        let pageParam = {
          title: "摄像机权限使用说明",
          cont: "用于扫描巡检码、空间码、拍照上传等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["camera"], function (ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
            }
          });
        })
        return;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.task-wrap {
  padding: 10px 0;
  border-bottom:5px solid #e6eaf0;
}
.task-box {
  min-height: 26px;
  display: flex;
  align-items: center;
  padding: 2px 20px;
}
.task-box span:nth-child(1) {
  width: 65px;
  font-size: 14px;
  color: #333333;
  line-height: 18px;
}
.task-box span:nth-child(2) {
  font-size: 13px;
  color: #888888;
  padding-left: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  .block {
    padding: 10px 0 0 0;
    width: 120px;
    height: 110px;
    border-radius: 10px;
    background-color: #ddd;
    text-align: center;
  }
}
</style>