<template>
  <div>
    <Header title="异常反馈" @backFun="backFn"> </Header>
    <div class="check-info">
      <div class="title">
        <span>巡检信息</span>
      </div>
      <!-- <div class="check-box">巡检消息详情页</div> -->
      <van-collapse v-model="activeNames">
        <van-collapse-item
          title=""
          :name="index"
          v-for="(item, index) in projList.maintainProjectdetailsReleaseList"
          :key="item.id"
          @tap.stop="toggle(index)"
        >
          <template #title>
            <van-icon name="notes-o" color="#29bebc" size="16" />
            <span class="project">巡检项目</span>
            <span class="van-ellipsis" style="width: 56%">{{
              item.detailName
            }}</span>
            <van-checkbox
              :name="index"
              ref="checkboxes"
              v-model="item.isCheck"
              @click.native.stop
              checked-color="#00c5c1"
            />
          </template>
          <div
            v-for="(item2, index) in item.maintainProjectdetailsTermReleaseList"
            :key="item2.id"
          >
            <div class="gist type1" v-if="item2.isNum == '1'">
              <span>{{ index + 1 }}. 巡检要点：{{ item2.content||'' }}</span>
            </div>
            <div class="gist type2" v-if="item2.isNum == '2'">
              <span>{{ index + 1 }}. 巡检要点：{{ item2.content||'' }}</span>
              <van-field
                v-model="item2.value"
                label=""
                placeholder="请输入"
                disabled
                type="textarea"
                autosize
              />
            </div>
            <div class="gist type3" v-if="item2.isNum == '0'">
              <span
                >{{ index + 1 }}. 巡检要点：{{
                  (item2.content||'') +
                  "(" +
                  item2.rangeStart +
                  "-" +
                  item2.rangeEnd +
                  item2.einheitName +
                  ")"
                }}</span
              >
              <van-field
                v-model="item2.value"
                label=""
                placeholder="请输入"
                disabled
              />
            </div>
            <div class="gist type4" v-if="item2.isNum == '3'">
              <span>{{ index + 1 }}. 巡检要点：{{ item2.content||'' }}</span>
              <van-radio-group v-model="item2.radio">
                <van-radio
                  v-for="item3 in item2.termJson"
                  :key="item3.conText"
                  :name="item3.contText"
                  checked-color="#29bebc"
                  icon-size="16px"
                  disabled
                  >{{ item3.contText }}</van-radio
                >
              </van-radio-group>
            </div>
          </div>
        </van-collapse-item>
      </van-collapse>
    </div>
    <div class="btn">
      <van-button size="large" @click="toSubmit" type="primary" color="#00c5c1"
        >提交</van-button
      >
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      type: "",
      taskPointInfo: "",
      spyScan: "",
      activeNames: [],
      pointRelease: {
        maintainProjectRelease: {
          maintainProjectdetailsReleaseList: [],
        },
      },
      bookArr: [],
      projList: {
        maintainProjectdetailsReleaseList: [],
      },
      id:'',
      active:'',
      taskInfo:'',
      role:'',
      scanFlag:''
    };
  },
  created() {
    this.type = this.$route.query.type || "";
    this.taskPointInfo = this.$route.query.taskPointInfo || "";
    this.projList = this.$route.query.projList || "";
    this.pointRelease = this.taskPointInfo;
    this.id = this.$route.query.id;
    this.from = this.$route.query.from;
    this.active = this.$route.query.active;
    this.taskInfo = this.$route.query.taskInfo;
    this.role = this.$route.query.role;
    this.scanFlag = this.$route.query.scanFlag;
  },
  activated() {
    this.type = this.$route.query.type || "";
    this.taskPointInfo = this.$route.query.taskPointInfo || "";
    this.projList = this.$route.query.projList || "";
    this.spyScan = this.$route.query.spyScan;
  },
  methods: {
    toggle(index) {
      console.log("123", index);
      this.$refs.checkboxes[index].toggle();
    },
    toSubmit() {
      let bookArr = [];
      this.projList.maintainProjectdetailsReleaseList.forEach((item) => {
        if (item.isCheck) {
          bookArr.push(item);
        }
      });
      this.bookArr = bookArr;
      this.$router.push({
        path: "feedback",
        query: {
          type: this.type,
          taskPointInfo: this.taskPointInfo,
          bookArr,
          spyScan: this.spyScan,
          projList: this.projList,
          fromPage: "bookSelect",
        },
      });
    },
    backFn() {
      this.$router.push({
        path: "inspectionContent",
        query: {
          id: this.id,
          from: this.from,
          active: this.active,
          taskInfo: this.taskInfo,
          role: this.role,
          scanFlag: this.scanFlag,
        },
      });
    },
  },
};
</script>

<style scoped>
.container {
  width: 100vw;
  background-color: #f5f6fb;
}
.task-point > div,
.check-info .title,
.check-execute > div,
.check-status > div {
  padding-left: 16px;
  height: 54px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e6eaf0;
}
.title span {
  display: inline-block;
  font-size: 16px;
  color: #353535;
  font-weight: 600;
  border-left: 4px solid #29bebc;
  padding-left: 8px;
}
.task-point .left,
.check-execute .left,
.check-status .left {
  font-size: 15px;
  color: #353535;
  width: 120px;
  /* margin-right: 12px; */
}
.task-point .right,
.check-execute .right,
.check-status .right {
  font-size: 14px;
  color: #888888;
}
.check-box {
  box-sizing: border-box;
  height: 232px !important;
  background-color: #f2f6ff;
  border: 2px solid #d6dbe3 !important;
  margin: 16px !important;
  padding: 0 !important;
  justify-content: center;
  font-size: 15px;
  color: #888888;
  margin-bottom: 0 !important;
}
.check-info {
  padding-bottom: 16px;
}
.check-status {
  margin-bottom: 0;
}
.project {
  margin: 0 12px 0 5px;
}
/* >>> .van-collapse {
  height: 48vh!important;
  overflow: auto;
} */
.gist {
  color: #353535;
  margin-bottom: 10px;
}
>>> .van-field {
  border-bottom: 1px solid #dddddd;
}
>>> .van-cell::after {
  border-bottom: none;
}
>>> .van-collapse-item--border::after {
  display: none;
}
>>> .van-hairline--top-bottom::after {
  border-bottom: none;
}
.van-radio {
  margin-bottom: 8px;
}
.van-radio-group {
  padding-left: 8%;
  margin-top: 8px;
}
.van-checkbox {
  display: inline-block;
  position: absolute;
  right: 16vw;
  top: 27%;
}
.btn {
  width: 100%;
  position: fixed;
  bottom: 1vh;
  display: flex;
  justify-content: center;
}
.van-button {
  width: 98vw;
}
>>> .van-cell__title {
  display: flex;
}
</style>