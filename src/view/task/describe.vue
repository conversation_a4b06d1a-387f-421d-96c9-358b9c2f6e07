<template>
  <div class="container">
    <Header :title="type + '描述'" @backFun="goBack"> </Header>
    <div class="describe">
      <span class="list-title">{{ type }}描述</span>
      <van-field
        @input="questionDescription = questionDescription.replace(regStr, '')"
        v-model="questionDescription"
        rows="2"
        autosize
        label=""
        type="textarea"
        maxlength="120"
        :rules="[{ required: true, message: '请输入投诉描述' }]"
        placeholder="请输入投诉描述，字数在120字以内，或直接语音发布，限时60秒"
      />
      <sounds-recording @getRecord="getRecord" @getRecordFile="getRecordFile" />
      <div class="list-item">
        <div class="list-flex list-between">
          <div class="list-title">上传附件</div>
          <div class="list-text">注：照片最多上传三张</div>
        </div>
        <div style="margin-top: 20px">
          <van-uploader
            :max-size="maxSize * 1024 * 1024"
            @oversize="onOversize"
            accept="image/*"
            :disabled="fileMessage == '上传中'"
            v-model="fileList"
            max-count="3"
            :after-read="afterImgRead"
          />
        </div>
      </div>
    </div>
    <div class="feed-btn">
      <van-button
        size="large"
        @click="toSubmit"
        type="primary"
        native-type="submit"
        :loading="loading"
        loading-text="正在提交..."
        >提交</van-button
      >
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      type: "",
      questionDescription: "",
      regStr:
        /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi,
      recordingInfo: "",
      params: "",
      questionDescription: "",
      loading: false,
      maxSize: 5,
      fileMessage: "",
      fileList: [],
      recordingInfoFile: "",
      taskInfo: "",
      role: "",
      scanFlag: "",
      source: "",
      typeValue: "",
      active:''
    };
  },
  created() {
    this.params = this.$route.query.params || "";
    this.taskInfo = this.$route.query.taskInfo || "";
    this.role = this.$route.query.role || "";
    this.scanFlag = this.$route.query.scanFlag || "";
    this.source = this.$route.query.source || "";
    this.typeValue = this.$route.query.typeValue || "";
    this.active = this.$route.query.active || "";
    this.taskData = this.$route.query.taskData || "";
    let type = this.$route.query.type;
    if (type == "pass") {
      this.type = "合格";
    } else {
      this.type = "不合格";
    }
  },
  methods: {
    getRecord(info) {
      this.recordingInfo = info;
      console.log("desc录音路径", info);
    },
    getRecordFile(info) {
      this.recordingInfoFile = info;
      console.log("desc录音文件", info);
    },
    // 图片上传成功回调
    afterImgRead(file) {
      file.status = "uploading";
      file.message = "上传中...";
      this.fileMessage = "上传中";
      // this.fileName=file.file.name;
      this.handleUploadImg(file);
    },
    onOversize() {
      this.$toast.fail(`文件大小不能超过${this.maxSize}M`);
    },
    handleUploadImg(file) {
      let params = {
        file: file.file,
      };
      this.axios.postContralHostUrlOne("uploadFileToOSS", params, (res) => {
        const { code, data, message } = res;
        if (code == 200) {
          file.status = "success";
          file.message = "上传成功";
          this.fileMessage = "上传成功";
          this.fileList[this.fileList.length - 1].url = data;
        }
      });
    },
    goBack() {
      this.$router.push({
        path: "inspectionContent",
        query: {
          active: this.active,
          taskInfo: this.taskInfo,
          role: this.role,
          scanFlag: this.scanFlag,
          taskData:this.taskData
        },
      });
    },
    toSubmit() {
      let params = this.params;
      params.details = this.questionDescription;
      params.callerTapeUrl = this.recordingInfo;
      console.log("描述语音", params.callerTapeUrl);
      const urls = this.fileList.length
        ? this.fileList.map((item) => {
            return item.url;
          })
        : [];
      params.attachmentUrl = urls.join(",");
      console.log(params);
      this.axios.postContralHostBase("inspectionSubmit", params, (res) => {
        if (res.code == "200") {
          let that = this;
          this.$toast.success({
            message: "提交成功!",
            duration: 1000,
            onClose() {
              if (that.source == "bigScan") {
                that.$router.push({
                  path: "tasks",
                  query: {
                    active: that.active,
                    taskInfo: that.taskInfo,
                    typeValue: that.typeValue,
                    from: "bigScan",
                    fromPage: "inspectionContent",
                  },
                });
              } else if (that.source == "bigScanOneData") {
                that.$router.go(-2);
              } else {
                that.$router.push({
                  path: "taskDetail",
                  query: {
                    active: that.active,
                    taskInfo: that.taskInfo,
                    role: that.role,
                    scanFlag: that.scanFlag,
                  },
                });
              }
            },
          });
        }
      });
    },
  },
};
</script>

<style scoped>
.container {
  width: 100vw;
}
.describe {
  height: 80vh;
  overflow: auto;
}
.describe > span {
  display: flex;
  font-size: 14px;
  height: 40px;
  align-items: center;
  padding-left: 16px;
}
.list-item {
  padding: 0 14px;
  position: relative;
}
.list-item::after {
  position: absolute;
  box-sizing: border-box;
  content: " ";
  pointer-events: none;
  right: 0;
  bottom: 0;
  left: 0;
  border-bottom: none;
  transform: scaleY(0.5);
}
.list-flex {
  display: flex;
  align-items: center;
}
.list-between {
  justify-content: space-between;
}
.list-title {
  font-size: 15px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #353535;
  margin-right: 20px;
  width: 80px;
  margin: 14px 0;
}
.list-text {
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #888888;
}
.van-cell {
  margin-bottom: 14px;
}
.van-cell::after {
  border-bottom: none;
}
.feed-btn {
  padding: 10px 16px;
  border-top: 1px solid #f5f6fb;
}
>>> .van-button--primary {
  background-color: #00cac8;
  border: 1px solid #00cac8;
  border-radius: 8px;
}
</style>