<template>
  <div class="container">
    <Header title="动火许可证" @backFun="goBack"> </Header>
    <div class="title" style="position: relative; margin-top: 8px">
      动火许可证
      <i class="el-icon-download right_t" @click="download"></i>
    </div>
    <div class="bh">编号：{{ list.hotWorkNumber }}</div>

    <table cellspacing="5" cellpadding="5" style="border-collapse: collapse; width: 95%; text-align: center; font-family: '宋体'; margin: 0 auto">
      <tr>
        <td class="td1">工程名称</td>
        <td colspan="5">
          {{ list.projectName || "" }}
        </td>
        <!-- <td class="td1">动火地点</td>
              <td class="td2" colspan="2">333</td> -->
      </tr>
      <tr>
        <td class="td1">申请动火<br />单位</td>
        <td colspan="5">{{ list.constructionUnitName || "" }}</td>
      </tr>
      <tr>
        <td class="td1">动火部位</td>
        <td colspan="5">{{ list.hotSpot || "" }}</td>
      </tr>
      <tr>
        <td class="td1">动火方式</td>
        <td colspan="2">{{ list.hotModeName || "" }}</td>
        <td class="td1">动火人特种<br />作业证编号</td>
        <td colspan="2" style="word-break: break-all">{{ list.certificateNumber || "" }}</td>
      </tr>
      <tr>
        <td class="td1">动火作业<br />起止时间</td>
        <td colspan="5">{{ list.hotTime || "" }}</td>
      </tr>
      <tr>
        <td class="td1">动火内容</td>
        <td colspan="5">{{ list.hotWorkContent || "" }}</td>
      </tr>
      <tr>
        <td class="td1">现场消防<br />安全措施</td>
        <td colspan="5" style="word-break: break-all">
          {{ list.fireSafetyMeasures || "" }}
        </td>
      </tr>
      <tr>
        <td class="td1">配备灭火<br />器材</td>
        <td colspan="5">
          {{ list.fireExtinguishingEquipment || "" }}
        </td>
      </tr>
      <tr>
        <td class="td1">动火人(签字)<br /></td>
        <td colspan="2">
          {{ list.hotPersonName }}
        </td>
        <td class="td1">监护人(签字)<br /></td>
        <td colspan="2">
          {{ list.tutelageName }}
        </td>
      </tr>
      <tr>
        <td class="td1">动火现场负责人<br />意见(签字)</td>
        <td colspan="5" class="right">
          <template v-if="list.node1 && list.node1.length > 0">
            <img class="img-preview" v-for="(item, index) in list.node1" :key="index" :src="'data:image/png;base64,' + item" />
          </template>
        </td>
      </tr>
      <tr>
        <td class="td1">监理单位现场负责<br />人意见(签字)</td>
        <td colspan="5" class="right">
          <template v-if="list.node2 && list.node2.length > 0">
            <img class="img-preview" v-for="(item, index) in list.node2" :key="index" :src="'data:image/png;base64,' + item" />
          </template>
        </td>
      </tr>
      <tr>
        <td class="td1" rowspan="2">单位施工管理部门<br />负责人(签字)</td>
        <td colspan="5" class="right">
          <template v-if="list.node3 && list.node3.length > 0">
            <img class="img-preview" v-for="(item, index) in list.node3" :key="index" :src="'data:image/png;base64,' + item" />
          </template>
        </td>
      </tr>
      <tr>
        <td colspan="5" class="right">
          <template v-if="list.node4 && list.node4.length > 0">
            <img class="img-preview" v-for="(item, index) in list.node4" :key="index" :src="'data:image/png;base64,' + item" />
          </template>
        </td>
      </tr>
      <tr>
        <td class="td1" rowspan="3">动火批准人意见<br />(签字)</td>
        <td colspan="5" class="right" style="height: 43px">
          <template v-if="list.node5 && list.node5.length > 0">
            <img class="img-preview" v-for="(item, index) in list.node5" :key="index" :src="'data:image/png;base64,' + item" />
          </template>
        </td>
      </tr>
      <tr>
        <td colspan="5" class="right" style="height: 43px">
          <template v-if="list.node6 && list.node6.length > 0">
            <img class="img-preview" v-for="(item, index) in list.node6" :key="index" :src="'data:image/png;base64,' + item" />
          </template>
        </td>
      </tr>
      <tr>
        <td colspan="5" class="right" style="height: 43px">
          <template v-if="list.node7 && list.node7.length > 0">
            <img class="img-preview" v-for="(item, index) in list.node7" :key="index" :src="'data:image/png;base64,' + item" />
          </template>
        </td>
      </tr>
      <tr>
        <td class="td1">动火完工验收及动<br />火批准人(签字)</td>
        <td colspan="5" class="right">
          <template v-if="list.node8 && list.node8.length > 0">
            <img class="img-preview" v-for="(item, index) in list.node8" :key="index" :src="'data:image/png;base64,' + item" />
          </template>
        </td>
      </tr>
    </table>

    <div class="remarks"><span>备注：</span><span>动火作业管理部门,消防安全归口管理部门，动火操作人各保留一份</span></div>
    <div style="font-size: 14px; padding: 0 2.5%">
      <div class="title" style="line-height: 50px">《动火许可证》管理要求</div>
      <div style="text-indent: 2em; font-weight: 600">一、对于存在以下情况的，不予批准《动火许可证》;</div>
      <div class="list">
        <div>1.动火操作人员不具有相应资格;</div>
        <div>2.室外动火遇有5级(含5级)以上风力时;</div>
        <div>3.作业现场与非施工区未做有效的防火分离;</div>
        <div>4.作业现场防火措施为落实;</div>
        <div>5.与刷漆、喷漆、脱漆等易燃操作同时间、同部位上下交叉作业;</div>
        <div>6.有限空间作业条件达不到国家标准规定的安全要求;</div>
        <div>7.其他不符合安全动火作业的情形;</div>
      </div>
      <div style="text-indent: 2em; font-weight: 600">二、发生以下任何一种情况,应立即终止施工动火作业，需要继续作业的,重新办理《动火许可证》;</div>
      <div class="list">
        <div>1.作业环境和条件发生变化;</div>
        <div>2.作业内容发生变化;</div>
        <div>3.现场发现重大安全隐患;</div>
        <div>4.动火人或监护人有临时调整的;</div>
        <div>5.已经发生事故;</div>
      </div>
      <div class="foot-box">
        <div class="bom">
          <div>{{ list.hospitalName || "" }}</div>
          <div>{{ list.dateTime }}</div>
        </div>
        <img v-if="list.superviseSeal" :src="'data:image/png;base64,' + list.superviseSeal" />
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";

export default {
  data() {
    return {
      list: "",
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    setTimeout(() => {
      api.addEventListener(
        {
          name: "keyback666",
        },
        (ret, err) => {
          this.goBack();
        }
      );
    }, 100);
  },
  activated() {},
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        overlay: true,
        message: "加载中...",
      });
      let params = {
        hospitalName: this.loginInfo.hospitalName,
        id: this.$route.query.id,
      };
      this.axios.postContralHostBase("getHotWorkPermit", params, (res) => {
        this.$toast.clear();
        if (res.code == 200) {
          this.list = res.data;
        } else {
          this.$toast.fail(res.message);
        }
      });
    },
    download() {
      let params = {
        hospitalName: this.loginInfo.hospitalName,
        id: this.$route.query.id,
        hospitalCode: this.loginInfo.hospitalCode,
        unitCode: this.loginInfo.unitCode,
      };
      // 加载中
      this.$toast.loading({
        duration: 0,
        forbidClick: true,
        overlay: false,
        message: "下载中...",
      });
      let _this = this;
      axios({
        method: "post",
        url: __PATH.BASEURL + "fireApplicationHospital/hotWorkPermitDownloadWordApp",
        data: params,
      }).then(function (res) {
        // 关闭加载中
        _this.$toast.clear();
        api.download(
          {
            url: res.data.data,
            report: true,
            cache: true,
            allowResume: true,
          },
          function (ret, err) {
            if (ret.state == 1) {
              if (api.systemType == "ios") {
                api.openWin({
                  name: "my/pdfview",
                  url: "widget://html/common_window.html",
                  bgColor: "rgba(250, 250, 250, 0)",
                  hideHomeIndicator: true,
                  bounces: false,
                  scrollEnabled: false,
                  useWKWebView: true,
                  pageParam: {
                    title: "文件预览",
                    savePath: ret.savePath,
                    webUrl: ret.savePath,
                  },
                });
              } else {
                // 安卓
                // 文档

                var docReader = api.require("docReader");
                docReader.open(
                  {
                    path: ret.savePath,
                    autorotation: false,
                  },
                  function (ret, err) {
                    console.log(JSON.stringify(ret));
                    console.log(JSON.stringify(err));
                  }
                );
              }
              console.log("下载成功");
            } else {
              console.log("下载失败");
            }
          }
        );
      });
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  max-width: 100vw;
  overflow-x: auto;
  height: 100vh;
  background-color: #fff;
  .title {
    text-align: center;
    font-size: 16px;
    font-weight: 600;
  }
}
table,
tr,
th,
td {
  background-color: #fff;
  border: 1px solid #000;
  font-size: 12px;
}
table {
  table-layout: fixed;
}
.td1 {
  min-width: 96px;
}
.right {
  text-align: right;
}
.remarks {
  font-size: 10px;
  line-height: 20px;
  margin-left: 2.5%;
}
.bh {
  text-align: right;
  margin-right: 15%;
  font-size: 12px;
  line-height: 20px;
}
.list {
  text-indent: 2em;
  > div {
    line-height: 25px;
  }
}
.right_t {
  font-size: 20px;
  position: absolute;
  right: 10px;
  top: 5px;
}
.bom {
  text-align: right;
}
.img-preview {
  width: 100%;
  height: 100%;
}
.foot-box {
  display: flex;
  margin-top: 20px;
  align-items: center;
  justify-content: flex-end;
}
.foot-box img {
  width: 80px;
  height: 80px;
  margin-left: 12px;
}
</style>
