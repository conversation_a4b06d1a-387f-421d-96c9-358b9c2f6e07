<template>
  <div class="container">
    <Header title="动火检查" @backFun="goBack"> </Header>
    <van-popup v-model="showStartTimePicker" position="bottom">
      <van-datetime-picker v-model="currentDate" type="datetime" @confirm="onStartTimeConfirm" @cancel="showStartTimePicker = false" />
    </van-popup>
    <van-popup v-model="showEndTimePicker" position="bottom">
      <van-datetime-picker v-model="currentDate2" type="datetime" :min-date="currentDate" @confirm="onEndTimeConfirm" @cancel="showEndTimePicker = false" />
    </van-popup>
    <van-tabs @click="tabclick" color="#29BEBC">
      <van-tab title="待检查" name="0"></van-tab>
      <van-tab title="检查历史" name="3"></van-tab>
    </van-tabs>
    <div class="tool-box" v-if="answerType == '3'">
      <div class="time-box">
        <i class="line"></i>
        <div class="start-time" @click="showStartTimePicker = true">
          <span v-if="startTime">{{ startTime }}</span>
          <span v-else style="color:#86909C">请选择开始时间</span>
        </div>
        <div class="end-time" @click="showEndTimePicker = true">
          <span v-if="endTime">{{ endTime }}</span>
          <span v-else style="color:#86909C">请选择结束时间</span>
        </div>
      </div>
      <div class="search-box">
        <van-dropdown-menu active-color="#29BEBC">
          <van-dropdown-item v-model="inspectionState" :options="inspectionStateList" @change="search" />
        </van-dropdown-menu>
        <div class="reset" @click="handleReset">重置</div>
      </div>
    </div>
    <div v-if="list.length > 0" class="hei">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad" style="padding-bottom: 50px;">
          <div v-for="item in list" :key="item.id" class="item" @click="routerUrl(item)">
            <div>
              <div style="border-bottom: 1px solid #f2f4f9;display:flex; justify-content: space-between;line-height: 30px;">
                <div style="display:flex">
                  <div>任务名称：</div>
                  <div>{{ item.taskName || "" }}</div>
                </div>
                <div v-if="answerType == '3'" :style="item.taskStatus == '1' ? 'color:red' : 'color:#03c316'">{{ item.taskStatus == "1" ? "未完成" : "已完成" }}</div>
              </div>
              <div class="child">
                <span>巡检时间：</span>
                <span>{{ item.taskStartTime || "" }}</span>
              </div>
              <div class="child">
                <div>工程名称：</div>
                <div>{{ item.projectName || "" }}</div>
              </div>
              <div class="child">
                <div>建设单位：</div>
                <div>{{ item.constructionUnit || "" }}</div>
              </div>
              <div class="child">
                <div>动火部位：</div>
                <div>{{ item.hotSpot || "" }}</div>
              </div>
              <div class="child">
                <div>动火方式：</div>
                <div>{{ item.hotModeName || "" }}</div>
              </div>
              <div class="child">
                <div>动火时间：</div>
                <div>{{ item.hotStartTime || "" }}-{{ item.hotEndTime || "" }}</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <van-empty class="custom-image" :image="require('../../assets/images/noData.png')" description="" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      current: 1,
      size: 10,
      total: 0,
      answerType: "0",
      list: [],
      showStartTimePicker: false,
      showEndTimePicker: false,
      startTime: "",
      endTime: "",
      inspectionState: "",
      inspectionStateList: [
        { text: "巡检状态", value: "" },
        { text: "未完成", value: "1" },
        { text: "已完成", value: "2" }
      ],
      currentDate: new Date(),
      currentDate2: new Date()
    };
  },
  created() {
    setTimeout(() => {
      this.getList();
      api.addEventListener(
        {
          name: "keyback666"
        },
        (ret, err) => {
          this.goBack();
        }
      );
    }, 100);
  },
  methods: {
    onRefresh() {
      this.current = 1;
      this.finished = false;
      this.loading = true;
      this.list = [];
      this.getList();
    },
    onLoad() {
      this.finished = false;
      this.loading = true;
      this.current++;

      this.getList();
    },
    tabclick(val) {
      this.answerType = val;
      this.loading = true;
      this.finished = false;
      this.current = 1;
      this.list = [];
      this.getList();
    },
    handleReset() {
      this.startTime = "";
      this.endTime = "";
      this.inspectionState = "";
      this.search();
    },
    search() {
      this.current = 1;
      this.finished = false;
      this.loading = true;
      this.list = [];
      this.getList();
    },
    getList() {
      let params = {
        accomplishType: this.answerType, //任务状态
        taskStartTime: this.startTime, //开始时间
        taskEndTime: this.endTime, //结束时间
        pageNo: this.current,
        pageSize: this.size,
        taskStatus: this.inspectionState //巡检状态
      };
      this.axios.postContralHostBase("getFireOperationTaskList", params, res => {
        this.loading = false;
        if (res.code == 200) {
          this.list = this.list.concat(res.data.list)
          this.finished = this.list.length >= res.data.sum
          this.loading = false
        } else {
          this.finished = true;
          this.loading = false;
          this.$toast.fail(res.message);
        }
      });
    },
    routerUrl(list) {
      sessionStorage.setItem("taskInfo", JSON.stringify(list));
      this.$router.push({
        path: "taskDetail",
        query: {
          taskInfo: list,
          active: "",
          from: this.answerType == 0 ? "tasks" : "inspectionRecord",
          isFireOperation: true
        }
      });
    },
    onType(val) {
      this.form.time = moment(val).format("YYYY-MM-DD");
      this.showType = false;
    },
    goBack() {
      api.sendEvent({
        name: "refreshList"
      });
      api.closeFrame({});
    },
    onStartTimeConfirm(val) {
      console.log(val);
      // 将时间格式化成yyyy-MM-dd HH:mm格式，不足两位补0
      let year = val.getFullYear();
      let month = val.getMonth() + 1;
      let day = val.getDate();
      let hour = val.getHours();
      let minute = val.getMinutes();
      month = month < 10 ? "0" + month : month;
      day = day < 10 ? "0" + day : day;
      hour = hour < 10 ? "0" + hour : hour;
      minute = minute < 10 ? "0" + minute : minute;
      this.startTime = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":00";
      this.showStartTimePicker = false;
      this.search();
    },
    onEndTimeConfirm(val) {
      console.log(val);
      // 将时间格式化成yyyy-MM-dd HH:mm格式，不足两位补0
      let year = val.getFullYear();
      let month = val.getMonth() + 1;
      let day = val.getDate();
      let hour = val.getHours();
      let minute = val.getMinutes();
      month = month < 10 ? "0" + month : month;
      day = day < 10 ? "0" + day : day;
      hour = hour < 10 ? "0" + hour : hour;
      minute = minute < 10 ? "0" + minute : minute;
      this.endTime = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":59";
      this.showEndTimePicker = false;
      this.search();
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #f2f4f9;
  .item {
    overflow: auto;
    background-color: #fff;
    margin: 5px 0.1875rem 0.1875rem;
    padding: 8px 16px;
    border-radius: 0.125rem;
    font-size: 14px;
    color: #1d2129;
    font-family: PingFang SC-Medium;
    line-height: 20px;
    border-bottom: 1px solid #eee;
    border-radius: 8px;
    .accoutName {
      display: flex;
      justify-content: space-between;
      > div:nth-child(1) {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .child {
      display: flex;
      padding: 5px 0;
      > div:nth-child(1) {
        width: 80px;
      }
      > div:nth-child(2) {
        width: calc(100% - 80px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .child2 {
      // display: flex;
      text-align: right;
      margin-top: 5px;
    }
  }
  .hei {
    margin-top: 5px;
    height: calc(100% - 144px);
    padding-bottom: 40px;
    overflow: auto;
    padding: 0 10px;
  }
  .notList {
    position: relative;
    height: calc(90% - 1.24rem);
    .emptyImg {
      position: absolute;
      height: 100%;
      width: 50%;
      left: 25%;
      // background: url('../../../assets/images/equipmentManagement/缺省@2x.png') 0 40% no-repeat;
      background-size: 100% auto;
      .emptyText {
        position: absolute;
        width: 100%;
        text-align: center;
        bottom: 40%;
      }
    }
  }
}
.tool-box {
  width: 100%;
  height: 100px;
  background-color: #fff;
  position: sticky;
  top: 104px;
  border-bottom: 1px solid #e5e6eb;
  padding: 0 16px;
  padding-top: 12px;
  box-sizing: border-box;
  z-index: 9;
}
.time-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
}
.time-box > div {
  width: 45%;
  height: 35px;
  background-color: #f2f3f5;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #1d2129;
  border-radius: 4px;
}
.line {
  position: absolute;
  width: 10px;
  height: 2px;
  top: 50%;
  left: 48%;
  background-color: #c9cdd4;
}
.search-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .van-cell::after {
    border: none;
  }
  > .van-dropdown-menu {
    width: 38%;
  }
}
.search-box .van-field {
  width: 60%;
  margin-top: 5px;
}
.reset {
  width: 30%;
  font-size: 15px;
  color: #1d2129;
  text-align: center;
}
/deep/ .van-dropdown-menu__bar {
  box-shadow: none !important;
}
/deep/ .van-cascader__header {
  display: none;
}
.van-tabs {
  position: sticky;
  top: 60px;
  z-index: 9;
}
</style>
