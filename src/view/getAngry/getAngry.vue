<template>
  <div class="container">
    <Header title="动火结束上报" @backFun="goBack"> </Header>
    <div class="box">
      <div
        style="font-size:16px;
  margin-bottom:15px ;
      "
      >
        <span style="color:red;font-size:12px"> *</span> 请建设单位动火后检查清理建筑施工垃圾，并上传现场照片和视频
      </div>
      <div style="display:flex">
        <van-uploader
          :after-read="afterRead"
          v-model="fileList"
          :max-size="maxSize * 1024 * 1024"
          @oversize="onOversize"
          @delete="beforeDel"
          upload-text="添加图片"
          accept="image/*"
          :disabled="fileMessage == '上传中'"
          max-count="5"
        >
          <template v-if="fileMessage == '上传成功'" #preview-cover="{ file }">
            <!-- <div class="preview-cover van-ellipsis">上传成功</div> -->
          </template>
        </van-uploader>
        <!-- <br /> -->
        <van-uploader
          :after-read="afterRead2"
          v-model="videoList"
          :max-size="50 * 1024 * 1024"
          :before-read="beforeRead"
          @oversize="onOversize2"
          @delete="beforeDel2"
          upload-text="添加视频"
          accept="video/*"
          max-count="1"
          @click-preview="handleclicksc"
        >
          <template v-if="videoMessage == '上传成功'" #preview-cover="{ file }">
            <video style="width:120px;height:120px;object-fit:cover;" :src="videourl" v-if="videourl"></video>

            <!-- <div class="preview-cover van-ellipsis">上传成功</div> -->
          </template>
        </van-uploader>
      </div>
      <div class="upload-tip">注：最多上传5张照片，单张大小≤20M,1个1分钟以内的视频</div>
      <van-field v-model="finishTranscript" rows="2" label="文字记录" type="textarea" placeholder="请输文字记录内容" required maxlength="200" show-word-limit />
    </div>
    <div class="btn">
      <van-button color="#00cac8" size="large" @click="submit">提交</van-button>
    </div>
    <van-dialog
      style="width:100%;border-radius:0;height:200px"
      theme="default"
      v-model="showvideoplay"
      :show-cancel-button="false"
      :show-confirm-button="false"
      closeOnClickOverlay
    >
      <video controls preload="auto" style="width:100%;height:200px;object-fit: contain;" :src="videourl" v-if="videourl"></video>
    </van-dialog>
  </div>
</template>

<script>
import axios from "axios";
import Vue from "vue";
import { Toast } from "vant";
export default {
  data() {
    return {
      finishTranscript: "",
      disabled: "",
      maxSize: 20,
      videoMaxSize: 50,
      fileList: [],
      fileKey: [],
      fileMessage: "",
      videoList: [],
      videoKey: [],
      videoMessage: "",
      videourl: "",
      showvideoplay: false
    };
  },
  created() {
    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
    setTimeout(() => {
      this.sysClickBack();
    }, 100);
  },
  mounted() {},
  activated() {},
  methods: {
    submit() {
      let params = {
        id: this.$route.query.id,
        finishTranscript: this.finishTranscript
      };
      if (this.fileKey.length) {
        let fileArr = [];
        this.fileKey.forEach(i => {
          fileArr.push(i.fileKey);
        });
        params.finishImageUrl = fileArr.toString();
      } else {
        params.finishImageUrl = "";
      }
      if (this.videoKey.length) {
        let videoArr = [];
        this.videoKey.forEach(i => {
          videoArr.push(i.fileKey);
        });
        params.finishVideoUrl = videoArr.toString();
      } else {
        params.finishVideoUrl = "";
      }
      if (params.finishImageUrl == "") return this.$toast("请上传照片");
      if (params.finishTranscript == "") return this.$toast("请填写文字记录");
      this.axios.postContralHostBase("getFinishFireApplication", params, res => {
        if (res.code == 200) {
          this.$toast.success(res.message);
          this.$router.go(-1);
        } else {
          this.$toast.fail(res.message);
        }
      });
    },
    handleclicksc(file) {
      this.showvideoplay = true;
    },
    afterRead2(file2) {
      this.videoMessage = "上传中";
      this.videourl = file2.content;
      const { file } = file2;
      file2.status = "uploading";
      file2.message = "上传中...";
      this.handleUploadImg2(file, file2);
    },
    handleUploadImg2(file, file2) {
      var formData = new FormData();
      formData.append("file", file);
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      axios.post(__PATH.BASEURL + "file/upload", formData).then(res => {
        if (res.data.code == 200) {
          file2.status = "success";
          file2.message = "上传成功";
          this.fileMessage = "上传成功";
          let urlObij = {};
          urlObij.name = res.data.data.fileName;
          urlObij.fileKey = res.data.data.fileKey;
          this.videoKey.push(urlObij);
        } else {
          file2.status = "failed";
          file2.message = "上传失败";
          this.videoMessage = "上传失败";
        }
      });
    },
    beforeDel2(e) {
      this.videoKey = this.videoKey.filter(i => i.name != e.file.name);
    },
    beforeRead(file) {
      var url = URL.createObjectURL(file);
      var audioElement = new Audio(url);
      var result;
      audioElement.muted = true;
      audioElement.play().then(() => audioElement.pause());
      return new Promise((resolve, reject) => {
        audioElement.addEventListener("loadedmetadata", function() {
          // 视频时长值的获取要等到这个匿名函数执行完毕才产生
          result = audioElement.duration; //得到时长为秒，小数，182.36
          var serce = parseInt(result);
          if (serce > 60) {
            audioElement.muted = false;
            Toast.fail("视频不能超过1分钟");
            reject();
          } else {
            audioElement.muted = false;
            resolve();
          }
        });
      });
    },

    onOversize() {
      this.$toast.fail(`图片大小不能超过${this.maxSize}M`);
    },
    onOversize2() {
      this.$toast.fail(`视频大小不能超过${this.videoMaxSize}M`);
    },
    afterRead(file2) {
      this.fileMessage = "上传中";
      const { file } = file2;
      file2.status = "uploading";
      file2.message = "上传中...";
      this.handleUploadImg(file, file2);
    },
    handleUploadImg(file, file2) {
      var formData = new FormData();
      formData.append("file", file);
      formData.append("hospitalCode", this.loginInfo.hospitalCode);
      axios.post(__PATH.BASEURL + "file/upload", formData).then(res => {
        if (res.data.code == 200) {
          file2.status = "success";
          file2.message = "上传成功";
          this.fileMessage = "上传成功";
          let urlObij = {};
          urlObij.name = res.data.data.fileName;
          urlObij.fileKey = res.data.data.fileKey;
          this.fileKey.push(urlObij);
        } else {
          file2.status = "failed";
          file2.message = "上传失败";
          this.fileMessage = "上传失败";
        }
      });
    },
    beforeDel(e) {
      this.fileKey = this.fileKey.filter(i => i.name != e.file.name);
    },
    isDisabled() {
      return this.fileKey.length == "5";
    },
    sysClickBack() {
      api.addEventListener(
        {
          name: "keyback666"
        },
        (ret, err) => {
          this.goBack();
        }
      );
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: 100vh;
  .box {
    padding: 5px 10px;
  }
}
.preview-cover {
  position: absolute;
  bottom: 0;
  box-sizing: border-box;
  width: 100%;
  padding: 4px;
  color: #fff;
  font-size: 12px;
  text-align: center;
  background: rgba(0, 0, 0, 0.3);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.upload-tip {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #696969;
  line-height: 20px;
}
.btn {
  width: 100%;
  position: fixed;
  z-index: 999;
  left: 0;
  bottom: 0;
}
</style>
