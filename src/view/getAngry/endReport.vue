<template>
  <div class="container">
    <Header title="动火结束上报" @backFun="goBack"> </Header>
    <div v-if="list.length > 0" class="hei">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div v-for="item in list" :key="item.id" class="item" @click="routerUrl(item)">
            <div>
              <div class="status">
                <div>
                  <span style="color:#4e5969">工程名称：</span>
                  <span>{{ item.projectName }}</span>
                </div>
                <span v-if="item.hotState == '2'" class="c1">动火后-待结束</span>
                <span v-if="item.hotState == '3'" class="c3">动火后-已结束</span>
              </div>
              <!-- <div class="child">
                <div>工程名称：</div>
                <div>{{ item.projectName || "" }}</div>
              </div> -->
              <div class="child">
                <div>建设单位：</div>
                <div>{{ item.constructionUnit || "" }}</div>
              </div>
              <div class="child">
                <div>施工单位：</div>
                <div>{{ item.constructionUnitName || "" }}</div>
              </div>
              <div class="child">
                <div>动火部位：</div>
                <div>{{ item.hotSpot || "" }}</div>
              </div>
              <div class="child">
                <div>动火方式：</div>
                <div>{{ item.hotModeName || "" }}</div>
              </div>
              <div class="child">
                <div>动火时间：</div>
                <div>{{ item.hotStartTime || "" }}-{{ item.hotEndTime || "" }}</div>
              </div>
              <div class="status2" v-if="item.hotAfterApprovalState">
                <div v-if="item.hotAfterApprovalState == '0'" class="c1">
                  <img src="@/assets/images/tips2.png" />
                  <span>待审批</span>
                </div>
                <div v-if="item.hotAfterApprovalState == '1'" class="c2">
                  <img src="@/assets/images/tips3.png" />
                  <span>审批通过</span>
                </div>
                <div v-if="item.hotAfterApprovalState == '2'" class="c3">
                  <img src="@/assets/images/tips1.png" />
                  <span>审批未通过</span>
                </div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <van-empty class="custom-image" :image="require('../../assets/images/noData.png')" description="" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      current: 1,
      size: 15,
      total: 0,
      list: []
    };
  },
  created() {
    setTimeout(() => {
      api.addEventListener(
        {
          name: "keyback666"
        },
        (ret, err) => {
          this.goBack();
        }
      );
    }, 100);
  },
  mounted() {
    this.getList();
  },
  methods: {
    onLoad() {
      console.log("执行了");
      this.finished = false;
      this.loading = true;
      this.current++;

      this.getList();
    },
    onRefresh() {
      this.current = 1;
      this.finished = false;
      this.loading = true;
      this.list = [];
      this.getList();
    },
    getList() {
      let params = {
        pageNo: this.current,
        pageSize: this.size
      };
      this.axios.postContralHostBase("getFinishFireApplicationList", params, res => {
        console.log(res, "ssssssssssssssss");
        this.loading = false;
        if (res.code == 200) {
          res.data.list.forEach(item => {
            // item.hotAfterApprovalState = "2";
            this.list.push(item);
          });
          if (this.list.length >= res.data.total) {
            this.finished = true;
            this.loading = false;
            return;
          }
        } else {
          this.finished = true;
          this.loading = false;
          this.$toast.fail(res.message);
        }
      });
    },
    routerUrl(list) {
      this.$router.push({
        path: "/getAngry",
        query: {
          id: list.id
        }
      });
    },

    goBack() {
      api.sendEvent({
        name: "refreshList"
      });
      api.closeFrame({});
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  width: 100vw;
  min-height: 100vh;
  background-color: #f2f4f9;
  .item {
    overflow: auto;
    background-color: #fff;
    margin: 10px 0;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 16px;
    color: #1d2129;
    font-family: PingFang SC-Medium;
    line-height: 20px;
    border-bottom: 1px solid #eee;
    .accoutName {
      display: flex;
      justify-content: space-between;
      > div:nth-child(1) {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .child {
      display: flex;
      padding: 5px 0;
      > div:nth-child(1) {
        width: 80px;
        color: #4e5969;
      }
      > div:nth-child(2) {
        color: #1d2129;
        width: calc(100% - 80px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .hei {
    height: calc(100% - 50px);
    // margin-bottom: 40px;
    padding-bottom: 40px;
    overflow: auto;
    padding: 0 10px;
  }
  .notList {
    position: relative;
    height: calc(90% - 1.24rem);
    .emptyImg {
      position: absolute;
      height: 100%;
      width: 50%;
      left: 25%;
      // background: url('../../../assets/images/equipmentManagement/缺省@2x.png') 0 40% no-repeat;
      background-size: 100% auto;
      .emptyText {
        position: absolute;
        width: 100%;
        text-align: center;
        bottom: 40%;
      }
    }
  }
}
.status {
  border-bottom: 1px solid #f2f4f9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 35px;
  > div {
    max-width: 64%;
  }
  > span {
    transform: translateY(-3px);
    color: #f53f3f;
  }
  .c1 {
    color: #3562db !important;
    background-color: #e6effc;
    padding: 4px 6px;
    font-size: 14px;
  }
  .c2 {
    color: #f53f3f !important;
    background-color: #ffece8;
    padding: 4px 6px;
    font-size: 14px;
  }
  .c3 {
    color: #4e5969 !important;
    background-color: #f2f3f5;
    padding: 4px 6px;
    font-size: 14px;
  }
}
.status2 {
  border-top: 1px solid #f2f4f9;
  margin-top: 5px;
  padding: 5px 0;
  > div {
    display: flex;
    align-items: center;
  }
  img {
    width: 14px;
    margin-right: 5px;
  }
  .c1 {
    color: #4e5969 !important;
    font-size: 14px;
  }
  .c2 {
    color: #00b42a !important;
    font-size: 14px;
  }
  .c3 {
    color: #f53f3f !important;
    font-size: 14px;
  }
}
</style>
