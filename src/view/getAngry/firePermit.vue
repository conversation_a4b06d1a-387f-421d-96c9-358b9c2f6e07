<template>
  <div class="container">
    <Header title="动火许可证" @backFun="goBack"> </Header>
    <van-popup v-model="showStartTimePicker" position="bottom">
      <van-datetime-picker v-model="currentDate" type="datetime" @confirm="onStartTimeConfirm" @cancel="showStartTimePicker = false" />
    </van-popup>
    <van-popup v-model="showEndTimePicker" position="bottom">
      <van-datetime-picker v-model="currentDate2" type="datetime" :min-date="currentDate" @confirm="onEndTimeConfirm" @cancel="showEndTimePicker = false" />
    </van-popup>
    <div class="tool-box">
      <div class="time-box">
        <i class="line"></i>
        <div class="start-time" @click="showStartTimePicker = true">
          <span v-if="startTime">{{ startTime }}</span>
          <span v-else style="color:#86909C">请选择开始时间</span>
        </div>
        <div class="end-time" @click="showEndTimePicker = true">
          <span v-if="endTime">{{ endTime }}</span>
          <span v-else style="color:#86909C">请选择结束时间</span>
        </div>
      </div>
      <div class="search-box">
        <van-field v-model="constructionUnit" placeholder="建设单位" @blur="search" />
        <van-field v-model="projectName" placeholder="工程名称" @blur="search" />
        <div class="reset" @click="handleReset">重置</div>
      </div>
    </div>
    <div v-if="list.length > 0" class="hei">
      <van-pull-refresh v-model="isLoading" @refresh="onRefresh" immediate-check="false">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div v-for="item in list" :key="item.id" class="item" @click="routerUrl(item)">
            <div>
              <div class="child">
                <div>工程名称：</div>
                <div>{{ item.projectName || "" }}</div>
              </div>
              <div class="child">
                <div>建设单位：</div>
                <div>{{ item.constructionUnit || "" }}</div>
              </div>
              <div class="child">
                <div>施工单位：</div>
                <div>{{ item.constructionUnitName || "" }}</div>
              </div>
              <div class="child">
                <div>工程属地：</div>
                <div>{{ item.projectTerritory || "" }}</div>
              </div>
              <div class="child">
                <div>建设地址：</div>
                <div>{{ item.constructionAddress || "" }}</div>
              </div>
              <div class="child">
                <div>动火时间：</div>
                <div>{{ item.hotStartTime | handleTime }}&ensp;-&ensp;{{ item.hotEndTime | handleTime }}</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div v-else class="notList">
      <van-empty class="custom-image" :image="require('../../assets/images/noData.png')" description="" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isLoading: false,
      loading: false,
      finished: false,
      refreshing: false,
      current: 1,
      size: 15,
      total: 0,
      list: [],
      startTime: "",
      endTime: "",
      constructionUnit: "",
      showStartTimePicker: false,
      showEndTimePicker: false,
      projectName: "",
      currentDate: new Date(),
      currentDate2: new Date()
    };
  },
  mounted() {
    this.getList();
  },
  created() {
    setTimeout(() => {
      api.addEventListener(
        {
          name: "keyback666"
        },
        (ret, err) => {
          this.goBack();
        }
      );
    }, 100);
  },
  methods: {
    onLoad() {
      this.finished = false;
      this.loading = true;
      this.current++;
      this.getList();
    },
    onRefresh() {
      this.current = 1;
      this.finished = false;
      this.loading = true;
      this.list = [];
      this.getList();
    },
    getList() {
      let params = {
        pageNo: this.current,
        pageSize: this.size,
        hotStartTime: this.startTime,
        hotEndTime: this.endTime,
        constructionUnit: this.constructionUnit,
        projectName: this.projectName
      };
      this.axios.postContralHostBase("getSelectFirePermitList", params, res => {
        console.log(res, "ssssssssssssssss");
        this.loading = false;
        if (res.code == 200) {
          console.log(res, "sssssssssssss");
          res.data.list.forEach(item => {
            this.list.push(item);
          });
          if (this.list.length >= res.data.total) {
            this.finished = true;
            this.loading = false;
            return;
          }
        } else {
          this.finished = true;
          this.loading = false;
          this.$toast.fail(res.message);
        }
      });
    },
    routerUrl(list) {
      this.$router.push({
        path: "/firePrinting",
        query: {
          id: list.id
        }
      });
    },

    goBack() {
      api.sendEvent({
        name: "refreshList"
      });
      api.closeFrame({});
    },
    handleReset() {
      this.startTime = "";
      this.endTime = "";
      this.constructionUnit = "";
      this.projectName = "";
      this.current = 1;
      this.list = [];
      this.getList();
    },
    onStartTimeConfirm(val) {
      console.log(val);
      // 将时间格式化成yyyy-MM-dd HH:mm:ss格式，不足两位补0
      let year = val.getFullYear();
      let month = val.getMonth() + 1;
      let day = val.getDate();
      let hour = val.getHours();
      let minute = val.getMinutes();
      month = month < 10 ? "0" + month : month;
      day = day < 10 ? "0" + day : day;
      hour = hour < 10 ? "0" + hour : hour;
      minute = minute < 10 ? "0" + minute : minute;
      this.startTime = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":00";
      this.showStartTimePicker = false;
      this.list = [];
      this.search();
    },
    onEndTimeConfirm(val) {
      console.log(val);
      // 将时间格式化成yyyy-MM-dd HH:mm:ss格式，不足两位补0
      let year = val.getFullYear();
      let month = val.getMonth() + 1;
      let day = val.getDate();
      let hour = val.getHours();
      let minute = val.getMinutes();
      month = month < 10 ? "0" + month : month;
      day = day < 10 ? "0" + day : day;
      hour = hour < 10 ? "0" + hour : hour;
      minute = minute < 10 ? "0" + minute : minute;
      this.endTime = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":59";
      this.showEndTimePicker = false;
      this.list = [];
      this.search();
    },
    search() {
      this.list = [];
      this.getList();
    }
  },
  filters: {
    handleTime(val) {
      if (val) {
        return val.substring(0, val.length - 3);
      } else {
        return "";
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  width: 100vw;
  min-height: 100vh;
  background-color: #f2f4f9;
  .item {
    overflow: auto;
    background-color: #fff;
    margin: 16px 0.1875rem 0.1875rem;
    padding: 12px;
    border-radius: 8px;
    font-size: 14px;
    color: #1d2129;
    font-family: PingFang SC-Medium;
    line-height: 20px;
    border-bottom: 1px solid #eee;
    .accoutName {
      display: flex;
      justify-content: space-between;
      > div:nth-child(1) {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .child {
      display: flex;
      padding: 5px 0;
      > div:nth-child(1) {
        color: #4e5969;
        width: 80px;
      }
      > div:nth-child(2) {
        color: #1d2129;
        width: calc(100% - 80px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .hei {
    height: calc(100% - 50px);
    padding-bottom: 40px;
    overflow: auto;
    padding: 0 10px;
  }
  .notList {
    position: relative;
    height: calc(90% - 1.24rem);
    .emptyImg {
      position: absolute;
      height: 100%;
      width: 50%;
      left: 25%;
      // background: url('../../../assets/images/equipmentManagement/缺省@2x.png') 0 40% no-repeat;
      background-size: 100% auto;
      .emptyText {
        position: absolute;
        width: 100%;
        text-align: center;
        bottom: 40%;
      }
    }
  }
}
.tool-box {
  z-index: 9;
  width: 100%;
  height: 100px;
  background-color: #fff;
  position: sticky;
  top: 60px;
  border-bottom: 1px solid #e5e6eb;
  padding: 0 16px;
  padding-top: 12px;
  box-sizing: border-box;
}
.time-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
}
.time-box > div {
  width: 45%;
  height: 35px;
  background-color: #f2f3f5;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #1d2129;
  border-radius: 4px;
}
.line {
  position: absolute;
  width: 10px;
  height: 2px;
  top: 50%;
  left: 48%;
  background-color: #c9cdd4;
}
.search-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .van-cell::after {
    border: none;
  }
}
.search-box .van-field {
  width: 40%;
  margin-top: 5px;
}
.reset {
  font-size: 14px;
  color: #1d2129;
}
</style>
