body,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
blockquote,
dl,
dt,
dd,
ul,
ol,
li,
pre,
form,
fieldset,
legend,
button,
input,
textarea,
th,
td {
    margin: 0;
    padding: 0;
}

body,
button,
input,
select,
textarea {
    font: 12px/1.5tahoma, arial, \5b8b\4f53;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: 100%;
}

address,
cite,
dfn,
em,
var {
    font-style: normal;
}

code,
kbd,
pre,
samp {
    font-family: couriernew, courier, monospace;
}

small {
    font-size: 12px;
}

ul,
ol {
    list-style: none;
}

a {
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

sup {
    vertical-align: text-top;
}

sub {
    vertical-align: text-bottom;
}

legend {
    color: #000;
}

fieldset,
img {
    border: 0;
}

button,
input,
select,
textarea {
    font-size: 100%;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

html,
body {
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.left_button {
    width:156px;
    height:44px;
    background:rgba(236,238,248,1);
    border-radius:5px;
    font-size:16px;
    font-family:PingFangSC-Medium;
    font-weight:500;
    color:rgba(53,53,53,1);
}
.through_button {
    width:343px;
    height:44px;
    background:#2d4a74;
    border-radius:5px;
    font-size:16px;
    font-family:PingFangSC-Medium;
    font-weight:500;
    color:rgba(255,255,255,1);
}
.right_button {
    width:156px;
    height:44px;
    background:#2d4a74;
    border-radius:5px;
    font-size:16px;
    font-family:PingFangSC-Medium;
    font-weight:500;
    color:rgba(255,255,255,1);
}
.top_select input{
    border-radius: 0;
    text-align: right;
}
.top_select .el-input__inner {
    border: none;
}
/* .top_select input::-webkit-input-placeholder {
    position:relative;
    left:85%;
} */
.el-select-dropdown {
    left: 0!important;
    margin-top: 0;
    width: 100%;
    border-radius: 0!important;
}
.el-popper {
    margin-top: 0!important;
}
.el-popper[x-placement^=bottom] .popper__arrow::after {
    content: "";
    border: none;
}
.popper__arrow {
    display: none!important;
}
/* .el-select .el-input.is-focus .el-input__inner {
    border: 1px solid rgba(239,239,244,1);
} */
.el-select .el-input.is-focus .el-input__inner {
    border-color: rgba(239,239,244,1)!important;
}
.el-select-dropdown__item.selected {
    color: #888888!important;
    font-weight: 400!important;
}
.top_select .el-input {
    height: 100%;
}
.top_select .el-input input{
    height: 100%;
}
.el-select-dropdown__item {
    height: 49px!important;
    line-height: 49px!important;
}
textarea {
    outline:none;
    resize:none;
    border: none;
    box-sizing: border-box;
    padding: 11px 11px 0 0 ;
    font-size:14px;
    font-family:PingFang-SC-Medium;
    font-weight:500;
    color:rgba(53,53,53,1);
}
.noData {
    width: 50%;
    height: auto;
    margin:10% 25% 0;
}
.noDataDiv {
    text-align: center;
    color: #999;
}
.mint-msgbox-input input {
    width: 95%;
}
.purchase-title {
    font-size:0.88rem;
    font-family:PingFang SC;
    font-weight:500;
    color:rgba(53,53,53,1);
    display: inline-block;
    width: 5.25rem;
}
.green-line {
    font-size:0.94rem;
    font-family:PingFang SC;
    font-weight:bold;
    box-sizing: border-box;
    width: 100%;
    margin-top: 1rem;
}
.sino-block{
    display: inline-block;
    width:0.44rem;
    height:0.44rem;
    background:#2d4a74;
    margin-right: 0.22rem;
}
.gray-div {
    height: 0.63rem;
    width: 100%;
    background-color: rgb(244, 245, 249);
}
.mint-radiolist .mint-cell {
    float: left;
}
.nut-radio input:checked {
    background-color:#2d4a74 !important;
    border-color: #2d4a74 !important;
}
.nut-radio input::after {
    background-color: #2d4a74 !important;
}