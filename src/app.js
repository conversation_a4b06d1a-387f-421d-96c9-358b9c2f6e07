// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from "vue";
import store from "./store";
import App from "./App.vue";
import router from "./router";
import axios from "axios";
import { Toast } from "vant";
import "./vant-ui";
import "mint-ui/lib/style.css";
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
import centerControl from "@/centralControl/centralControl/centralControl"; //引入建造类
import CustomerComponent from "@/components/automaticRegistComponents";
import "@/assets/iconFont/iconfont.css";
import lotusCalendar from "lotus-calendar"; //日历选择
import vueHashCalendar from "vue-hash-calendar";
import dragBall from "vue-drag-ball";
import echarts from "echarts";
import { Checkbox, CheckboxGroup } from "vant";
import { Cascader } from "vant";
import { DropdownMenu, DropdownItem } from "vant";

Vue.use(DropdownMenu);
Vue.use(DropdownItem);
Vue.use(Cascader);
Vue.use(Checkbox);
Vue.use(CheckboxGroup);
Vue.prototype.$echarts = echarts;
// import 'lib-flexible/flexible'
// // 中央事件总线
// window.eventBus = new Vue();   // 注册全局事件对象
// Vue.prototype.$event = new Vue();
// 引入组件CSS样式
import "vue-hash-calendar/lib/vue-hash-calendar.css";
import VCharts from "v-charts";
// 注册组件库
Vue.use(dragBall);
Vue.use(VCharts);
Vue.use(vueHashCalendar);
Vue.use(ElementUI);
Vue.use(CustomerComponent); //自动注册自定义封装组件
Vue.use(lotusCalendar);
Vue.config.productionTip = false;
centerControl.builderFun(Vue.prototype); //起调建造函数

import debounce from "./common/debounce.js";
Vue.directive("debounce", debounce);

const getToken = () => localStorage.getItem("token");
// router.beforeEach((to, from, next) =>{
//   /* 路由发生变化修改页面title */
//   if (to.meta.title) {
//     document.title = to.meta.title;
//   }
//   next();
// });
(Vue.prototype.error = function (data) {}),
  router.beforeEach((to, from, next) => {
    //  判断是否有token
    const hasToken = getToken();
    if (to.meta.title) {
      document.title = to.meta.title;
    }
    if (Vue.prototype.utils.isWechat()) {
      if (to.path == "/") {
        next();
      } else if (to.query.loginInfo) {
        // @ts-ignore
        localStorage.setItem("loginInfo", to.query.loginInfo);
        // @ts-ignore
        localStorage.setItem("token", to.query.token);
        next();
      } else {
        if (hasToken) {
          next();
          // } else {
          //   /* 没有 token 的值 */
          //   next(`/`);
        }
      }
    } else {
      next();
    }
  });
//全局请求响应配置
axios.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);
axios.interceptors.response.use(
  (data) => {
    if (data.data.code !== "200" && data.data.message) {
      Toast.fail(data.data.message);
    }
    return data;
  },

  (error) => {
    Toast.fail("接口似乎走丢了!");
    return Promise.reject(error);
  }
);
/* eslint-disable no-new */
new Vue({
  el: "#app",
  router,
  store,
  components: { App },
  template: "<App/>",
});
