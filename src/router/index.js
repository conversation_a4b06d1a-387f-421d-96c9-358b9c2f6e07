import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

export const constantRoutes = [
  {
    path: "/",
    component: () => import("@/view/home/<USER>")
  },
  {
    path: "/feedback",
    name: "feedback",
    meta: {
      title: "反馈",
      keepAlive: true,
      isUseCache: false
    },
    component: () => import("@/view/feedback/feedbackSubmit/index")
  },
  {
    path: "/addAccessories",
    name: "addAccessories",
    meta: {
      title: "添加配件"
    },
    component: () => import("@/view/feedback/feedbackSubmit/addAccessories")
  },
  {
    path: "/feedbackList",
    name: "feedbackList",
    meta: {
      title: "反馈记录",
      keepAlive: true
    },
    component: () => import("@/view/feedback/feedbackList/feedBackList")
  },
  {
    path: "/reformDetail",
    name: "reformDetail",
    meta: {
      title: "整改"
    },
    component: () => import("@/view/feedback/feedbackList/reformDetail")
  },
  {
    path: "/repairByMeDetail",
    name: "repairByMeDetail",
    meta: {
      title: "自修"
    },
    component: () => import("@/view/feedback/feedbackList/repairByMeDetail")
  },
  {
    path: "/repairDetail",
    name: "repairDetail",
    meta: {
      title: "报修"
    },
    component: () => import("@/view/feedback/feedbackList/repairDetail")
  },
  {
    path: "/complaint",
    name: "complaint",
    meta: {
      title: "投诉"
    },
    component: () => import("@/view/feedback/feedbackList/complaint")
  },
  {
    path: "/manageHome",
    name: "manageHome",
    meta: {
      title: "巡检服务"
    },
    component: () => import("@/view/home/<USER>")
  },
  {
    path: "/workerHome",
    name: "workerHome",
    meta: {
      title: "巡检服务"
    },
    component: () => import("@/view/home/<USER>")
  },
  {
    path: "/signIn",
    name: "signIn",
    meta: {
      title: "签到"
    },
    component: () => import("@/view/signIn/signIn.vue")
  },
  {
    path: "/signDetail",
    name: "signDetail",
    meta: {
      title: "签到详情"
    },
    component: () => import("@/view/signIn/signDetail.vue")
  },
  {
    path: "/inspectionRecord",
    name: "inspectionRecord",
    meta: {
      title: "巡检记录"
    },
    component: () => import("@/view/inspectionRecord/inspectionRecord.vue")
  },
  {
    path: "/taskdetail",
    name: "taskdetail",
    meta: {
      title: "任务详情",
      keepAlive: true
    },
    component: () => import("@/view/task/taskdetail.vue")
  },
  {
    path: "/tasks",
    name: "tasks",
    meta: {
      title: "巡检任务"
    },
    component: () => import("@/view/task/tasks.vue")
  },
  {
    path: "/checkDetail",
    name: "checkDetail",
    meta: {
      title: "巡检详情"
    },
    component: () => import("@/view/task/checkDetail.vue")
  },
  {
    path: "/signRecord",
    name: "signRecord",
    meta: {
      title: "签到记录"
    },
    component: () => import("@/view/signIn/signRecord.vue")
  },
  {
    path: "/signRecordDetail",
    name: "signRecordDetail",
    meta: {
      title: "签到详情"
    },
    component: () => import("@/view/signIn/signRecordDetail.vue")
  },
  {
    path: "/userSignDetail",
    name: "userSignDetail",
    meta: {
      title: "人员签到详情"
    },
    component: () => import("@/view/signIn/userSignDetail.vue")
  },
  {
    path: "/account",
    name: "account",
    meta: {
      title: "重点台账"
    },
    // component: () => import("@/view/account/account.vue")
    component: () => import("@/view/account/newAccount.vue")
  },
  {
    path: "/analysis",
    name: "analysis",
    meta: {
      title: "巡检任务分析"
    },
    component: () => import("@/view/analysis/analysis.vue")
  },
  {
    path: "/analysisPro",
    name: "analysisPro",
    meta: {
      title: "任务概览"
    },
    component: () => import("@/view/analysis/analysisPro.vue")
  },
  {
    path: "/inspectionContent",
    name: "inspectionContent",
    meta: {
      title: "巡检内容"
    },
    component: () => import("@/view/task/inspectionContent.vue")
  },
  {
    path: "/describe",
    name: "describe",
    meta: {
      title: "描述"
    },
    component: () => import("@/view/task/describe.vue")
  },
  {
    path: "/inspectionSelect",
    name: "inspectionSelect",
    meta: {
      title: "异常反馈", //选择任务书
      keepAlive: true
    },
    component: () => import("@/view/task/taskBookSelect.vue")
  },
  {
    path: "/recordDetail",
    name: "recordDetail",
    meta: {
      title: "巡检记录"
    },
    component: () => import("@/view/task/recordDetail.vue")
  },
  {
    path: "/dangeranalysis",
    name: "dangeranalysis",
    meta: {
      title: "隐患分析"
    },
    component: () => import("@/view/hiddendanger/dangeranalysis.vue")
  },
  {
    path: "/repository",
    name: "repository",
    meta: {
      title: "隐患知识库"
    },
    component: () => import("@/view/hiddendanger/repository.vue")
  },
  {
    path: "/specialExamination",
    name: "specialExamination",
    meta: {
      title: "专项检查"
    },
    component: () => import("@/view/specialExamination/specialExamination.vue")
  },
  {
    path: "/examinationTable",
    name: "examinationTable",
    meta: {
      title: "专项检查选择检查表"
    },
    component: () => import("@/view/specialExamination/examinationTable.vue")
  },
  {
    path: "/checkTableDetail",
    name: "checkTableDetail",
    meta: {
      title: "专项检查表"
    },
    component: () => import("@/view/specialExamination/checkTableDetail.vue")
  },
  {
    path: "/checkHistory",
    name: "checkHistory",
    meta: {
      title: "检查历史"
    },
    component: () => import("@/view/specialExamination/checkHistory.vue")
  },
  {
    path: "/riskStatistical",
    name: "riskStatistical",
    meta: {
      title: "风险统计"
    },
    component: () => import("@/view/analysis/riskStatistical.vue")
  },
  {
    path: "/scanCode",
    name: "scanCode",
    meta: {
      title: ""
    },
    component: () => import("@/view/home/<USER>")
  },
  {
    path: "/accountDetail",
    name: "accountDetail",
    meta: {
      title: "台账详情"
    },
    component: () => import("@/view/account/accountDetail.vue")
  },
  {
    path: "/YGJaccount",
    name: "YGJaccount",
    meta: {
      title: "重点台账"
    },
    // component: () => import("@/view/account/YGJaccount.vue")
    component: () => import("@/view/account/accountPro.vue")
  },
  {
    path: "/accountDistribution",
    name: "accountDistribution",
    meta: {
      title: "台账分布"
    },
    component: () => import("@/view/account/accountDistribution.vue")
  },
  ,
  {
    path: "/accidentManagement",
    name: "accidentManagement",
    meta: {
      title: "事故管理"
    },
    component: () => import("@/view/accidentManagement/accidentManagement.vue")
  },
  {
    path: "/jobManagement",
    name: "jobManagement",
    meta: {
      title: "作业管理"
    },
    component: () => import("@/view/jobManagement/jobManagement.vue")
  },
  {
    path: "/specialHiddenDanger",
    name: "SpecialHiddenDanger",
    meta: {
      title: "专项隐患",
      keepAlive: true
    },
    component: () => import("@/view/specialHiddenDanger/specialHiddenDanger.vue")
  },
  {
    path: "/specialDetails",
    name: "SpecialDetails",
    meta: {
      title: "详情"
    },
    component: () => import("@/view/specialHiddenDanger/details.vue")
  },
  {
    path: "/approve",
    name: "Approve",
    meta: {
      title: "专项审批"
    },
    component: () => import("@/view/specialHiddenDanger/approve.vue")
  },
  {
    path: "/riskAnalysis",
    name: "RiskAnalysis",
    meta: {
      title: "风险分析"
    },
    component: () => import("@/view/riskAnalysis/riskAnalysis.vue")
  },
  // 世纪坛特需，风险详情
  {
    path: "/riskDetail",
    name: "riskDetail",
    meta: {
      title: "风险详情"
    },
    component: () => import("@/view/riskDetail/riskDetail.vue")
  },
  {
    path: "/onlineExam",
    name: "onlineExam",
    meta: {
      title: "在线考试"
    },
    component: () => import("@/view/onlineExam/onlineExam.vue")
  },
  {
    path: "/replyQuestion",
    name: "replyQuestion",
    meta: {
      title: "在线考试"
    },
    component: () => import("@/view/onlineExam/replyQuestion.vue")
  },
  {
    path: "/examCompleteDetail",
    name: "examCompleteDetail",
    meta: {
      title: "在线考试"
    },
    component: () => import("@/view/onlineExam/examCompleteDetail.vue")
  },
  {
    path: "/equipmentList",
    name: "equipmentList",
    meta: {
      title: "设备列表"
    },
    component: () => import("@/view/account/equipmentList.vue")
  },
  {
    path: "/equipmentDetail",
    name: "equipmentDetail",
    meta: {
      title: "设备详情"
    },
    component: () => import("@/view/account/equipmentDetail.vue")
  },
  {
    path: "/hotRequest",
    name: "hotRequest",
    meta: {
      title: "动火申请"
    },
    component: () => import("@/view/hot/hotRequest.vue")
  },
  {
    path: "/firePermit",
    name: "firePermit",
    meta: {
      title: "动火许可证"
    },
    component: () => import("@/view/getAngry/firePermit.vue")
  },

  {
    path: "/reviewOperation",
    name: "reviewOperation",
    meta: {
      title: "作业检查"
    },
    component: () => import("@/view/getAngry/reviewOperation.vue")
  },
  {
    path: "/firePrinting",
    name: "firePrinting",
    meta: {
      title: "动火许可证"
    },
    component: () => import("@/view/getAngry/firePrinting.vue")
  },
  {
    path: "/endReport",
    name: "endReport",
    meta: {
      title: "动火结束上报"
    },
    component: () => import("@/view/getAngry/endReport.vue")
  },
  {
    path: "/getAngry",
    name: "getAngry",
    meta: {
      title: "动火结束上报"
    },
    component: () => import("@/view/getAngry/getAngry.vue")
  },
  {
    path: "/hotApprove",
    name: "hotApprove",
    meta: {
      title: "动火审批"
    },
    component: () => import("@/view/hot/hotApprove.vue")
  },
  {
    path: "/hotApproveDetail",
    name: "hotApproveDetail",
    meta: {
      title: "审批详情",
      keepAlive: true
    },
    component: () => import("@/view/hot/hotApproveDetail.vue")
  },
  {
    path: "/hotApproveForm",
    name: "hotApproveForm",
    meta: {
      title: "审批表单",
      keepAlive: true
    },
    component: () => import("@/view/hot/hotApproveForm.vue")
  },
  {
    path: "/hotApproveForm2",
    name: "hotApproveForm2",
    meta: {
      title: "保卫处审批表单",
      keepAlive: true
    },
    component: () => import("@/view/hot/hotApproveForm2.vue")
  },
  {
    path: "/hotApproveForm3",
    name: "hotApproveForm3",
    meta: {
      title: "监理单位审批表单",
      keepAlive: true
    },
    component: () => import("@/view/hot/hotApproveForm3.vue")
  },
  {
    path: "/constructionApplication",
    name: "constructionApplication",
    meta: {
      title: "建设单位申报"
    },
    component: () => import("@/view/hot/constructionApplication.vue")
  },
  {
    path: "/addConstructionUnits",
    name: "addConstructionUnits",
    meta: {
      title: "建设单位申报"
    },
    component: () => import("@/view/hot/addConstructionUnits.vue")
  },
  {
    path: "/constructionUnitsDetails",
    name: "constructionUnitsDetails",
    meta: {
      title: "建设单位申报详情"
    },
    component: () => import("@/view/hot/constructionUnitsDetails.vue")
  },
  {
    path: "/hotApplicationRecord",
    name: "hotApplicationRecord",
    meta: {
      title: "动火申请记录"
    },
    component: () => import("@/view/hot/hotApplicationRecord.vue")
  },
  {
    path: "/hotApplicationRecordDetails",
    name: "hotApplicationRecordDetails",
    meta: {
      title: "动火申请记录详情"
    },
    component: () => import("@/view/hot/hotApplicationRecordDetails.vue")
  },
  {
    path: "/safetyManagement",
    name: "safetyManagement",
    meta: {
      title: "安全管理填报"
    },
    component: () => import("@/view/safetyManagement/safetyManagement.vue")
  },
  {
    path: "/complete",
    name: "complete",
    meta: {
      title: "完成"
    },
    component: () => import("@/view/safetyManagement/complete.vue")
  },
  {
    path: "/safetyDetail",
    name: "safetyDetail",
    meta: {
      title: "填报详情"
    },
    component: () => import("@/view/safetyManagement/safetyDetail.vue")
  }
];
const getToken = () => localStorage.getItem("token");

const createRouter = () =>
  new Router({
    mode: "history",
    routes: constantRoutes,
    base: "/h5"
  });
const router = createRouter();
// 路由拦截
// router.beforeEach((to, from, next) => {
//   //  判断是否有token
//   const hasToken = getToken();
//   if (to.path == "/") {
//     next();
//   } else {
//     if (hasToken) {
//       try{
//         console.log(utils.isWechat(),'13213')
//       }catch(e){
//         console.log(e,'错误信息')
//       }
//       next();
//     } else {
//       /* 没有 token 的值 */
//       next(`/`);
//     }
//   }
// });

export default router;
