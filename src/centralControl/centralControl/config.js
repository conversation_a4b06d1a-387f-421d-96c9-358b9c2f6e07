import notice from '../notice/notice';//引入组件传值插件
import axios from '../axios/centralControl';//请求封装组件
import check from '../check/checkCentralControl';//校验
import utils from '../utils/utils';//工具函数
import config from '../config/config';//配置文件绑定
import bus from '../bus/bus'; //中央事件总线
const configArr = [
    {
        key: "notice",  //调用名称
        value: notice,  //绑定的入口函数
    },

    {
        key: "axios",  //调用名称
        value: axios,  //绑定的入口函数
    },

    {
        key: "check",  //调用名称
        value: check,  //绑定的入口函数
    },

    {
        key: "utils",  //调用名称
        value: utils,  //绑定的入口函数
    },

    {
        key: "config",  //调用名称
        value: config,  //绑定的入口函数
    },
    {
        key: "bus",  
        value: bus, 
    },
]
export default configArr;