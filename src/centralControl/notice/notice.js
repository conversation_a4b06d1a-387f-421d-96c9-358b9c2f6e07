class Obsever {
  //构造函数初始化 消息中心
  constructor() {
    this.arrBroth = [];
  }
  //注册监听者
  onBrother(name, objs) {
    var obj = {
      key: name,
      value: objs
    };
    this.arrBroth.push(obj);
  }
  //  通知改变
  emitBrother(name, info) {
    let ownObj = this.arrBroth;
    for (let item in ownObj) {
      if (ownObj[item].key === name) {
        ownObj[item]["value"](info);
      }
    }
  }
  //删除监听者
  offBrother(name) {
    this.arrBroth = this.arrBroth.filter(item => item.key != name);
  }
}
let obsever = new Obsever();
export default obsever;
