import UAParser from "ua-parser-js";
import wx from "weixin-js-sdk";
import moment from "moment";
const uaParer = new UAParser();
class Utils {
  isIos() {
    return uaParer.getOS().name === "iOS";
  }
  isAndroid() {
    return uaParer.getOS().name === "Android";
  }
  // 是否是微信端。
  isWechat() {
    return uaParer.getBrowser().name === "WeChat";
  }
  commonBack(that) {
    if (uaParer.getBrowser().name === "WeChat") {
      // 如果是在微信端。
      that.$router.go(-1);
    } else {
      api.closeWin();
    }
  }
  // 请求APP权限
  reqPermission(perms, callback) {
    api.requestPermission(
      {
        list: perms,
        code: 100001
      },
      function(ret, err) {
        if (callback) {
          callback(ret);
          return;
        }
      }
    );
  }
  openCustomDialog(pageParam, callBack) {
    api.openFrame({
      name: "logout",
      url: "widget://html/my/dialog.html",
      rect: {
        x: 0,
        y: 0,
        w: api.frameWidth,
        h: api.winHeight,
      },
      // 固定两个参数 title cont
      pageParam: pageParam,
      bgColor: "rgba(0, 0, 0, 0.6)",
      bounces: false,
      softInputMode: "resize",
    });
    api.addEventListener(
      {
        name: "logoutTure",
      },
      function (ret, err) {
        if (ret) {
          callBack();
        } else {
          console.log(JSON.stringify(err));
        }
      }
    );
  }
  // APP端是否有某一个权限
  hasPermission(perm) {
    var perms = new Array();
    perms.push(perm);
    var rets = api.hasPermission({
      list: perms
    });
    if (rets.length == 1) {
      return rets[0].granted;
    }
    return false;
  }
  // 调用APP扫码 （获取设备信息
  scanCode(val) {
    console.log('是否', val)
    var FNScanner = api.require("FNScanner");
    return new Promise(function(resolve, reject) {
      FNScanner.openScanner(
        {
          autorotation: false, //扫码页面是否自动旋转
          isAlbum: val == '2' //是否隐藏相册按钮 1：是 2：否
        },
        function(ret, err) {
          if (ret.eventType == "success") {
            // 调用后端接口 参数为扫描成功的参数
            var sacnInfoArr = ret.content.split(",");
            // 兼容风险二维码
            if (ret.content.indexOf('riskDisclosure?riskId') != -1) {
              const str = ret.content
              const riskDetail = str.slice(str.lastIndexOf('?') + 1, str.length).split('&')
              const scanCode = [
                'risk',
                riskDetail[1].substring(riskDetail[1].indexOf('=') + 1, riskDetail[1].length),
                riskDetail[2].substring(riskDetail[2].indexOf('=') + 1, riskDetail[2].length),
                riskDetail[0].substring(riskDetail[0].indexOf('=') + 1, riskDetail[0].length)
              ]
              sacnInfoArr = scanCode
            }
            // alert(ret.content);
            if (sacnInfoArr[0] == "iAAS") {
              resolve(sacnInfoArr, "IAAS");
            } else if (sacnInfoArr[0] == "ihsp") {
              resolve(sacnInfoArr);
            } else if (sacnInfoArr[0] == "icms") {
              resolve(sacnInfoArr);
            } else if (sacnInfoArr[0] == "risk") {
              resolve(sacnInfoArr);
            } else {
              reject();
              // YBS.apiToast("无效的二维码,请检查二维码");
              console.log("无效的二维码,请检查二维码");
            }
          }
          // else {
          //   reject();
          // }
        }
      );
    });
  }
  _sum(m, n) {
    return Math.floor(Math.random() * (m - n) + n);
  }
  // 录音上传
  uploadOssFrequency(aliyunOSS, file, upLoadObj, short) {
    var frequencyName = new Date().getTime() + "-" + this._sum(1, 1000);
    let objectKey = upLoadObj.dir + "/" + frequencyName + ".amr";
    let params = {
      file,
      bucketName: "sinomis",
      objectKey,
      uploadType: 1
    };
    return new Promise(function(resolve, reject) {
      aliyunOSS.upload(params, function(ret, err) {
        if (ret) {
          if (ret.oper == "complete") {
            var path = upLoadObj.dir + "/" + frequencyName + ".amr";
            resolve(path);
          }
        } else {
          reject(err);
        }
      });
    });
  }
  // 获取APP登录信息
  getAppUserInfo() {
    if (!this.isWechat()) {
      var userInfo = api.getPrefs({
        sync: true,
        key: "userInfo"
      });
      userInfo = JSON.parse(userInfo);
      const virtualToken = encodeURIComponent(userInfo.hospitalName);
      localStorage.setItem("token", virtualToken);
      localStorage.setItem("loginInfo", JSON.stringify(userInfo));
      console.log(localStorage.getItem("loginInfo"), "ooo");
    }
  }
  formatDateH(date) {
    return moment(date).format("YYYY年MM月DD日");
  }
  formatDate(date) {
    return moment(date).format("YYYY-MM-DD");
  }
  formatDateTime(date) {
    return moment(date).format("YYYY-MM-DD HH:mm:ss");
  }
  formatDateTimeOther(date) {
    return moment(date).format("YYYY-MM-DD HH:mm");
  }
  getUniqueName() {
    return new Date().valueOf();
  }
  // 数组去重
  unique(arr, flag) {
    var arrCopy = [];
    for (var a = 0; a < arr.length; a++) {
      if (arrCopy.length == 0) {
        arrCopy.push(arr[a]);
      } else {
        var cks = true;
        for (var b = 0; b < arrCopy.length; b++) {
          console.log(arrCopy, b);
          if (arr[a][flag] == arrCopy[b][flag]) {
            cks = false;
            break;
          }
        }
        if (cks) {
          arrCopy.push(arr[a]);
        }
      }
    }
    return arrCopy;
  }
  // 两位小数
  inputFixTwo() {
    let oninput =
      "if(!(/(?!^0\\d)^(\\d{1,8}(\\.\\d{0,2})?)?$/.test(value))&&value.indexOf('.')>0){value=value.slice(0,10)}if(!(/(?!^0\\d)^(\\d{1,8}(\\.\\d{0,2})?)?$/.test(value))&&value.indexOf('.')<0){value=value.slice(0,8)}if(value.includes('-')){value=0}if(isNaN(value)) { value = '' } if(value.indexOf('.')>0){value=value.slice(0,value.indexOf('.')+3)} if(value < 0){value = 0} ";
    return oninput;
  }
  // 退出公众号
  exitWx() {
    //安卓手机
    document.addEventListener(
      "WeixinJSBridgeReady",
      function() {
        WeixinJSBridge.call("closeWindow");
      },
      false
    );
    //ios手机
    WeixinJSBridge.call("closeWindow");
  }

  /**
   * 将json串转换成树形结构
   * @param a 树dataList
   * @param idStr 树节点id string 'id'
   * @param pidStr 树parentId string '树parentId'
   * @param chindrenStr children string 'children'
   * @returns {Array}
   */
  transData(a, idStr, pidStr, chindrenStr, extraParameter) {
    let r = [],
      hash = {},
      id = idStr,
      pid = pidStr,
      children = chindrenStr,
      i = 0,
      j = 0,
      len = a.length;
    for (; i < len; i++) {
      hash[a[i][id]] = a[i];
    }
    for (; j < len; j++) {
      let aVal = a[j],
        hashVP = hash[aVal[pid]];
      if (hashVP) {
        !hashVP[children] && (hashVP[children] = []);
        hashVP[children].push(aVal);
      } else {
        r.push(aVal);
      }
      //            查找已部署节点id集
      if (extraParameter && aVal.state == "1") extraParameter.push(aVal.id);
    }
    return r;
  }
  // 获取东八时区
  obtainEast8Date() {
    var timezone = 8; //目标时区时间，东八区   东时区正数 西市区负数
    var offset_GMT = new Date().getTimezoneOffset(); // 本地时间和格林威治的时间差，单位为分钟
    var nowDate = new Date().getTime(); // 本地时间距 1970 年 1 月 1 日午夜（GMT 时间）之间的毫秒数
    var targetDate = new Date(
      nowDate + offset_GMT * 60 * 1000 + timezone * 60 * 60 * 1000
    );
    return targetDate;
  }
  // 获取缓存信息
  getlocalStorage() {
    return JSON.parse(localStorage.getItem("loginInfo"));
  }
  isObject(exp) {
    return Object.prototype.toString.call(exp) == "[object Object]";
  }
}
let utils = new Utils();
export default utils;
