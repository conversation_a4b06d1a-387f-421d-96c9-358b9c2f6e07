import interfaceClass from './Interface.js'
import config from './config.js'
import dateConfig from './dataConfig';
class centralControl {
  commonFun(str,dateStr,data,fun,infoObj){
    interfaceClass[str](dateStr, {...infoObj.currency, ...data},fun,infoObj);
}
  // 走一站式（不走网关
  postContralHostUrlOne(str,data,fun){
    let infoObj = dateConfig.postContralHostUrl();
    this.commonFun(str,config.getStr("urlOne") + config.getStr(str),data,fun,infoObj);
  }

  // 
  postContralHostBase(str,data,fun){
    let infoObj = dateConfig.postContralBase();
    this.commonFun(str,config.getStr("urlBase") + config.getStr(str),data,fun,infoObj);
  }
  

}

let centralcontrol = new centralControl();
export default centralcontrol
