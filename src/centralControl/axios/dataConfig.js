class DateConfig {
  postContralHostUrl() {
    let currentLoginInfo = this.getLocalStorage();
    if (!currentLoginInfo) return;
    let postinfo = {
      currency: {
        unitCode: currentLoginInfo.unitCode, //上级单位编号
        hospitalCode: currentLoginInfo.hospitalCode, //医院编号编号
        sysForShort: "icms",
        designateDeptCode: currentLoginInfo.officeCode,
        designateDeptName: currentLoginInfo.officeName
      },
      allInfo: currentLoginInfo
    };
    if (currentLoginInfo.type == "1") {
      postinfo.currency.designateDeptCode = currentLoginInfo.workTeamId || ""; //院内传空
      postinfo.currency.designateDeptName = ""; //院内传空
    }
    return postinfo;
  }
  postContralBase() {
    let currentLoginInfo = this.getLocalStorage();
    if (!currentLoginInfo) return;
    let postinfo = {
      currency: {
        unitCode: currentLoginInfo.unitCode, //上级单位编号
        hospitalCode: currentLoginInfo.hospitalCode, //医院编号编号
        hospitalName: currentLoginInfo.hospitalName, //医院名称
        staffId: currentLoginInfo.id,
        sysForShort: "ipsm",
        platformFlag: 2,
        userName: currentLoginInfo.name,
        userId: currentLoginInfo.id,
        officeCode: currentLoginInfo.teamId || currentLoginInfo.officeId,
        officeId: currentLoginInfo.teamId || currentLoginInfo.officeId,
        // officeCode: currentLoginInfo.teamId || currentLoginInfo.officeCode,
        officeName: currentLoginInfo.teamNames || currentLoginInfo.officeName,
        companyCode: currentLoginInfo.companyCode,
        type: currentLoginInfo.type,
        phone: currentLoginInfo.phone,
      },
      allInfo: currentLoginInfo
    };
    if (currentLoginInfo.isHospital == 0) {
      // delete postinfo.currency.hospitalCode
      postinfo.currency.hospitalCode = api.pageParam.hospitalCode;
    }
    return postinfo;
  }
  // 获取localStorage信息
  getLocalStorage() {
    return JSON.parse(localStorage.getItem("loginInfo"));
  }
}
let dateConfig = new DateConfig();
export default dateConfig;
