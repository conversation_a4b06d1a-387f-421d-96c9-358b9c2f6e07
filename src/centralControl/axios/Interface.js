import axios from "./axios.js";
class Interface {
  /*                一站式                         */
  //巡检自修
  saveOrderFixByOwn(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //巡检自修 APP
  saveOrderFixByOwnAPP(url, data, fun) {
    axios.APPAjax(url, data, fun);
  }
  // 获取耗材目录 搜索
  getConsumables(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 巡检整改
  saveOrderAbarbeitung(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 巡检整改 APP
  saveOrderAbarbeitungAPP(url, data, fun) {
    axios.APPAjax(url, data, fun);
  }
  // 获取反馈列表
  getList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 网格查组织机构
  hospitalOfficeByGridId(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 巡检报修
  saveTaskByWeChat(url, data, fun) {
    data.createByTeamId = data.designateDeptCode;
    data.createByTeamName = data.designateDeptName;
    axios.postRequest(url, data, fun);
  }
  // 巡检报修 APP
  saveTaskByWeChatAPP(url, data, fun) {
    data.createByTeamId = data.designateDeptCode;
    data.createByTeamName = data.designateDeptName;
    axios.APPAjax(url, data, fun);
  }
  // 巡检整改详情
  getDetailsAbarbeitung(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 整改验收
  abarbeitungAcceptanceCheck(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 整改验收 APP
  abarbeitungAcceptanceCheckAPP(url, data, fun) {
    axios.APPAjax(url, data, fun);
  }
  // 获取巡检自修详情
  getDetailsFixByOwn(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 报修详情
  getTaskDetail(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 获取医院自定义的字典项接口
  getPersonnelDictionary(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 上传语音
  uploadVoiceToOSS(url, data, fun) {
    axios.APPAjax(url, data, fun);
  }
  // 上传图片
  uploadFileToOSS(url, data, fun) {
    var form = new FormData(); // FormData 对象
    form.append("file", data.file); // 文件对象
    form.append("unitCode", data.unitCode); //上级单位编码
    form.append("hospitalCode", data.hospitalCode); //医院编码

    axios.postFileRequest(url, form, fun);
  }

  /*                一站式                         */

  /*                基础                         */
  // 上传图片
  uploadImg(url, data, fun) {
    console.log(data);
    var form = new FormData(); // FormData 对象
    form.append("file", data.file); // 文件对象
    form.append("unitCode", data.unitCode); //上级单位编码
    form.append("hospitalCode", data.hospitalCode); //医院编码

    axios.postFileRequest(url, form, fun);
  }

  //
  getListNew(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 扫码后获取设备信息
  getGridInfo(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 修改任务点状态并提交描述
  submissionDescribe(url, data, fun) {
    axios.APPAjax(url, data, fun);
  }

  /*                基础                         */

  //扫码获取巡检内容
  getPerformTask(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // //获取计划类型
  // getPlanType(url, data, fun) {
  //   axios.postRequest(url, data, fun);
  // }
  //巡检任务列表
  getInspectionTaskList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //巡检任务详情
  getTaskPointDetail(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取任务点信息
  getTaskPoint(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取签到记录
  getSignRecord(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //签到
  signIn(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取任务点信息
  getSignTaskPoint(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取巡检记录
  getInspectionRecord(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取巡检详情
  getCheckDetail(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //巡检提交
  inspectionSubmit(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取反馈列表
  getFeedbackList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取签到记录
  getScanSignedRecord(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取任务点统计
  getTaskPointRecord(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取人员签到详情
  getScanSignedRecordDetail(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取统计分析列表
  getAnalysisList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取医院后勤统计分析列表
  getAnalysisProList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取统计分析图表1
  getAnalysisChart1(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取统计分析图表2
  getAnalysisChart2(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取医院后勤统计分析图表1
  getAnalysisProChart2(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取巡检内容
  getInspectionContentData(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取角色
  getRole(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //首页获取未完成任务数
  getTaskNum(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //获取首页菜单
  getMenuList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  //任务间隔时间
  executePlanTaskByTaskId(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 获取隐患数据
  getDangeranAlysis(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  // 获取巡检统计
  getTsaksTotal(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 获取计划类型
  getPlanType(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 根据计划类型获取任务
  getPlanTypeTaskList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 获取重点台账列表(医院端)
  getAccountList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 获取重点台账列表(医管端)
  getYGJAccountList(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  // 获取重点台账列表(医管端)new
  getAccountProStatistics(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getAccountStatistics(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  equipmentCertificateList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  equipmentDetail(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  equipmentCertificateDetail(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getCertificateStatistics(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  earlyWarningList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  // 获取台账分布
  getAccountDistribution(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  //获取事故责任部门
  selectDepartList(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  //获取事故审核人员

  getUseListByDeptId(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  //保存事故
  save(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  //事故列表
  accidentListData(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  //获取事故详情
  details(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  //删除事故
  delete(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  //审核
  audit(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  //作业列表
  assignmentListData(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  //作业列表
  getAuditTabulation(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  getTemplateList(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  saveAssignment(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  getAssignmentDetail(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  deleteAssignment(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  auditAssignment(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  getStatusNumber(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  getspecialHiddenDangerList(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  getSpecialHiddenDangerDetail(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  auditSpecialHiddenDanger(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }

  obtainPersonnelSignature(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  getRiskAnalysis(url, data, fun) {
    axios.postRequest(url, data, fun);
  }

  questionFindList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  questionPvqQuestionDetail(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  saveQuestionAnswer(url, data, fun) {
    axios.postRequest(url, data, fun);
  }

  getOnlineQuestionAnalyzeById(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getDictValueList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getApprovalSettingList(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  selectAreaList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  saveHot(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getOutSourcedCompanyList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getFireOperationTaskList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }

  getFinishFireApplication(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getFinishFireApplicationList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }

  getSelectFirePermitList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }

  getHotWorkPermit(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }

  getDownloadWord(url, data, fun) {
    axios.downloadFn(url, data, fun);
  }

  selectHotApprovalList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getFireApplication(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  approveFireApplication(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getListByBusinessId(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  saveConstructionUnit(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getUnitList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  constructionUnitDetails(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  editConstructionUnit(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getHotApplicationRecord(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getBuildUnitList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getSourcedCompanyList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  saveSafetyManagement(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getClassify(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getSafetyManagementList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getSafetyManagementDetail(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  getOutsourcedUserList(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  getProjectNameList(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  getHospitalArea(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  repositoryList(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  examinationList(url, data, fun) {
    axios.getRequest(url, data, fun);
  }
  checkList(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  areaList(url, data, fun) {
    axios.postRequest(url, data, fun);
  }
  checkTableTS(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  saveCheckTable(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  checkHistoryList(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  checkHistoryDetail(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  hospitalCheckList(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  checkConfirm(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  getIsPhotograph(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
  checkConfirmUpdata(url, data, fun) {
    axios.postRequestUTF8(url, data, fun);
  }
}
let interfaceClass = new Interface();
export default interfaceClass;
