const axios = require("axios").default;
const qs = require("qs");
class Request {
  //axios请求
  postRequest(strUrl, data, fun) {
    //qs模块转义数据格式
    data = qs.stringify(data);
    let config = {
      headers: {
        'Content-Type':'application/x-www-form-urlencoded'
      }
    };
    axios
      .post(strUrl, data, config)
      .then(result => {
        fun(result.data);
      })
      .catch(error => {
        throw new Error(error.message);
      });
  }
  //axios请求
  getRequest(strUrl, data, fun) {
    axios
      .get(strUrl, { params: data })
      .then(result => {
        fun(result.data);
      })
      .catch(error => {
        throw new Error(error.message);
      });
  }
  // 调用原生的接口请求，
  APPAjax(strUrl, data, fun){
    api.ajax({
      url: strUrl,
      method: 'post',
      data: {
          values: data,
          files: data.files
      }
    }, function(ret, err) {
      if (ret) {
        fun(ret);
      } else {
        throw new Error(err.message);
      }
    });

  }
  //axiosUTF8
  postRequestUTF8(strUrl, data, fun) {
    //qs模块转义数据格式
    let config = {
      headers: {
        "Content-Type": "application/json;charset=UTF-8"
        // 'Content-Type':'application/x-www-form-urlencoded'
      }
    };
    axios
      .post(strUrl, data, config)
      .then(result => {
        fun(result.data);
      })
      .catch(error => {
        throw new Error(error.message);
      });
  }
  //封装导出接口
  export(urlAnfData) {
    window.location.href = urlAnfData;
  }
  // 下载
  downloadFn(strUrl, data, fun) {
    fetch(`${strUrl}`, {
      method: "POST",
      headers: {
        credentials: "include",
        "Content-Type": "application/json;charset=utf-8"
      },
      body: JSON.stringify(data)
    })
      .then(res => {
        if (res.ok) {
          return res.blob();
        } else {
          console.log("error");
        }
      })
      .then(res => {
        fun(res);
      });
  }
  // 下载formData
  downloadFnFormData(strUrl, data, fun) {
    fetch(`${strUrl}`, {
      method: "POST",
      headers: {
        credentials: "include",
        "Content-Type": "multipart/form-data"
      },
      body: JSON.stringify(data)
    })
      .then(res => {
        if (res.ok) {
          return res.blob();
        } else {
          console.log("error");
        }
      })
      .then(res => {
        fun(res);
      });
  }
  // 上传文件
  postFileRequest(strUrl, data, fun) {
    let config = {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    };
    axios.post(strUrl, data, config).then(result => {
      fun(result.data);
    });
  }
}
let request = new Request();
export default request;
