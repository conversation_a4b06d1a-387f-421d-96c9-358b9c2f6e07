class Config {
  // urlOne = __PATH.ONESTOP; //一站式
  urlBase = __PATH.BASEURL; //基础

  getList = "cleaningWorkOrder/getList"; //获取反馈列表
  saveOrderFixByOwn = "cleaningWorkOrder/saveOrderFixByOwn"; //巡检自修
  saveOrderFixByOwnAPP = "cleaningWorkOrder/saveOrderFixByOwn"; //巡检自修
  getConsumables = "/deviceRepair/getConsumables"; // 获取耗材目录-条件搜索
  saveOrderAbarbeitung = "cleaningWorkOrder/saveOrderAbarbeitung"; //巡检整改
  saveOrderAbarbeitungAPP = "cleaningWorkOrder/saveOrderAbarbeitung"; //巡检整改
  hospitalOfficeByGridId = "sys/underlyingDataController/hospitalOfficeByGridId"; //网格查组织机构
  saveTaskByWeChat = "appOlgTaskManagement.do?saveTaskByWeChat"; //巡检报修
  saveTaskByWeChatAPP = "appOlgTaskManagement.do?saveTaskByWeChat"; //巡检报修
  getDetailsAbarbeitung = "cleaningWorkOrder/getDetailsAbarbeitung"; //巡检整改详情
  abarbeitungAcceptanceCheck = "cleaningWorkOrder/abarbeitungAcceptanceCheck"; //整改验收
  abarbeitungAcceptanceCheckAPP = "cleaningWorkOrder/abarbeitungAcceptanceCheck"; //整改验收
  getDetailsFixByOwn = "cleaningWorkOrder/getDetailsFixByOwn"; //获取巡检自修详情
  getTaskDetail = "appOlgTaskManagement.do?getTaskDetail"; //报修详情
  getPersonnelDictionary = "appOlgTaskManagement.do?getPersonnelDictionary"; //获取医院自定义的字典项接口
  uploadVoiceToOSS = "appOlgTaskManagement/uploadVoiceToOSS"; //上传语音至OSS

  getListNew = "feedBackRecord/getList"; // 获取反馈列表New
  // uploadImg= "/upload/image"; // 上传图片
  uploadImg = "/file/upload";
  uploadFileToOSS = "/appOlgTaskManagement/uploadFileToOSS"; // 上传图片
  getGridInfo = "/appCleanInterface/getGridInfo"; // 扫码后获取设备信息
  submissionDescribe = "/appWorkSubmission/submissionDescribe"; // 修改任务点状态并提交描述
  // 巡检2.0
  getPerformTask = "planTaskNewApiController/getPerformTask"; //巡检内容
  // getPlanType="sysDictData/findList" //计划类型
  getInspectionTaskList = "planTaskNewApiController/getInspectionTaskList"; //巡检任务列表
  getTaskPointDetail = "planTaskNewApiController/taskPointByTaskId"; //巡检任务详情
  executePlanTaskByTaskId = "planTaskNewApiController/executePlanTaskByTaskId"; //校验任务间隔时间
  getSignRecord = "signedRecord/listDataApp"; //获取签到记录
  signIn = "signedRecord/saveSignedRecord"; //签到
  getSignTaskPoint = "signedRecord/scanSignedRecord"; //获取任务点信息
  getInspectionRecord = "planTaskNewApiController/getPlanTaskNewRecordList"; //巡检记录列表
  getCheckDetail = "taskPointRelease/appDetail"; //获取巡检详情
  inspectionSubmit = "appWorkSubmission/submissionQualified"; //巡检提交
  getFeedbackList = "cleaningWorkOrder/getList"; //获取反馈列表
  getScanSignedRecord = "signedRecord/getScanSignedRecord"; //获取签到记录
  getTaskPointRecord = "signedRecord/getScanSignedRecordByTaskPoint"; //获取任务点统计
  getScanSignedRecordDetail = "signedRecord/getScanSignedRecordDetail"; //获取人员签到详情
  getAnalysisList = "maintainController/planPercentageTop5App"; //获取统计分析列表
  getAnalysisProList = "maintainController/workTeamTaskPercentage"; //获取医院后勤统计分析列表
  getAnalysisChart1 = "maintainController/taskPercentageByStatusApp"; //获取统计分析图表1
  getAnalysisChart2 = "maintainController/taskPercentageByTypeApp"; //获取统计分析图表2
  getAnalysisProChart2 = "maintainController/taskFinishPercent"; //获取医院后勤统计分析图表1
  getInspectionContentData = "planTaskNewApiController/taskPointByPointIdAndTaskId"; //获取巡检内容
  getRole = "roleManagement/getRoleByUserCode"; //获取角色
  getTaskNum = "planTaskNewApiController/getTotalInspectionTaskNumber"; //首页获取未完成任务数
  getMenuList = "userLoginController/userAuth"; //获取首页菜单
  getRiskType = "statisticsController/getRiskStatsByType"; //获取风险类型
  getRiskNumber = "statisticsController/getRiskStatsByLevel"; //获取风险数量
  getDangeranAlysis = "safetyManager/authorityAppQuestion/selectQuestionAnalysis"; // 获取隐患分析数据
  getTsaksTotal = "planTaskNew/tasklistData"; //获取巡检总览
  getPlanType = "sysDictData/listData"; // 获取计划类型
  getPlanTypeTaskList = "planTaskNew/listData"; // 根据计划类型获取任务
  getAccountList = "standingBook/listData"; // 获取重点台账列表(医院端)
  getYGJAccountList = "standingBook/managementEquipmentSafety"; // 获取重点台账列表(医管端)
  getAccountDistribution = "standingBook/hospitalStatistics"; // 获取台账分布

  selectDepartList = "safetyManager/common/selectDepartList"; //获取事故责任部门
  getUseListByDeptId = "safetyManager/common/getUseListByDeptId"; //获取审核人员
  save = "/accident/save"; //保存事故
  accidentListData = "/accident/accidentListData"; //事故列表
  details = "accident/details"; //获取事故详情
  delete = "accident/delete"; //删除事故
  audit = "accident/audit"; //审核
  assignmentListData = "assignment/assignmentListData"; //作业列表
  getAuditTabulation='assignment/getAuditTabulation'; //获取作业列表

  getTemplateList = "assignmentTemplate/findList"; //模版列表
  saveAssignment = "assignment/save"; //保存作业
  getAssignmentDetail = "assignment/details"; //获取作业详情
  deleteAssignment = "assignment/delete"; //删除作业
  // auditAssignment = "assignment/audit"; //审核作业
  auditAssignment = "assignment/auditAssignment"; //作业审核 新
  obtainPersonnelSignature='/assignment/obtainPersonnelSignature';//获取个人最新签名
  getStatusNumber = "assignment/getStatusNumber"; //作业状态统计

  getspecialHiddenDangerList = "specialRectification/listData"; //获取专项隐患列表
  getSpecialHiddenDangerDetail = "specialRectification/details"; //获取专项隐患详情
  auditSpecialHiddenDanger = "specialRectification/audit"; //专项隐患审核
  getRiskAnalysis = "riskManageController/getRiskAnalysis"; //获取风险分析
  // 考试
  questionFindList=("questionnair/wx_MyQuestionList"); // 考试列表
  questionPvqQuestionDetail = ("questionnair/getPaperQuestions");      // 问卷题目详情
  saveQuestionAnswer = ("questionnair/save");      // 问卷提交
  getOnlineQuestionAnalyzeById = ("questionnair/getOnlineQuestionAnalyzeById");          //答案分析

  getAccountProStatistics  = 'standingBook/getAppUnitList'; // 医管局重点台账统计
  getAccountStatistics = 'standingBook/getAppHospitalList'; // 医院端重点台账统计
  equipmentCertificateList = 'specialEquipmentCertificate/listData'; // 特种设备证书列表
  equipmentDetail = 'standingBook/getEquipmentSafety'; // 设备详情
  equipmentCertificateDetail = 'specialEquipmentCertificate/get'; // 特种设备证书
  earlyWarningList = 'standingBook/getAppFindList'; // 预警列表
  getCertificateStatistics = 'standingBook/certificateAppStatisticsList'; // 医管端证书统计
  getDictValueList = 'dictUtils/getDictList'; // 获取字典项
  getApprovalSettingList = 'approvalSetting/getApprovalSettingList' // 获取审批设置数据列表
  selectAreaList = 'fireApplication/selectAllAreaList' // 获取区域列表
  saveHot = 'fireApplication/saveOrUpdate' //动火申请提交
  getHospitalArea = 'fireApplication/selectHospitalAreaConfig' // 获取医院区域配置
  getOutSourcedCompanyList = 'fireApplication/getOutSourcedCompanyList' // 获取外包单位列表
  getSourcedCompanyList = 'fireApplication/getSourcedCompanyList' // 获取外包单位列表(全部)
  getBuildUnitList = 'constructionunitdeclare/findListData' // 获取建设单位列表
  getFireOperationTaskList='planTaskNewApiController/fireOperationTaskList' //app动火检查

  getFinishFireApplicationList='fireApplication/selectWaitFinishFireApplicationList'//查询动火结束上报列表
  getFinishFireApplication='fireApplication/finishFireApplication'//动火结束上报
  getSelectFirePermitList='fireApplication/selectFirePermitList'//查询动火许可证列表
  getHotWorkPermit='fireApplicationHospital/getHotWorkPermit'//获取动火许可证
  getDownloadWord='fireApplicationHospital/hotWorkPermitDownloadWord'//获取动火许可证

  selectHotApprovalList = 'fireApplication/selectHotApprovalList' // 获取动火审批列表
  getFireApplication = 'fireApplication/getFireApplication' // 获取动火申请详情
  approveFireApplication = 'fireApplication/approveFireApplication' // 动火审批
  getListByBusinessId = '/approvalOperationRecords/getListByBusinessId' // 获取审批记录
  saveConstructionUnit = 'constructionunitdeclare/save' // 建设单位申报
  getUnitList = 'constructionunitdeclare/listData' // 建设单位申报列表
  constructionUnitDetails = 'constructionunitdeclare/getParticulars' // 建设单位申报查看详情
  editConstructionUnit = 'constructionunitdeclare/update' // 建设单位修改
  getHotApplicationRecord = 'fireApplication/selectFireWorkList' // 动火申请记录列表
  saveSafetyManagement = '/safetyManagement/saveOrUpdate' // 保存安全管理填报
  getClassify = '/safetyManagementClassify/findList' // 安全管理分类列表
  getSafetyManagementList = '/safetyManagement/listData' // 安全管理填报记录
  getSafetyManagementDetail = '/safetyManagement/get' // 安全管理填报详情

  getOutsourcedUserList = '/controlTeamUser/getControlTeamUserList' // 外务公司人员
  getProjectNameList = '/sysDictData/findList' // 获取工程名称

  repositoryList = '/questionRepository/listData' // 隐患知识库列表
  examinationList = '/inspectionLedgerController/unitInspectionCountList' // 专项检查列表
  checkList = '/inspection/table/businessList' // 医管端检查表列表
  areaList = '/gasSafetyLedger/getHospitalCourtyard' // 查询医院下的院区
  checkTableDetail = '/inspection/table/detailsTree' // 检查表详情
  checkTableTS = '/inspectionLedgerController/detailsTree' // 根据检查表查询是否有暂存内容
  saveCheckTable = 'inspectionLedgerController/save' // 专项检查暂存、提交
  checkHistoryList = '/inspectionLedgerController/list'  // 专项检查历史、暂存历史
  checkHistoryDetail = '/inspectionLedgerController/get' // 获取检查详情
  hospitalCheckList = '/inspectionLedgerController/listData' // 医院端专项检查列表
  checkConfirm = '/inspectionLedgerController/confirmationStatus' // 专项检查确认
  checkConfirmUpdata = '/inspectionLedgerController/update' // 专项检查修改
  getIsPhotograph = '/hospital/configuration/hospitalConfiguration/getIsPhotograph' // 查询巡检任务扫码时是否可以选择相册内图片

  getStr(str) {
    return config[str];
  }
}
let config = new Config();
export default config;
