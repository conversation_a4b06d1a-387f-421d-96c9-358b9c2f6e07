const checkObject = {
    "title": { required: true, message: '请输入标题', trigger: 'blur' }, //标题非空校验
    "titleSection": { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }, //标题长度区间校验
    "content": { required: true, message: '请输入选项', trigger: 'blur' }, //内容项非空校验
    "contentSection": { min: 1, max: 200, message: '长度小于200个字符', trigger: 'blur' }, //标题长度区间校验
    "redStarLogo":{ required: true, message: " ", trigger: "blur" }, //红星标识
}
export default checkObject;