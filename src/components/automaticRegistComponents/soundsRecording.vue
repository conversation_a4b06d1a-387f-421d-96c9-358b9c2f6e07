<template>
  <div class="sounds-all">
    <div class="sounds-div"  @touchstart.prevent="startTalk($event)"
      @touchend.prevent="endTalk($event)"
       v-if="UploadVoice.hasVoice == 'unvoice'">
      <i class="iconfont">&#xeae6;</i>
      <span>点击录音</span>
    </div>

    <div class="sounds-playing" v-else>
      <div
        class="sounds-info"
        @click="handlePlayingClick()"
        v-if="UploadVoice.hasVoice == 'recorded'"
      >
        <img :src="icon_wify2" class="play-img" />
        <div>{{UploadVoice.duration}} ″</div>
      </div>
      <div class="sounds-info" v-if="UploadVoice.hasVoice == 'playRecording'">
        <img :src="icon_wify1" class="play-img" />
        <div>{{UploadVoice.duration}} ″</div>
      </div>
      <div class="sound-del" @click="handleDelVoiceClick()" v-if="UploadVoice.delBtn">清除</div>
    </div>

    <!-- 正在录音窗口 -->
    <van-popup v-model="UploadVoice.recoding">
      <div class="recording">
        <img :src="recording" alt="正在录音中" />
      </div>
    </van-popup>

    <!-- 底部按住说话 -->
    <div
      class="speak-all"
      v-if="isSpeaking"
      @touchstart.prevent="startTalk($event)"
      @touchend.prevent="endTalk($event)"
    >
      <div class="speak-text">{{speackStatus==1?"按住 说话":"松开 结束"}}</div>
      <div :class="['speak-img ', speackStatus==1?'speak-img-start':'speak-img-stop']"></div>
    </div>
  </div>
</template>
<script>
import icon_wify2 from "@/assets/images/icon_wify2.png";
import icon_wify1 from "@/assets/images/icon_wify1.gif";
import recording from "@/assets/images/recording.gif";
export default {
  name: "soundsRecording",
  data() {
    return {
      isSpeaking: false,
      speackStatus: "1",
      startY: "",

      UploadVoice: {
        START: 0,
        END: 0,
        path: "",
        base64Path: "",
        duration: "",
        recordTimer: null,
        voiceLocalId: "",
        voiceServerId: "",
        hasVoice: "unvoice",
        recoding: false,
        delBtn: false,
        alltime: 60, //总时长
        r_flag: true, //录音开关
        r_time: 0, //录音当前时长
        localId: ""
      },
      voiceArray: [],
      icon_wify2: icon_wify2,
      icon_wify1: icon_wify1,
      recording: recording,

      callerTape: "", //app语音
    };
  },
  mounted() {
  },
  methods: {
    //点击录音
    toSpeak() {
      this.speackStatus = 1;
      this.isSpeaking = true;
    },

    /* 语音开始 */
    startTalk(e) {
      this.APPStartTalk(e);
    },
    //APP语音开始
    APPStartTalk(e) {
      const YBS = this.utils;
      const UploadVoice = this.UploadVoice;
      if (!YBS.hasPermission("storage")) {
        let pageParam = {
          title: "存储权限使用说明",
          cont: "用于录音、语音识别等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["storage"], function(ret) {
            if (ret && ret.list.length > 0 && ret.list[0].granted) {
              if (!YBS.hasPermission("microphone")) {
                let pageParam = {
                  title: "麦克风权限使用说明",
                  cont: "用于录音、语音识别等场景"
                };
                YBS.openCustomDialog(pageParam, function () {
                  YBS.reqPermission(["microphone"], function(ret) {
                    console.log(JSON.stringify(ret));
                  });
                })
                return;
              }
            }
          });
        });
        return;
      }
      if (!YBS.hasPermission("microphone")) {
        let pageParam = {
          title: "麦克风权限使用说明",
          cont: "用于录音、语音识别等场景"
        };
        YBS.openCustomDialog(pageParam, function () {
          YBS.reqPermission(["microphone"], function(ret) {});
        })
        return;
      }
      UploadVoice.START = new Date().getTime();
      let _this = this;
      UploadVoice.recordTimer = setTimeout(function() {
        this.speackStatus = "2";
        api.startRecord();
        UploadVoice.recoding = true;
        _this.doRenderVoice(UploadVoice);
      }, 300);
      // setTimeout(function() {
      //   if(_this.UploadVoice.path)return;
      //   api.stopRecord(function(ret, err) {
      //     console.log('appstopRecord',ret)
      //     if (ret) {
      //       UploadVoice.path = ret.path;
      //       UploadVoice.duration = 60;
      //       UploadVoice.voiceLocalId = ret.localId;
      //       UploadVoice.recoding = false;
      //       UploadVoice.hasVoice = "recorded";
      //       UploadVoice.delBtn = true;
      //       _this.doRenderVoice(UploadVoice);
      //       _this.$emit("getRecordFile", ret.path);

      //       _this.plusReady(ret.path)
      //     }
      //   });
      // }, 60000);
      this.startY = e.targetTouches[0].pageY;
    },
   
    /* 语音结束 */
    endTalk(e) {
      this.APPEndTalk(e);
    },
    //APP语音结束
    APPEndTalk(e) {
      let endY = e.changedTouches[0].pageY;
      let _this = this;
      const UploadVoice = this.UploadVoice;
      UploadVoice.END = new Date().getTime();

      if (
        UploadVoice.END - UploadVoice.START < 300 ||
        _this.startY - endY >= 50
      ) {
        UploadVoice.END = 0;
        UploadVoice.START = 0;
        //小于300ms，不录音
        UploadVoice.recoding = false;
        _this.speackStatus = "1";
        _this.handleDelVoiceClick();
        clearTimeout(UploadVoice.recordTimer);

        if (_this.startY - endY >= 50) return _this.$toast.fail("已取消");
        if (UploadVoice.END - UploadVoice.START < 300)
          return _this.$toast.fail("说话时间太短");
      } else {
        api.stopRecord(function(ret, err) {
          console.log('语音',ret)
          if (ret) {
            UploadVoice.path = ret.path;
            UploadVoice.duration = ret.duration || 1;
            UploadVoice.voiceLocalId = ret.localId;
            UploadVoice.recoding = false;
            UploadVoice.hasVoice = "recorded";
            UploadVoice.delBtn = true;
            _this.doRenderVoice(UploadVoice);
            _this.$emit("getRecordFile", ret.path);

            _this.plusReady(ret.path)
          }
        });
      }
      this.isSpeaking = false;
    },
    // 上传语音至OSS
    plusReady(path){
      console.log('后端上传',path)
      // 后端上传
      this.axios.postContralHostUrlOne(
        'uploadVoiceToOSS',
        {
          files:{callerTape:path}
        },
        res => {
          const {code,message,data}=res;
          if (code == 200) {
            this.$emit("getRecord",data);
          }
        }
      );
      // 前端上传
      // let _self= this;
      // var aliyunOSS = api.require('aliyunOSS');
      // const upLoadObj={
      //   dir:'icms'
      // }
      // aliyunOSS.initOSSClient({
      //     endpoint: 'https://oss-cn-beijing.aliyuncs.com',
      //     accessKeyId:'LTAIeUfTgrfT5j7Y',
      //     accessKeySecret: '1IHBmAJCY8MkjTc4WTAi36HbJbGgRy',
      // }, function(ret, err) {
      //     if (ret) {
      //       _self.utils.uploadOssFrequency(aliyunOSS,path,upLoadObj).then(res=>{
      //         // _self.callerTape=res;
      //         _self.callerTape='https://sinomis.oss-cn-beijing.aliyuncs.com/'+res;
      //         _self.$emit("getRecord",_self.callerTape);
      //       })

      //     } else {

      //     }
      // });

    },
    handlePlayingClick() {
      const UploadVoice = this.UploadVoice;
      UploadVoice.hasVoice = "playRecording";
      UploadVoice.delBtn = false;
      this.doRenderVoice(UploadVoice);
      let _this = this;
      api.startPlay(
        {
          path: UploadVoice.path
        },
        function(ret) {
          UploadVoice.hasVoice = "recorded";
          UploadVoice.delBtn = true;
          _this.doRenderVoice(UploadVoice);
        }
      );
    },
    // 删除语音
    handleDelVoiceClick() {
      console.log('删除语音')
      const UploadVoice = this.UploadVoice;
      UploadVoice.START = 0;
      UploadVoice.voiceLocalId = "";
      UploadVoice.voiceServerId = "";
      UploadVoice.hasVoice = "unvoice";
      UploadVoice.delBtn = false;
      this.voiceArray = "";
      UploadVoice.path = "";
      this.doRenderVoice(UploadVoice);
    },
    doRenderVoice(obj) {
    }
  }
};
</script>
<style scoped lang="scss">
@import "../../assets/stylus/theme.scss";
.sounds-all {
  width: 100%;
  display: flex;
  justify-content: center;
  padding-bottom: 14px;
}
.right-icon-sty i {
  font-size: 22px;
}
.iconfontRight {
  color: #d8dee7;
}
.iconfontScan {
  color: $main-bgColor;
}
.sounds-div {
  color: #FFF;
  background: #38C7C4;
  padding: 8px 12px;
  border-radius: 8px;
  display: flex;
  width: 70%;
  align-items: center;
  justify-content: center;
  i {
    font-size: 20px;
    margin-top: 2px;
  }
  span {
    font-size: 15px;
    font-family: PingFang-SC-Medium, PingFang-SC;
    font-weight: 500;
  }
}

.speak-all {
  border-top: 1px solid #dddee6;
  background: $main-bgColor-gray;
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
  z-index: 1000;
  padding: 16px 0 40px 0;
  display: flex;
  flex-direction: column;
  // justify-content: center;
  align-items: center;
  .speak-text {
    font-size: 17px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #2d4a74;
    margin-bottom: 18px;
  }
  .speak-img {
    width: 98px;
    height: 60px;
    background-size: 100%;
  }
  .speak-img-start {
    background-image: url("../../assets/images/startRecord.png");
  }
  .speak-img-stop {
    background-image: url("../../assets/images/stopRecord.png");
  }
  > img {
    width: 98px;
  }
}
.speak-btn {
  color: $main-bgColor;
  border: 1px solid $main-bgColor;
  margin: 16px;
  padding: 12px 0;
  text-align: center;
  border-radius: 40px;
  font-size: 17px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  background: #fff;
}
.recording {
  width: 180px;
  height: 180px;
  img {
    width: 180px;
    height: 180px;
    -webkit-touch-callout: none;
  }
}
.sounds-info {
  width: 140px;
  height: 40px;
  border-radius: 40px;
  background: $main-bgColor;
  display: flex;
  align-items: center;
  color: #fff;
  img {
    width: 12.8px;
    margin: 0 14px;
  }
}
.sound-del {
  font-size: 17px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: $main-bgColor;
  line-height: 16px;
  margin: 0 10px;
}
.sounds-playing {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
/deep/ .img-upload {
  display: block;
}
/deep/ .img-upload .van-field__value {
  margin-top: 10px;
}
/deep/ .sounds-fields .van-cell__title {
  line-height: 40px;
}
/deep/ .sounds-fields .van-field__button {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
/deep/ .sounds-fields .van-field__button > div {
  width: 100%;
}
/deep/ .van-field__label {
  font-size: 15px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
}
/deep/ .van-button--info {
  color: #fff;
  background-color: $main-bgColor;
  border: 1px solid $main-bgColor;
  font-size: 17px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
}
/deep/ .van-button--round {
  border-radius: 4px;
}
/deep/ .van-field__control {
  font-size: 15px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #47515f;
}
/deep/ .van-cell {
  color: #333333;
  font-size: 15px;
}
/deep/ .van-overlay {
  background: none;
}
</style>
