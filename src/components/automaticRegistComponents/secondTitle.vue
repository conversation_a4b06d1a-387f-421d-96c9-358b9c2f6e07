<template>
  <div :class="['card-title', hasBorder?'card-title-border':'']">
    <div class="title-all">
      <span class="card-text">{{title}}</span>
      <!-- <span class="card-subTitle" v-if="subTitle" :style="{background:subTitle=='正常'?'#29BEBC':'#FF2836'}">{{subTitle}}</span>      -->
    </div>
    <div><slot name="right"></slot></div>
  </div>
</template>

<script>
export default {
  name: "SecondTitle",
  props: {
    title: {
      type: String,
      default: "",
    },
    hasBorder: {
      type: Boolean,
      default: false,
    },
    subTitle:{
      type: String,
      default: "",
    }
  },
  data() {
    return {};
  },
  methods: {
  }
};
</script>
<style scoped lang="scss">
@import "../../assets/stylus/theme.scss";
.card-title {
  background: #FFF;
  padding: 18px 16px;
  display: flex;
  justify-content: space-between;
  .title-all {
    display: flex;
  }
  .card-text{
    display: flex;
    align-items: center;
    font-size: 16px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: normal;
    color: #353535;
  }
  .card-text::before{
    display: inline-block;
    background: #38C7C4;
    height: 16px;
    width: 4px;
    margin-right: 8px;
    content: '';
  }
  .card-subTitle {
    padding: 3px 7px;
    border-radius: 4px;
    font-size: 13px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
    margin-left: 10px;
  }
}
.card-title-border {
  position: relative;
}
.card-title-border::after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  right: 0;
  bottom: 0;
  left: 0;
  border-bottom: 1px solid #ebedf0;
  transform: scaleY(.5);
}
</style>