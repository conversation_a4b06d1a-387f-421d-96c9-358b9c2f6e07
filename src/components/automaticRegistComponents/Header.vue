<template>
  <div class="header">
    <van-sticky :offset-top="0">
      <div class="header-topBar"></div>
      <div class="header-inner">
        <div class="left-icon" v-if="showLeftBtn" @click="handleBack">
          <i class="iconfont" :class="leftIcon" v-if="leftIcon"></i>
          <span style="font-size:16px">{{ leftText }}</span>
        </div>
        <div class="header-content">
          <span class="title">{{ title }}</span>
        </div>
        <div class="right-icon" v-if="showRightBtn" @click="handleMore">
          <i class="iconfont" :class="rightIcon" v-if="rightIcon"></i>
          <span v-else>{{ rightText }}</span>
        </div>
        <!-- <div class="left-icon" v-if="showLeftBtn" @click="handleBack">
          <i class="iconfont" :class="leftIcon" v-if="leftIcon"></i>
          <span style="font-size:16px">{{ leftText }}</span>
        </div>
        <div class="header-content" :style="{ backgroundColor: bgColor }">
          <span class="title">{{ title }}</span>
        </div>
        <div class="right-icon" v-if="showRightBtn" @click="handleMore">
          <slot></slot>
          <i class="iconfont" :class="rightIcon" v-if="rightIcon"></i>
          <span v-else>{{ rightText }}</span>
        </div> -->
      </div>
    </van-sticky>
  </div>
</template>

<script>
export default {
  name: "Header",
  props: {
    title: {
      type: String,
      default: ""
    },
    leftIcon: {
      type: String,
      default: "icon-back"
    },
    rightIcon: {
      type: String,
      default: ""
    },
    rightText: {
      type: String,
      default: ""
    },
    leftText: {
      type: String,
      default: "返回"
    },
    bgColor: {
      type: String,
      default: "#00CAC8"
    },
    showLeftBtn: {
      type: Boolean,
      default: () => true
    },
    showRightBtn: {
      type: Boolean,
      default: () => true
    }
  },
  data() {
    return {};
  },
  methods: {
    handleBack() {
      this.$emit("backFun");
    },
    handleMore() {
      this.$emit("moreFun");
    }
  }
};
</script>
<style scoped lang="scss">
.header {
  /* position: -webkit-sticky; */
  .header-topBar {
    height: 15px;
    background-color: rgb(0, 202, 200);
  }
  .header-inner {
    width: 100%;
    height: 45px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgb(0, 202, 200);
  }
  .header-content {
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    // position: relative;

    .title {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      // align-items: center;
      line-height: 45px;
      color: #fff;
      font-size: 16px;
    }
  }

  .left-icon {
    width: 20%;
    padding-left: 0.3rem;
    height: 2.5rem;
    // position: absolute;
    // top: 15px;
    // left: 5px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #fff;
    font-size: 0.6rem;
    line-height: 2.5rem;
    z-index: 999;
  }

  .right-icon {
    width: 20%;
    padding-right: 0.3rem;
    // width: 4rem;
    height: 2rem;
    // position: absolute;
    top: 0;
    right: 15px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: #fff;
    font-size: 0.6rem;
  }
}
</style>
