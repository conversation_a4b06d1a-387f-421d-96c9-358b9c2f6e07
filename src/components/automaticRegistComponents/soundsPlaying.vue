<template>
  <div>
    <div class='sounds-info' @click="handlePlayingClick()" v-if='during'>
      <img :src="icon_wify1" class="play-img" v-if="isPlaying">
      <img :src="icon_wify2" class="play-img" v-else>
      <div>{{during}} ″</div>
    </div>
    <div v-else></div>  


  </div>
</template>
<script>
import icon_wify2 from '@/assets/images/icon_wify2.png';
import icon_wify1 from '@/assets/images/icon_wify1.gif';
export default {
  name: 'soundsPlaying',
  props: {
    soundUrl: {
      type: String
    }
  },
  data () {
    return {
      icon_wify2:icon_wify2,
      icon_wify1:icon_wify1,
      amr:null,
      during:0,
      isPlaying:false,
      url:''
    }
  },
  watch:{
    soundUrl(val){
      this.init(val);
    }
  },
  mounted(){
    this.init(this.soundUrl);
  },
  methods: {
    init(soundUrl){
      if(soundUrl){
        if(this.soundUrl&&this.soundUrl.indexOf(__PATH.OSSURL)>=0){
          this.url=this.soundUrl;
        } else {
          this.url=`${__PATH.OSSURL}${this.soundUrl}`;
        }
        this.playInit();
      }
    },
    playInit(){
      var BenzAMRRecorder = require('benz-amr-recorder')
      this.amr = new BenzAMRRecorder();
      this.amr.initWithUrl(this.url).then(()=>{
        this.during = this.amr.getDuration();
        this.during = parseInt(this.during);

      })
     
    },
    handlePlayingClick(){
      var BenzAMRRecorder = require('benz-amr-recorder')
      this.amr = new BenzAMRRecorder();
      this.amr.initWithUrl(this.url).then(()=>{
        this.amr.play();
      })
      this.amr.onPlay(()=> {
        console.log('开始播放336');
        this.isPlaying=true;
      })
      this.amr.onEnded(()=> {
        console.log('播放完毕123');
        this.isPlaying=false;
      })
      this.amr.onPause(()=> {
        console.log('暂停');
      });
      this.amr.onResume(()=> {
        console.log('继续播放');
      });
      this.amr.onAutoEnded(()=> {
        console.log('播放自动结束');
        this.isPlaying=false;
      });
    }
  },
}
</script>
<style scoped lang="scss">
@import "../../assets/stylus/theme.scss";
.sounds-info {
  width: 160px;
  height: 40px;
  border-radius: 6px;
  background: $main-bgColor;
  display: flex;
  align-items: center;
  color: #FFF;
  img {
    width: 12.8px;
    margin: 0 14px;
  }
}
</style>
