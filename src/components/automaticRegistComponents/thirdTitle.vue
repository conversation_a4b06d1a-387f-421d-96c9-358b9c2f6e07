<template>
  <div class='card-title'>
    <span class="card-text">{{title}}</span>
    <div><slot></slot></div>
  </div>
</template>

<script>
export default {
  name: "ThirdTitle",
  props: {
    title: {
      type: String,
      default: ""
    },

  },
  data() {
    return {};
  },
  methods: {
  }
};
</script>
<style scoped lang="scss">
@import "../../assets/stylus/theme.scss";
.card-title {
  background: #FFF;
  padding: 4px 16px;
  font-size: 16px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 500;
  color: #5188FC;
  display: flex;
  justify-content: space-between;
  .card-text{
    display: flex;
    align-items: center;
  }
  .card-text::before{
    display: inline-block;
    background: #5188FC;
    height: 6px;
    border-radius: 50%;
    width: 6px;
    margin-right: 8px;
    content: '';
  }
}
</style>