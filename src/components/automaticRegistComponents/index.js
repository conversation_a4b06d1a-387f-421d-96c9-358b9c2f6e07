//使用require.context实现前端工程自动化 实现全局组件统一声明
const requestFile = require.context('./', true, /\.vue$/);
const newcomm = requestFile.keys().map(component => {
	let componentEntity = requestFile(component).default
	return componentEntity;
})
const install = (Vue) => {
	newcomm.forEach(item => {
		// 使用内置的组件名称 进行全局组件注册
		Vue.component(item.name, item)
	})
}
if (typeof window !== 'undefined' && window.Vue) {
	install(window.Vue);
}

export default {
	install,
	...newcomm //实现按需加载,需要单个导出组件
}
