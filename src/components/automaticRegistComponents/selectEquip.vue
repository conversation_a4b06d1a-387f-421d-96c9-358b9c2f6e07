<template>
  <div>
    <van-action-sheet
      v-if="getAssetsTypeListData[0]"
      v-model="equipShow"
      round
      :style="{ height: '60%' }"
      @click-overlay="cancleBtn"
    >
      <van-row class="prop-box">
        <van-col class="prop-text" span="8" @click="cancleBtn">取消</van-col>
        <van-col span="8" class="prop-title">设备分类选择</van-col>
        <van-col class="prop-text" span="8" style="text-align: right" @click="completeBtn">完成</van-col>
      </van-row>
      <div class="contentBox">
        <van-collapse
          v-model="activeNames"
          v-for="item in getAssetsTypeListData[0].list"
          :key="item.typeId"
        >
          <van-collapse-item v-if="item.list" :title="item.typeName" :name="item.name">
            <template>
              <div class="checkbox-box" v-for="child in item.list" :key="child.typeId">
                <van-checkbox
                  v-model="child.checked"
                  shape="square"
                  checked-color="#2D4A74"
                >{{child.typeName}}</van-checkbox>
              </div>
            </template>
          </van-collapse-item>
        </van-collapse>
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
export default {
  name: "selectEquip",
  props: {
    equipShow: {
      type: Boolean,
      default: false,
      equipmentTypeText: ""
    },
    pageOrgin: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      activeNames: [0], //默认展开面板的name，对应代码中的item.name
      getAssetsTypeListData: []
      // equipShow: false
    };
  },
  created: function() {
    // this.equipShow = this.poupShow;
    this.gainBenifitDate();
  },
  methods: {
    cancleBtn() {
      this.equipShow = false;
      this.$emit("cancleBtn", this.equipShow);
      this.gainBenifitDate();
    },
    completeBtn() {
      this.equipShow = false;
      console.log(this.getAssetsTypeListData);
      this.equipChangeData(this.getAssetsTypeListData[0].list);
      console.log(this.equipmentTypeIds);
      this.$emit(
        "completeBtn",
        this.equipShow,
        this.equipmentTypeIds,
        this.equipmentTypeText
      );
      // this.gainBenchDate();
    },
    equipChangeData(data) {
      this.equipmentTypeIds = "";
      this.equipmentTypeText = "";
      let that = this;
      data.map((item, index) => {
        item.list &&
          item.list.map((child, idx) => {
            that.equipmentTypeIds +=
              child.checked == true ? child.typeId + "," : "";
            that.equipmentTypeText +=
              child.checked == true ? child.typeName + "," : "";
          });
      });
      console.log(data);
      console.log(111);
      that.equipmentTypeIds = that.equipmentTypeIds.substring(
        0,
        that.equipmentTypeIds.lastIndexOf(",")
      );
      that.equipmentTypeText = that.equipmentTypeText.substring(
        0,
        that.equipmentTypeText.lastIndexOf(",")
      );
    },
    // 获取效益分析数据
    gainBenifitDate() {
      let params = {
        meterFlag: "",
        maintainFlag: "",
        antiepidemicFlag: ""
      };
      this.utils.imasGetDepartment(this, {}, "getAssetsTypeList", false); // 获取巡检计划数据
    },
    // 获取效益分析数据回调
    getAssetsTypeList(data) {
      this.getAssetsTypeListData = data;
      this.getAssetsTypeListData = this.utils.transData(
        data,
        "typeId",
        "typeParentId",
        "list"
      );
      this.exchangData(this.getAssetsTypeListData[0].list);
      this.equipChangeData(this.getAssetsTypeListData[0].list);
      console.log(this.getAssetsTypeListData);
    },
    // 对设备列表数据转换
    exchangData(data) {
      data.map((item, index) => {
        this.$set(item, "name", index);
        if (index == 0) {
          this.$set(item, "checked", true);
        } else {
          this.$set(item, "checked", false);
        }
        item.list &&
          item.list.map((child, inx) => {
            if (inx == 0 && index == 0) {
              if (this.pageOrgin != 1) {
                //不需要默认第一个选中
                this.$set(child, "checked", true);
              }
            } else {
              this.$set(child, "checked", false);
            }
          });
      });
    }
  }
};
</script>
<style scoped lang="scss">
@import "../../assets/stylus/theme.scss";
.contentBox {
  position: absolute;
  width: 100%;
  top: 44px;
  bottom: 0;
  overflow: auto;
}
.prop-box {
  padding: 10px 10px;
  .prop-title {
    font-size: 0.8rem;
    font-family: PingFang-SC-Medium, PingFang-SC;
    font-weight: 500;
    color: #333333;
  }
  .prop-text {
    font-size: 0.85rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #2d4a74;
  }
}
.checkbox-box {
  margin-bottom: 10px;
  margin-left: 1rem;
}
</style>