<template>
  <div class="hello">
    <!-- 
         title:头部标题 
         leftIcon:左侧icon, 
         leftText:左侧返回文字
         showLeftBtn:是否显示左侧返回按钮 
         rightText:右侧文本内容  
         showRightBtn:是否显示右侧按钮图标  
         bgColor:头部背景色 
         rightIcon:右侧图标（图标和文本只能显示一个）
    -->
    <Header 
      title="设备列表" 
      leftIcon="icon-back" 
      leftText="返回"
      bgColor="#2D4A74" 
      rightIcon="icon-gengduo" 
      @backFun="$router.go(-1)"
      @moreFun="handleMoreEvent">
    </Header>
  </div>
</template>

<script>
export default {
  name: 'HelloWorld',
  data () {
    return {}
  },
  methods: {
    handleMoreEvent(){
      alert('more')
    }
  },
}
</script>
<style scoped>
</style>
