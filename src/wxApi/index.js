import wx from "weixin-js-sdk";
import Vue from "vue";
const wxApi = {
  shareUrl: "",
  //获取微信签名
  getWxSign(callback) {
    let params = {
      url: encodeURIComponent(location.href.split("#")[0])
    };
    Vue.prototype.axios.postIaastServer("getSign", params, res => {
      const { code, data, message } = res;
      if (code == 200) {
        this.shareUrl = data.url;
        const { appId, nonceStr, timestamp, signature } = data;
        const WX_CONFIG = {
          debug: false,
          appId,
          timestamp,
          nonceStr,
          signature,
          jsApiList: [
            "hideMenuItems",
            "updateAppMessageShareData",
            "updateTimelineShareData"
          ] // 需要使用的JS接口列表
        };
        wx.config(WX_CONFIG);
        wx.ready(() => {
          // 展示分享菜单
          wx.hideMenuItems({
            menuList: [
              "menuItem:copyUrl",
              "menuItem:editTag",
              "menuItem:delete",
              "menuItem:originPage",
              "menuItem:readMode",
              "menuItem:openWithQQBrowser",
              "menuItem:openWithSafari",
              "menuItem:share:email",
              "menuItem:share:brand",
              "menuItem:share:qq",
              "menuItem:share:QZone"
            ] // 要隐藏的菜单项，只能隐藏“传播类”和“保护类”按钮
          });
          if (callback) callback();
        });
      }
    });
  },
  // 分享朋友
  shareAppMessage(option) {
    wx.updateAppMessageShareData({
      title: option.title, // 分享标题
      desc: option.desc, // 分享描述
      link: option.link, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
      imgUrl: option.imgUrl || "https://images.ktpis.com/app_logo_new_new.png", // 分享图标
      success() {
        if (option.success) option.success();
      }
    });
  },
  // 分享朋友圈
  shareTimeline(option) {
    wx.updateTimelineShareData({
      title: option.title, // 分享标题
      link: option.link, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
      imgUrl: option.imgUrl || "https://images.ktpis.com/app_logo_new_new.png", // 分享图标
      success() {
        if (option.success) option.success();
      }
    });
  }
};
export default wxApi;
