<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0, maximum-scale=1.0,user-scalable=no" />
    <title>巡检</title>
    
    <!-- <link rel="stylesheet" type="text/css"
    href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.7.2/animate.min.css" /> -->
    <!-- <style>
      body { /* IOS禁止微信调整字体大小 */
     -webkit-text-size-adjust: 100% !important;
     text-size-adjust: 100% !important;
     -moz-text-size-adjust: 100% !important;
     }
    </style> -->
    <style>
      body {
        background-color: #fff;
      }
      .loading-home {
        position: absolute;
        /* z-index: 10000; */
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        user-select: none;
      }

      .loading-home .loading {
        position: fixed;
        top: 45%;
        left: 50%;
        z-index: 99999;
        width: 35vw;
        height: 35vw;
        transform: translate(-50%, -50%);
      }
      .loading-home .loading img {
        width: 100%;
        height: 100%;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <div class="loading-home">
        <div class="loading">
          <% if (process.env.NODE_ENV == 'development') { %>
          <img src="/static/loading.gif" alt="" />
          <% } %> <% if (process.env.NODE_ENV == 'production') { %>
          <img src="./static/loading.gif" alt="" />
          <% } %>
        </div>
      </div>
    </div>
    <!-- built files will be auto injected -->
    <script>
      window.isDev = true;
      if (`<%= process.env.NODE_ENV %>` == "production") {
        localStorage.clear();
        window.isDev = false;
        console.log("生产环境");
      }
      var __PATH;
      //prod
      // var BASEURL = "https://ipsm.logimis.com/api/" // 生产
      // var FILEDOWNLOADURL = "https://sinomis.oss-cn-beijing.aliyuncs.com/"
      //dev
      // var BASEURL = "https://idpsserverdev.logimis.com/";
      // var FILEDOWNLOADURL = "https://sinomis.oss-cn-beijing.aliyuncs.com/";
      // test
      var BASEURL = "https://ipsmservertest.logimis.com/";
      var FILEDOWNLOADURL = "https://sinomis.oss-cn-beijing.aliyuncs.com/";
      __PATH = {
        BASEURL: BASEURL,
      };
      apiready = function () {
        console.log("apiready");
        // 开启Debug模式
        // 医帮手app 关于->版本号 连续点击7次开启
        api.getGlobalData({ key: 'debug' }) && loadDebugTools()

        var userInfo = api.getPrefs({
          sync: true,
          key: "userInfo",
        });
        var unifiedServer = api.getPrefs({
          sync: true,
          key: "unifiedServer",
        });
        var fullPath = api.getPrefs({
          sync: true,
          key: "fullPath",
        })
        userInfo.hospitalCode = api.pageParam.hospitalCode;
        userInfo.hospitalName = api.pageParam.hospitalName;
        userInfo = JSON.parse(userInfo);
        console.log("userInfo", userInfo);
        __PATH = {
          BASEURL: (fullPath ? fullPath : unifiedServer) + "/", //保洁
          DOWNLOAD_URL: userInfo.filePrefix,
        };
        console.log("__PATH", __PATH);
        if (!userInfo.filePrefix) {
          console.error('未获取到文件存储服务器地址')
        }
        if (userInfo.id) {
          const virtualToken = encodeURIComponent(userInfo.hospitalName);
          localStorage.setItem("token", virtualToken);
          localStorage.setItem("loginInfo", JSON.stringify(userInfo));
        }
      };
      function gethtmlfontsize() {
        let htmlwidth = document.documentElement.clientWidth || document.body.clientWidth;
        let htmlDom = document.getElementsByTagName("html")[0];
        //以375为基准做rem适配 即 1rem = 20px;
        htmlDom.style.fontSize = (htmlwidth / 375) * 20 + "px";
      }
      gethtmlfontsize();
      window.addEventListener("resize", gethtmlfontsize);

      // 加载debug 工具
      function loadDebugTools() {
        const scriptEl = document.createElement('script')
        const staticPath = !window.isDev ? './static' : '/static'
        scriptEl.src = staticPath + "/eruda.js"
        scriptEl.onload = function () {
          eruda.init();
        }
        document.body.append(scriptEl)
      }
    </script>
  </body>
</html>
