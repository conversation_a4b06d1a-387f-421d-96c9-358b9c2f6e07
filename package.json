{"name": "icms", "version": "1.0.0", "description": "A Vue.js project", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "test": "node build/test.js", "build": "node build/build.js"}, "dependencies": {"axios": "^0.21.1", "babel-plugin-import": "^1.13.3", "benz-amr-recorder": "^1.1.3", "css-loader": "^0.28.11", "echarts": "^4.9.0", "element-ui": "^2.15.0", "image-compressor": "^2.0.3", "image-compressor.js": "^1.1.4", "lib-flexible": "^0.3.2", "lotus-calendar": "^1.1.9", "mint-ui": "^2.2.13", "moment": "^2.29.3", "node-sass": "^5.0.0", "sass-loader": "^6.0.3", "scss": "^0.2.4", "scss-loader": "^0.0.1", "style-loader": "^2.0.0", "ua-parser-js": "^0.7.23", "v-charts": "^1.19.0", "vant": "^2.12.3", "vue": "^2.5.2", "vue-drag-ball": "0.0.1", "vue-hash-calendar": "^1.3.0", "vue-router": "^3.0.1", "vuex": "^3.6.2", "webpack-dev-server": "^2.9.6", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "cli": "^1.0.1", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.11", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "px2rem-loader": "^0.1.9", "rimraf": "^2.6.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.12.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}