'use strict'
const merge = require('webpack-merge')
const prodEnv = require('./prod.env')

module.exports = merge(prodEnv, {
  NODE_ENV: '"development"',
  ONESTOP:'"https://iomsdev.logimis.com/"',//一站式
  BASEURL:'"http://icmsapidev.logimis.com/"',//保洁
  OSSURL:'"https://sinomis.oss-cn-beijing.aliyuncs.com/"',//OSS

  // ONESTOP:'"https://iomstest.logimis.com/"',//一站式 测试
  // BASEURL:'"http://icmsapi.shx.logimis.com/"',//保洁  西安测试
  // OSSURL:'"https://sinomis.oss-cn-beijing.aliyuncs.com/"',//OSS
  // BASEURL:'"http://192.168.0.112:8990/"',//保洁  西安测试


  //prod
  // ONESTOP:'"https://ioms.logimis.com/"',//一站式
  // // BASEURL:'"https://icmsserver.logimis.com/"',//保洁
  // BASEURL:'"https://icis.logimis.com/api/"',//保洁
  // OSSURL:'"https://sinomis.oss-cn-beijing.aliyuncs.com/"',//OSS
})
